{"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": false, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "editor.codeActionsOnSave": {"quickfix.biome": "never", "source.organizeImports.biome": "never"}, "typescript.preferences.importModuleSpecifier": "non-relative", "typescript.tsdk": "node_modules/typescript/lib", "i18n-ally.localesPaths": ["packages/i18n/translations"], "i18n-ally.keystyle": "nested", "i18n-ally.enabledFrameworks": ["next-intl"], "i18n-ally.keysInUse": ["mail.organizationInvitation.headline"], "i18n-ally.tabStyle": "tab", "i18n-ally.sourceLanguage": "en"}