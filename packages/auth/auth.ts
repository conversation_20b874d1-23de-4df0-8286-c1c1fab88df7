/**
 * @fileoverview 这是应用程序的核心认证配置文件。
 * 它使用 `better-auth` 库来设置和管理用户认证、会话、组织、邀请以及与其他服务的集成。
 */

import { config } from "@repo/config";
import { db } from "@repo/database";
import type { Locale } from "@repo/i18n";
import { logger } from "@repo/logs";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import {
	admin,
	createAuthMiddleware,
	magicLink,
	openAPI,
	organization,
	username,
} from "better-auth/plugins";
import { passkey } from "better-auth/plugins/passkey";
import { parse as parseCookies } from "cookie";
import { updateSeatsInOrganizationSubscription } from "./lib/organization";
import { getUserByEmail } from "./lib/user";
import { invitationOnlyPlugin } from "./plugins/invitation-only";

/**
 * 从 HTTP 请求中提取用户的区域设置 (locale)。
 * 它会尝试从 cookie 中读取区域设置，如果找不到，则回退到默认区域设置。
 * @param {Request | undefined} request - 可选的 HTTP 请求对象。
 * @returns {Locale} - 用户的区域设置。
 */
const getLocaleFromRequest = (request?: Request) => {
	const cookies = parseCookies(request?.headers.get("cookie") ?? "");
	return (
		(cookies[config.i18n.localeCookieName] as Locale) ??
		config.i18n.defaultLocale
	);
};

/**
 * 获取应用程序的基础 URL。
 * @type {string}
 */
const appUrl = getBaseUrl();

/**
 * 初始化并配置 `better-auth` 实例。
 * 这是整个认证系统的核心。
 */
export const auth = betterAuth({
	/**
	 * 应用程序的基础 URL，用于生成重定向 URL 等。
	 * @type {string}
	 */
	baseURL: appUrl,
	/**
	 * 受信任的来源列表，用于 CORS 和其他安全检查。
	 * @type {string[]}
	 */
	trustedOrigins: [appUrl],
	/**
	 * 数据库适配器配置。
	 * 这里使用 Prisma 适配器连接到 PostgreSQL 数据库。
	 */
	database: prismaAdapter(db, {
		/**
		 * 指定数据库提供商类型。
		 * @type {string}
		 */
		provider: "postgresql",
	}),
	/**
	 * 会话管理配置。
	 */
	session: {
		/**
		 * 会话 cookie 的最大有效期（秒）。
		 * @type {number}
		 */
		expiresIn: config.auth.sessionCookieMaxAge,
		/**
		 * 会话"新鲜度"的年龄（秒）。如果设置为 0，则会话永远不会被视为"陈旧"。
		 * 这通常用于需要用户最近重新认证的操作。
		 * @type {number}
		 */
		freshAge: 0,
	},
	/**
	 * 账户相关配置。
	 */
	account: {
		/**
		 * 账户链接配置。允许用户将多个登录方式（例如 Google 和密码）链接到同一个账户。
		 */
		accountLinking: {
			/**
			 * 是否启用账户链接功能。
			 * @type {boolean}
			 */
			enabled: true,
			/**
			 * 可以自动链接的可信提供商列表。
			 * @type {string[]}
			 */
			trustedProviders: ["google", "github"],
		},
	},
	/**
	 * 钩子函数配置，允许在认证流程的特定点执行自定义逻辑。
	 */
	hooks: {
		/**
		 * 在认证 API 路由处理之后执行的中间件。
		 * @param {Function} createAuthMiddleware - 创建认证中间件的函数。
		 */
		after: createAuthMiddleware(async (ctx) => {
			// 如果是接受组织邀请的操作
			if (ctx.path.startsWith("/organization/accept-invitation")) {
				const { invitationId } = ctx.body;

				// 如果请求体中没有邀请 ID，则直接返回
				if (!invitationId) {
					return;
				}

				// 查找邀请记录
				const invitation = await db.invitation.findUnique({
					where: { id: invitationId },
				});

				// 如果邀请不存在，则直接返回
				if (!invitation) {
					return;
				}

				// 更新该组织订阅中的席位数
				await updateSeatsInOrganizationSubscription(
					invitation.organizationId,
				);
			// 如果是从组织中移除成员的操作
			} else if (ctx.path.startsWith("/organization/remove-member")) {
				const { organizationId } = ctx.body;

				// 如果请求体中没有组织 ID，则直接返回
				if (!organizationId) {
					return;
				}

				// 更新该组织订阅中的席位数
				await updateSeatsInOrganizationSubscription(organizationId);
			}
		}),
	},
	/**
	 * 用户模型相关配置。
	 */
	user: {
		/**
		 * 为用户模型添加额外的自定义字段。
		 */
		additionalFields: {
			/**
			 * 标记用户是否完成了引导流程。
			 */
			onboardingComplete: {
				type: "boolean",
				required: false,
			},
			/**
			 * 用户的首选区域设置。
			 */
			locale: {
				type: "string",
				required: false,
			},
		},
		/**
		 * 用户删除功能配置。
		 */
		deleteUser: {
			/**
			 * 是否启用用户删除功能。
			 * @type {boolean}
			 */
			enabled: true,
		},
		/**
		 * 用户更改邮箱功能配置。
		 */
		changeEmail: {
			/**
			 * 是否启用更改邮箱功能。
			 * @type {boolean}
			 */
			enabled: true,
			/**
			 * 发送邮箱更改验证邮件的函数。
			 * @param {object} params - 包含用户和验证 URL 的对象。
			 * @param {Request | undefined} request - 可选的 HTTP 请求对象。
			 */
			sendChangeEmailVerification: async (
				{ user: { email, name }, url },
				request,
			) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "emailVerification", // 使用邮件验证模板
					context: {
						url,
						name,
					},
					locale,
				});
			},
		},
	},
	/**
	 * 邮箱和密码认证方式配置。
	 */
	emailAndPassword: {
		/**
		 * 是否启用邮箱和密码认证。
		 * @type {boolean}
		 */
		enabled: true,
		/**
		 * 注册后是否自动登录。
		 * 如果禁用了公共注册 (config.auth.enableSignup 为 false)，意味着只能通过邀请注册，
		 * 此时可以安全地自动登录，因为邮箱已经通过邀请验证。
		 * 如果启用了公共注册，则不能自动登录，需要进行邮箱验证。
		 * @type {boolean}
		 */
		autoSignIn: !config.auth.enableSignup,
		/**
		 * 注册时是否需要邮箱验证。
		 * 仅当公共注册启用时才需要。
		 * @type {boolean}
		 */
		requireEmailVerification: config.auth.enableSignup,
		/**
		 * 发送密码重置邮件的函数。
		 * @param {object} params - 包含用户和重置 URL 的对象。
		 * @param {Request | undefined} request - 可选的 HTTP 请求对象。
		 */
		sendResetPassword: async ({ user, url }, request) => {
			const locale = getLocaleFromRequest(request);
			await sendEmail({
				to: user.email,
				templateId: "forgotPassword", // 使用忘记密码模板
				context: {
					url,
					name: user.name,
				},
				locale,
			});
		},
	},
	/**
	 * 邮箱验证流程配置。
	 */
	emailVerification: {
		/**
		 * 是否在注册时发送验证邮件。
		 * 仅当公共注册启用时才发送。
		 * @type {boolean}
		 */
		sendOnSignUp: config.auth.enableSignup,
		/**
		 * 发送邮箱验证邮件的函数。
		 * @param {object} params - 包含用户和验证 URL 的对象。
		 * @param {Request | undefined} request - 可选的 HTTP 请求对象。
		 */
		sendVerificationEmail: async (
			{ user: { email, name }, url },
			request,
		) => {
			const locale = getLocaleFromRequest(request);
			await sendEmail({
				to: email,
				templateId: "emailVerification", // 使用邮件验证模板
				context: {
					url,
					name,
				},
				locale,
			});
		},
	},
	/**
	 * 配置社交登录提供商。
	 */
	socialProviders: {
		/**
		 * Google 登录配置。
		 */
		google: {
			clientId: process.env.GOOGLE_CLIENT_ID as string,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
			scope: ["email", "profile"], // 请求获取邮箱和基本资料权限
		},
		/**
		 * GitHub 登录配置。
		 */
		github: {
			clientId: process.env.GITHUB_CLIENT_ID as string,
			clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
			scope: ["user:email"], // 请求获取用户邮箱权限
		},
	},
	/**
	 * 加载和配置 `better-auth` 插件。
	 * @type {Array<Function>}
	 */
	plugins: [
		/**
		 * 启用用户名/昵称功能。
		 */
		username(),
		/**
		 * 启用管理员角色和权限管理。
		 */
		admin(),
		/**
		 * 启用 Passkey (WebAuthn) 无密码登录。
		 */
		passkey(),
		/**
		 * 启用魔法链接登录。
		 */
		magicLink({
			/**
			 * 是否禁用通过魔法链接进行注册。
			 * @type {boolean}
			 */
			disableSignUp: false,
			/**
			 * 发送魔法链接邮件的函数。
			 * @param {object} params - 包含邮箱和登录 URL 的对象。
			 * @param {Request | undefined} request - 可选的 HTTP 请求对象。
			 */
			sendMagicLink: async ({ email, url }, request) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "magicLink", // 使用魔法链接模板
					context: {
						url,
					},
					locale,
				});
			},
		}),
		/**
		 * 启用组织和团队管理功能。
		 */
		organization({
			/**
			 * 发送组织邀请邮件的函数。
			 * @param {object} params - 包含邀请邮箱、邀请 ID 和组织信息的对象。
			 * @param {Request | undefined} request - 可选的 HTTP 请求对象。
			 */
			sendInvitationEmail: async (
				{ email, id, organization },
				request,
			) => {
				const locale = getLocaleFromRequest(request);
				// 检查被邀请的邮箱是否已存在用户
				const existingUser = await getUserByEmail(email);

				// 根据用户是否存在，决定邀请链接指向登录页还是注册页
				const url = new URL(
					existingUser ? "/auth/login" : "/auth/signup",
					getBaseUrl(),
				);

				// 在 URL 中附带邀请 ID 和邮箱，方便后续处理
				url.searchParams.set("invitationId", id);
				url.searchParams.set("email", email);

				// 发送邀请邮件
				await sendEmail({
					to: email,
					templateId: "organizationInvitation", // 使用组织邀请模板
					locale,
					context: {
						organizationName: organization.name,
						url: url.toString(),
					},
				});
			},
		}),
		/**
		 * 启用 OpenAPI 支持，用于生成 API 文档或客户端。
		 */
		openAPI(),
		/**
		 * 启用自定义的"仅邀请"插件，可能用于限制注册。
		 */
		invitationOnlyPlugin(),
	],
	/**
	 * API 错误处理配置。
	 */
	onAPIError: {
		/**
		 * 当 `better-auth` API 内部发生错误时的处理函数。
		 * @param {Error} error - 发生的错误对象。
		 * @param {object} ctx - 错误发生的上下文信息。
		 */
		onError(error, ctx) {
			// 使用日志记录器记录错误和上下文
			logger.error(error, { ctx });
		},
	},
});

/**
 * 重新导出组织相关的实用函数。
 */
export * from "./lib/organization";

/**
 * 导出 `better-auth` 推断出的 Session 类型。
 * @typedef {import('better-auth').InferSession<typeof auth>} Session
 */
export type Session = typeof auth.$Infer.Session;

/**
 * 导出 `better-auth` 推断出的 ActiveOrganization 类型。
 * 表示用户当前活跃（选中）的组织。
 * @typedef {import('better-auth').InferActiveOrganization<typeof auth>} ActiveOrganization
 */
export type ActiveOrganization = typeof auth.$Infer.ActiveOrganization;

/**
 * 导出 `better-auth` 推断出的 Organization 类型。
 * @typedef {import('better-auth').InferOrganization<typeof auth>} Organization
 */
export type Organization = typeof auth.$Infer.Organization;

/**
 * 导出 `better-auth` 推断出的组织成员角色类型。
 * @typedef {import('better-auth').InferMemberRole<typeof auth>} OrganizationMemberRole
 */
export type OrganizationMemberRole = typeof auth.$Infer.Member.role;

/**
 * 导出 `better-auth` 推断出的组织邀请状态类型。
 * @typedef {import('better-auth').InferInvitationStatus<typeof auth>} OrganizationInvitationStatus
 */
export type OrganizationInvitationStatus = typeof auth.$Infer.Invitation.status;

/**
 * 导出组织元数据类型。可以是一个记录或未定义。
 * @typedef {Record<string, unknown> | undefined} OrganizationMetadata
 */
export type OrganizationMetadata = Record<string, unknown> | undefined;
