import { z } from 'zod';
import { Prisma } from '@prisma/client';
import Decimal from 'decimal.js';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;

// DECIMAL
//------------------------------------------------------

export const DecimalJsLikeSchema: z.ZodType<Prisma.DecimalJsLike> = z.object({
  d: z.array(z.number()),
  e: z.number(),
  s: z.number(),
  toFixed: z.function(z.tuple([]), z.string()),
})

export const DECIMAL_STRING_REGEX = /^(?:-?Infinity|NaN|-?(?:0[bB][01]+(?:\.[01]+)?(?:[pP][-+]?\d+)?|0[oO][0-7]+(?:\.[0-7]+)?(?:[pP][-+]?\d+)?|0[xX][\da-fA-F]+(?:\.[\da-fA-F]+)?(?:[pP][-+]?\d+)?|(?:\d+|\d*\.\d+)(?:[eE][-+]?\d+)?))$/;

export const isValidDecimalInput =
  (v?: null | string | number | Prisma.DecimalJsLike): v is string | number | Prisma.DecimalJsLike => {
    if (v === undefined || v === null) return false;
    return (
      (typeof v === 'object' && 'd' in v && 'e' in v && 's' in v && 'toFixed' in v) ||
      (typeof v === 'string' && DECIMAL_STRING_REGEX.test(v)) ||
      typeof v === 'number'
    )
  };

/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const UserScalarFieldEnumSchema = z.enum(['id','name','email','emailVerified','image','createdAt','updatedAt','username','role','banned','banReason','banExpires','onboardingComplete','paymentsCustomerId','locale']);

export const SessionScalarFieldEnumSchema = z.enum(['id','expiresAt','ipAddress','userAgent','userId','impersonatedBy','activeOrganizationId','token','createdAt','updatedAt']);

export const AccountScalarFieldEnumSchema = z.enum(['id','accountId','providerId','userId','accessToken','refreshToken','idToken','expiresAt','password','accessTokenExpiresAt','refreshTokenExpiresAt','scope','createdAt','updatedAt']);

export const VerificationScalarFieldEnumSchema = z.enum(['id','identifier','value','expiresAt','createdAt','updatedAt']);

export const PasskeyScalarFieldEnumSchema = z.enum(['id','name','publicKey','userId','credentialID','counter','deviceType','backedUp','transports','createdAt']);

export const OrganizationScalarFieldEnumSchema = z.enum(['id','name','slug','logo','createdAt','metadata','paymentsCustomerId']);

export const MemberScalarFieldEnumSchema = z.enum(['id','organizationId','userId','role','createdAt']);

export const InvitationScalarFieldEnumSchema = z.enum(['id','organizationId','email','role','status','expiresAt','inviterId']);

export const PurchaseScalarFieldEnumSchema = z.enum(['id','organizationId','userId','type','customerId','subscriptionId','productId','status','createdAt','updatedAt']);

export const AiChatScalarFieldEnumSchema = z.enum(['id','organizationId','userId','title','messages','createdAt','updatedAt']);

export const ShareholderRegistryScalarFieldEnumSchema = z.enum(['id','fileName','recordCount','registerDate','companyCode','organizationId','userId','uploadedAt']);

export const CompanyInfoScalarFieldEnumSchema = z.enum(['id','registryId','organizationId','companyCode','companyName','registerDate','totalShareholders','totalInstitutions','largeShareholdersCount','largeSharesCount','totalShares','institutionShares','marginAccounts','marginShares','uploadedAt']);

export const ShareholderScalarFieldEnumSchema = z.enum(['id','shareholderId','registryId','organizationId','unifiedAccountNumber','securitiesAccountName','shareholderCategory','numberOfShares','lockedUpShares','shareholdingRatio','frozenShares','cashAccount','sharesInCashAccount','marginAccount','sharesInMarginAccount','contactAddress','contactNumber','zipCode','relatedPartyIndicator','clientCategory','marginCollateralAccountNumber','marginCollateralAccountName','natureOfShares','shareTradingCategory','rightsCategory','remarks','registerDate','uploadedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullableJsonNullValueInputSchema = z.enum(['DbNull','JsonNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.DbNull : value);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const NullsOrderSchema = z.enum(['first','last']);

export const JsonNullValueFilterSchema = z.enum(['DbNull','JsonNull','AnyNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.JsonNull : value === 'AnyNull' ? Prisma.AnyNull : value);

export const PurchaseTypeSchema = z.enum(['SUBSCRIPTION','ONE_TIME']);

export type PurchaseTypeType = `${z.infer<typeof PurchaseTypeSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  username: z.string().nullable(),
  role: z.string().nullable(),
  banned: z.boolean().nullable(),
  banReason: z.string().nullable(),
  banExpires: z.coerce.date().nullable(),
  onboardingComplete: z.boolean(),
  paymentsCustomerId: z.string().nullable(),
  locale: z.string().nullable(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  id: z.string(),
  expiresAt: z.coerce.date(),
  ipAddress: z.string().nullable(),
  userAgent: z.string().nullable(),
  userId: z.string(),
  impersonatedBy: z.string().nullable(),
  activeOrganizationId: z.string().nullable(),
  token: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Session = z.infer<typeof SessionSchema>

/////////////////////////////////////////
// ACCOUNT SCHEMA
/////////////////////////////////////////

export const AccountSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  providerId: z.string(),
  userId: z.string(),
  accessToken: z.string().nullable(),
  refreshToken: z.string().nullable(),
  idToken: z.string().nullable(),
  expiresAt: z.coerce.date().nullable(),
  password: z.string().nullable(),
  accessTokenExpiresAt: z.coerce.date().nullable(),
  refreshTokenExpiresAt: z.coerce.date().nullable(),
  scope: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Account = z.infer<typeof AccountSchema>

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const VerificationSchema = z.object({
  id: z.string(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date().nullable(),
})

export type Verification = z.infer<typeof VerificationSchema>

/////////////////////////////////////////
// PASSKEY SCHEMA
/////////////////////////////////////////

export const PasskeySchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  publicKey: z.string(),
  userId: z.string(),
  credentialID: z.string(),
  counter: z.number().int(),
  deviceType: z.string(),
  backedUp: z.boolean(),
  transports: z.string().nullable(),
  createdAt: z.coerce.date().nullable(),
})

export type Passkey = z.infer<typeof PasskeySchema>

/////////////////////////////////////////
// ORGANIZATION SCHEMA
/////////////////////////////////////////

export const OrganizationSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string().nullable(),
  logo: z.string().nullable(),
  createdAt: z.coerce.date(),
  metadata: z.string().nullable(),
  paymentsCustomerId: z.string().nullable(),
})

export type Organization = z.infer<typeof OrganizationSchema>

/////////////////////////////////////////
// MEMBER SCHEMA
/////////////////////////////////////////

export const MemberSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  role: z.string(),
  createdAt: z.coerce.date(),
})

export type Member = z.infer<typeof MemberSchema>

/////////////////////////////////////////
// INVITATION SCHEMA
/////////////////////////////////////////

export const InvitationSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  email: z.string(),
  role: z.string().nullable(),
  status: z.string(),
  expiresAt: z.coerce.date(),
  inviterId: z.string(),
})

export type Invitation = z.infer<typeof InvitationSchema>

/////////////////////////////////////////
// PURCHASE SCHEMA
/////////////////////////////////////////

export const PurchaseSchema = z.object({
  type: PurchaseTypeSchema,
  id: z.string().cuid(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  customerId: z.string(),
  subscriptionId: z.string().nullable(),
  productId: z.string(),
  status: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Purchase = z.infer<typeof PurchaseSchema>

/////////////////////////////////////////
// AI CHAT SCHEMA
/////////////////////////////////////////

export const AiChatSchema = z.object({
  id: z.string().cuid(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  title: z.string().nullable(),
  /**
   * [AIChatMessages]
   */
  messages: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type AiChat = z.infer<typeof AiChatSchema>

/////////////////////////////////////////
// SHAREHOLDER REGISTRY SCHEMA
/////////////////////////////////////////

export const ShareholderRegistrySchema = z.object({
  id: z.string().cuid(),
  fileName: z.string(),
  recordCount: z.number().int(),
  registerDate: z.coerce.date(),
  companyCode: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  uploadedAt: z.coerce.date(),
})

export type ShareholderRegistry = z.infer<typeof ShareholderRegistrySchema>

/////////////////////////////////////////
// COMPANY INFO SCHEMA
/////////////////////////////////////////

export const CompanyInfoSchema = z.object({
  id: z.string().cuid(),
  registryId: z.string(),
  organizationId: z.string(),
  companyCode: z.string(),
  companyName: z.string(),
  registerDate: z.coerce.date(),
  totalShareholders: z.number().int(),
  totalInstitutions: z.number().int(),
  largeShareholdersCount: z.number().int(),
  largeSharesCount: z.instanceof(Prisma.Decimal, { message: "Field 'largeSharesCount' must be a Decimal. Location: ['Models', 'CompanyInfo']"}),
  totalShares: z.instanceof(Prisma.Decimal, { message: "Field 'totalShares' must be a Decimal. Location: ['Models', 'CompanyInfo']"}),
  institutionShares: z.instanceof(Prisma.Decimal, { message: "Field 'institutionShares' must be a Decimal. Location: ['Models', 'CompanyInfo']"}),
  marginAccounts: z.number().int().nullable(),
  marginShares: z.instanceof(Prisma.Decimal, { message: "Field 'marginShares' must be a Decimal. Location: ['Models', 'CompanyInfo']"}).nullable(),
  uploadedAt: z.coerce.date(),
})

export type CompanyInfo = z.infer<typeof CompanyInfoSchema>

/////////////////////////////////////////
// SHAREHOLDER SCHEMA
/////////////////////////////////////////

export const ShareholderSchema = z.object({
  id: z.string().cuid(),
  shareholderId: z.string(),
  registryId: z.string(),
  organizationId: z.string(),
  unifiedAccountNumber: z.string(),
  securitiesAccountName: z.string(),
  shareholderCategory: z.string(),
  numberOfShares: z.instanceof(Prisma.Decimal, { message: "Field 'numberOfShares' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  lockedUpShares: z.instanceof(Prisma.Decimal, { message: "Field 'lockedUpShares' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  shareholdingRatio: z.instanceof(Prisma.Decimal, { message: "Field 'shareholdingRatio' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  frozenShares: z.instanceof(Prisma.Decimal, { message: "Field 'frozenShares' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  cashAccount: z.string().nullable(),
  sharesInCashAccount: z.instanceof(Prisma.Decimal, { message: "Field 'sharesInCashAccount' must be a Decimal. Location: ['Models', 'Shareholder']"}).nullable(),
  marginAccount: z.string().nullable(),
  sharesInMarginAccount: z.instanceof(Prisma.Decimal, { message: "Field 'sharesInMarginAccount' must be a Decimal. Location: ['Models', 'Shareholder']"}).nullable(),
  contactAddress: z.string().nullable(),
  contactNumber: z.string().nullable(),
  zipCode: z.string().nullable(),
  relatedPartyIndicator: z.string().nullable(),
  clientCategory: z.string().nullable(),
  marginCollateralAccountNumber: z.string().nullable(),
  marginCollateralAccountName: z.string().nullable(),
  natureOfShares: z.string().nullable(),
  shareTradingCategory: z.string().nullable(),
  rightsCategory: z.string().nullable(),
  remarks: z.string().nullable(),
  registerDate: z.coerce.date(),
  uploadedAt: z.coerce.date(),
})

export type Shareholder = z.infer<typeof ShareholderSchema>
