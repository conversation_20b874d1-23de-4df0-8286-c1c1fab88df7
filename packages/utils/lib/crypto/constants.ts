/**
 * 加密模块常量定义
 * 定义了加密算法、签名算法、请求有效期和环境变量名称
 */
export const CRYPTO_CONSTANTS = {
	// 加密算法常量
	ALGORITHM: "AES-CBC",

	// 签名算法常量
	SIGN_ALGORITHM: "HmacSHA256",

	// 请求有效期(毫秒)，默认60秒测试先增加时间，正式环境再减少
	REQUEST_EXPIRY:  60 * 1000, // 60秒

	// 环境变量名称
	ENV_API_KEY: "NEXT_PUBLIC_SHAREHOLDER_API_KEY",
	ENV_API_SECRET: "NEXT_PUBLIC_SHAREHOLDER_API_SECRET",
	ENV_API_IV: "NEXT_PUBLIC_SHAREHOLDER_API_IV",
	
	// 时间同步服务配置
	TIME_SYNC: {
		// 服务器时间API
		SERVER_TIME_API: "/api/server-time",
		// 刷新时间间隔（毫秒）
		REFRESH_INTERVAL: 60 * 60 * 1000, // 1小时刷新一次
	}
}; 