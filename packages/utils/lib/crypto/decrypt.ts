import CryptoJS from 'crypto-js';
import { CRYPTO_CONSTANTS } from './constants';

/**
 * 获取解密所需的密钥和IV
 * 在客户端和服务端兼容的方式获取环境变量
 */
function getDecryptionKeys() {
  // 判断是否在浏览器环境
  const isClient = typeof window !== 'undefined';
  
  let apiKey = '';
  let apiIv = '';
  
  if (isClient) {
    // 客户端直接从window.__ENV对象获取(如果可用)或从process.env获取
    apiKey = 
      (window as any).__ENV?.[CRYPTO_CONSTANTS.ENV_API_KEY] || 
      process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '';
      
    apiIv = 
      (window as any).__ENV?.[CRYPTO_CONSTANTS.ENV_API_IV] || 
      process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '';
      
    // 如果在客户端但未获取到密钥，尝试直接从全局process获取
    if (!apiKey) {
      apiKey = process.env.NEXT_PUBLIC_SHAREHOLDER_API_KEY || '';
      apiIv = process.env.NEXT_PUBLIC_SHAREHOLDER_API_IV || '';
    }
  } else {
    // 服务端直接从process.env获取
    apiKey = process.env[CRYPTO_CONSTANTS.ENV_API_KEY] || '';
    apiIv = process.env[CRYPTO_CONSTANTS.ENV_API_IV] || '';
  }
  
  return {
    key: CryptoJS.enc.Utf8.parse(apiKey),
    iv: CryptoJS.enc.Utf8.parse(apiIv)
  };
}

/**
 * 使用AES-CBC算法解密数据
 * @param encryptedData 加密后的字符串
 * @returns 解密后的字符串
 */
export function decryptData(encryptedData: string): string {
  // 获取解密密钥和IV
  const { key, iv } = getDecryptionKeys();

  // 使用AES-CBC模式解密数据
  const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}

/**
 * 解密并提取业务数据
 * @param encryptedData 加密后的字符串
 * @returns 解析后的业务数据对象和时间戳
 */
export function decryptRequestData<T>(encryptedData: string): { data: T; timestamp: number } {
  try {
    // 解密数据
    const decryptedString = decryptData(encryptedData);
    
    // 解析JSON
    const parsed = JSON.parse(decryptedString) as { data: T; timestamp: number };
    
    return {
      data: parsed.data,
      timestamp: parsed.timestamp
    };
  } catch (error) {
    throw new Error('数据解密或解析失败');
  }
}

/**
 * 解码Base64编码的时间数据
 * 用于解码服务器时间API返回的Base64编码时间数据
 * 支持服务器端和客户端环境
 * 
 * @param encodedData 不含填充等号的Base64编码字符串
 * @returns 解码后的时间数据对象 {timestamp: number, datetime: string}
 */
export function decodeTimeData(encodedData: string): { timestamp: number; datetime: string } {
  try {
    // 确保处理Base64填充问题
    // 计算需要的填充字符
    const padding = encodedData.length % 4;
    const paddedData = padding > 0 
      ? encodedData + '='.repeat(4 - padding) 
      : encodedData;
    
    // 解码Base64为字符串，根据环境选择不同的解码方法
    let jsonString: string;
    
    // 检查是否在Node.js环境
    if (typeof Buffer !== 'undefined') {
      // Node.js环境使用Buffer
      jsonString = Buffer.from(paddedData, 'base64').toString('utf-8');
    } else {
      // 浏览器环境使用atob
      // 注意：atob不能直接处理非ASCII字符，需要处理Unicode字符
      const raw = atob(paddedData);
      // 处理Unicode字符
      const rawLength = raw.length;
      const array = new Uint8Array(new ArrayBuffer(rawLength));
      
      for (let i = 0; i < rawLength; i++) {
        array[i] = raw.charCodeAt(i);
      }
      
      jsonString = new TextDecoder().decode(array);
    }
    
    // 解析为对象
    const data = JSON.parse(jsonString) as { timestamp: number; datetime: string };
    
    // 验证数据格式正确
    if (typeof data.timestamp !== 'number' || typeof data.datetime !== 'string') {
      throw new Error('无效的时间数据格式');
    }
    
    return data;
  } catch (error) {
    console.error('解码时间数据失败:', error);
    // 解码失败时返回当前时间作为后备方案
    const now = new Date();
    return {
      timestamp: now.getTime(),
      datetime: now.toISOString()
    };
  }
} 