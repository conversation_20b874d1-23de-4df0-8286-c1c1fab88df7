import CryptoJS from 'crypto-js';
import { CRYPTO_CONSTANTS } from './constants';

/**
 * 获取签名所需的密钥
 * 在客户端和服务端兼容的方式获取环境变量
 */
function getSignSecret() {
  // 判断是否在浏览器环境
  const isClient = typeof window !== 'undefined';
  
  let secret = '';
  
  if (isClient) {
    // 客户端从window.__ENV对象获取(如果可用)或从process.env获取
    secret = 
      (window as any).__ENV?.[CRYPTO_CONSTANTS.ENV_API_SECRET] || 
      process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] || '';
      
    // 如果在客户端但未获取到密钥，尝试直接从全局process获取
    if (!secret) {
      secret = process.env.NEXT_PUBLIC_SHAREHOLDER_API_SECRET || '';
    }
  } else {
    // 服务端直接从process.env获取
    secret = process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] || '';
  }
  
  return secret;
}

/**
 * 使用HMAC-SHA256算法生成内容签名
 * @param content 需要签名的内容
 * @returns 生成的签名字符串
 */
export function generateSign(content: string): string {
  // 获取签名密钥
  const secret = getSignSecret();

  // 使用HMAC-SHA256算法生成签名
  return CryptoJS.HmacSHA256(content, secret).toString();
}

/**
 * 验证内容签名是否正确
 * @param content 签名内容
 * @param sign 待验证的签名
 * @returns 签名是否有效
 */
export function verifySign(content: string, sign: string): boolean {
  // 重新计算签名并比较
  const calculatedSign = generateSign(content);
  return calculatedSign === sign;
}

/**
 * 检查时间戳是否在有效期内
 * 考虑时钟可能存在偏差，增加一个容差时间
 * @param timestamp 时间戳(毫秒)
 * @returns 时间戳是否有效
 */
export function isTimestampValid(timestamp: number): boolean {
  // 获取当前时间
  const currentTime = Date.now();
  
  // 计算时间差（允许未来一定时间范围内的时间戳，考虑到服务器和客户端可能有时钟差异）
  const diff = currentTime - timestamp;
  const clockToleranceMs = 3 * 60 * 1000; // 允许3分钟的时钟偏差
  
  // 检查时间差是否在允许范围内:
  // 1. 差值为负数但在容差范围内（未来时间但接近当前时间）
  // 2. 差值为正数但不超过有效期
  return (diff >= -clockToleranceMs) && (diff <= CRYPTO_CONSTANTS.REQUEST_EXPIRY);
}

/**
 * 为请求添加签名
 * @param content 已加密的内容
 * @returns 包含签名的请求对象
 */
export function createSignedRequest(content: string): { content: string; sign: string } {
  return {
    content,
    sign: generateSign(content)
  };
}