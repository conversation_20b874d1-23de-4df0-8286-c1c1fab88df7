import * as dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';
import { decryptData, CRYPTO_CONSTANTS } from './lib/crypto';

// 加载环境变量，优先从 .env.local 加载
const rootDir = path.resolve(__dirname, '../..');
const envLocalPath = path.join(rootDir, '.env.local');
const envPath = path.join(rootDir, '.env');

// 手动加载环境变量
if (fs.existsSync(envLocalPath)) {
  console.log(`加载环境变量从 ${envLocalPath}`);
  const envConfig = dotenv.parse(fs.readFileSync(envLocalPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
} else if (fs.existsSync(envPath)) {
  console.log(`加载环境变量从 ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('未找到环境变量文件');
}

// 输出当前环境变量信息（不显示完整内容，只显示是否存在）
console.log('环境变量检查:');
console.log(`- ${CRYPTO_CONSTANTS.ENV_API_KEY}: ${process.env[CRYPTO_CONSTANTS.ENV_API_KEY] ? '已设置' : '未设置'}`);
console.log(`- ${CRYPTO_CONSTANTS.ENV_API_SECRET}: ${process.env[CRYPTO_CONSTANTS.ENV_API_SECRET] ? '已设置' : '未设置'}`);
console.log(`- ${CRYPTO_CONSTANTS.ENV_API_IV}: ${process.env[CRYPTO_CONSTANTS.ENV_API_IV] ? '已设置' : '未设置'}`);

// 从命令行参数获取加密数据或使用内置的示例数据
let encryptedData: string;

// 检查是否有命令行参数
if (process.argv.length > 2) {
  // 从命令行参数获取文件路径或加密字符串
  const arg = process.argv[2];
  
  // 如果参数看起来像文件路径
  if (fs.existsSync(arg)) {
    console.log(`从文件读取加密数据: ${arg}`);
    encryptedData = fs.readFileSync(arg, 'utf8').trim();
  } else {
    console.log('使用命令行参数作为加密数据');
    encryptedData = arg;
  }
} else {
  // 使用内置的示例数据 - 把你的加密数据放在这里
  console.log('使用内置的示例数据');
  encryptedData =
		"knsxuc3Uzez12u8zgDfPbWhoru6hioA0KOwqX/AJcB5J12BTg2+6NVE153CeuO66FR07eZOoTDL1VLKSHhlSUO1WEccz2Ozp/VEPTFxUKqEsLQxnm2WZisqbJUqjQza2xqbNzItIGTd8r8bO3EFgSWv4rjz5Y9Ywdx/gL3UtUns3K0KhNgkQnXnSuftVkUTGRgWnsKdWlrlTNun7oy+b8D+8M1LUoAiUkx28O3GDGfwC3wbta68wOsUpGqmiULrtKkRnKlqjaCrsE4GAZnNTqx6+zgtooeGux6JjtOzwM3A5griwznj8DVdkgT8zrj5R/ZVJGjBpnVgsD8tL8o/3CiU0MYO8hmC8wtzlIWEhKdOgx4IZRrWMnwldjH1YIvUXH4KLLC2NLN4/xOEW0VxN8H2+vjhcb+KO5+oNImn97x3DKPx3n36GwJ0RBL5xrowWdy1JF4jvlj91KRgeejw/4jizJlDQ7RreC2n+b3Ohz2y67jANpf4/w1LqNGh+LXUoCLjI23ip5OVo73Be4QF7yYR+4qzl49CbF7FiOrIALVDLhISIE7MPdhnBYNIhYhrOQdDU0qz1QQ5B5XDjh027pXvYI2Z0wIh2ZbeSh3adwSbQfwX38wCLQtH4zmrYo2+k3v9IOtXBMPQxE2uj+XTu9CMnb8zeNKArw62mPRHhCShK/hhwlCnby228QouWvXVcgTTgsx2LD4d6GFp0UtwHV68qjGesqorjW4n3QBSKSCkjXDFZPU9+BEuCoDcxaB9n52jByyfsBSIzLFYTcyi1enyPuMdoubQDdq8bN+ewoOO+sEYy5v0VofPObjlKpjnwzWRUxge5NXTdk6ncJVhGHQ==";
}

try {
  console.log('\n开始解密数据...');
  
  // 解密数据
  const decryptedString = decryptData(encryptedData);
  
  // 解析JSON
  const parsed = JSON.parse(decryptedString);
  
  console.log('\n解密后的数据:');
  console.log(JSON.stringify(parsed, null, 2));

  // 打印时间戳信息（如果存在）
  if (parsed.timestamp) {
    console.log('\n请求时间戳:', new Date(parsed.timestamp).toISOString());
  }
} catch (error) {
  console.error('数据解密或解析失败:', error);
} 