import { operatorId, meetingApiClient } from "./config";

export const getUserDocs = {
	method: "GET",
	path: "/meetings/user/docs",
	handler: async (
		userId?: string,
		pageSize = 20,
		page = 1,
	) => {
		const targetUserId = userId || operatorId;
		
		try {
			const response = await meetingApiClient.get("/v1/docs", {
				params: {
					userid: targetUserId,
					page: page,
					page_size: pageSize,
				},
			});

			const responseData = response.data;
			if (responseData?.meeting_info_list) {
				responseData.meeting_info_list =
					responseData.meeting_info_list.filter(
						(meeting: any) =>
							meeting.doc_info_list &&
							meeting.doc_info_list.length > 0,
					);
				responseData.total_count =
					responseData.meeting_info_list.length;
				responseData.current_size =
					responseData.meeting_info_list.length;
				responseData.total_page =
					Math.ceil(responseData.total_count / pageSize) || 1;
			}

			return responseData;
		} catch (error: any) {
			console.log("Error fetching user docs:", error);
			throw error;
		}
	},
};

/* 修改块开始: 获取会议文档支持真实用户ID
 * 修改范围: getMeetingDocs handler支持接收用户ID参数
 * 对应需求: 使用真实用户ID而非固定operatorId获取会议文档
 * 恢复方法: 删除userId参数，恢复使用operatorId的原始实现
 */
export const getMeetingDocs = {
	method: "GET",
	path: "/meetings/docs/:meetingId",
	handler: async (meetingId: string, userId?: string) => {
		const targetUserId = userId || operatorId;
		
		try {
			const response = await meetingApiClient.get(`/v1/meetings/${meetingId}/docs`, {
				params: {
					userid: targetUserId,
					instanceid: "1",
				},
			});
			
			return response.data;
		} catch (error: any) {
			console.log("Error fetching meeting docs:", error);
			throw error;
		}
	},
};