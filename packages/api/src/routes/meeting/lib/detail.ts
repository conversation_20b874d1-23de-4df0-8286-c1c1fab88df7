import { meetingApiClient, operatorId } from "./config";

// 获取会议详情
export const getMeetingDetail = {
  method: "GET",
  path: "/meetings/details/:id",
  handler: async (meetingId: string) => {
    try {
      const response = await meetingApiClient.get(`/v1/meetings/${meetingId}`, {
        params: {
          operator_id: operatorId,
          operator_id_type: 1,
          instanceid: 1
        }
      });

      return response.data;
    } catch (error: any) {
      console.error("Error fetching meeting detail:", error);
      throw error;
    }
  },
};
