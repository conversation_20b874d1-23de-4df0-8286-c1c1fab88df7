import { operatorId, meetingApiClient } from "./config";
import type { CreateMeetingParams } from "./create";

// 修改会议参数接口，继承自创建会议参数，但所有字段都是可选的
export type UpdateMeetingParams = Partial<CreateMeetingParams> & {
  meeting_id: string; // 会议ID是必填的
};

/**
 * 修改会议API
 */
export const updateMeeting = {
  method: "PUT",
  path: "/meetings/update/:meetingId",
  handler: async (params: UpdateMeetingParams) => {
    try {
      // 构建请求体
      const requestBody = {
        userid: operatorId,
        instanceid: 1,
        meeting_id: params.meeting_id,
        // 可选参数，如果存在则添加到请求体
        ...(params.subject && { subject: params.subject }),
        ...(params.type !== undefined && { type: params.type }),
        ...(params.start_time && { start_time: params.start_time }),
        ...(params.end_time && { end_time: params.end_time }),
        ...(params.password && { password: params.password }),
        ...(params.hosts && { hosts: params.hosts }),
        ...(params.settings && { settings: params.settings }),
        ...(params.meeting_type !== undefined && { meeting_type: params.meeting_type }),
        ...(params.recurring_rule && { recurring_rule: params.recurring_rule }),
        ...(params.enable_live !== undefined && { enable_live: params.enable_live }),
        ...(params.live_config && { live_config: params.live_config }),
        ...(params.enable_doc_upload_permission !== undefined && { enable_doc_upload_permission: params.enable_doc_upload_permission }),
        ...(params.media_set_type !== undefined && { media_set_type: params.media_set_type }),
        ...(params.enable_interpreter !== undefined && { enable_interpreter: params.enable_interpreter }),
        ...(params.enable_enroll !== undefined && { enable_enroll: params.enable_enroll }),
        ...(params.enable_host_key !== undefined && { enable_host_key: params.enable_host_key }),
        ...(params.host_key && { host_key: params.host_key }),
        ...(params.sync_to_wework !== undefined && { sync_to_wework: params.sync_to_wework }),
        ...(params.time_zone && { time_zone: params.time_zone }),
        ...(params.location && { location: params.location }),
        ...(params.allow_enterprise_intranet_only !== undefined && { allow_enterprise_intranet_only: params.allow_enterprise_intranet_only }),
      };

      console.log("Update Meeting API request:", JSON.stringify(requestBody));
      const response = await meetingApiClient.put("/v1/meetings", requestBody);

      console.log("Meeting updated:", response.data);
      return response.data;
    } catch (error: any) {
      console.error("Error updating meeting:", error);
      throw error;
    }
  },
}; 