
import { operatorId, meetingApiClient } from "./config";

/* 修改块开始: 支持传入用户ID获取会议列表
 * 修改范围: getUpcomingMeetingList handler函数
 * 对应需求: 使用真实用户ID而非固定operatorId查询会议
 * 恢复方法: 删除userId参数，恢复使用固定operatorId
 */
export const getUpcomingMeetingList = {
	method: "GET",
	path: "/meetings/upcoming",
	handler: async (userId?: string) => {
		// 使用传入的用户ID，如果没有则使用默认的operatorId
		const targetUserId = userId || operatorId;
		
		try {
			const response = await meetingApiClient.get("/v1/meetings", {
				params: {
					userid: targetUserId,
					instanceid: "1",
				},
			});

			return response.data;
		} catch (error: any) {
			console.log("Error fetching upcoming meetings:", error);
			throw error;
		}
	},
};

export const getHistoryMeetingList = {
	method: "GET",
	path: "/meetings/history",
	handler: async (pageSize = 20, page = 1, userId?: string) => {
		const targetUserId = userId || operatorId;

		try {
			const response = await meetingApiClient.get(
				`/v1/history/meetings/${targetUserId}`,
				{
					params: {
						page_size: pageSize,
						page: page,
					},
				},
			);

			return response.data;
		} catch (error: any) {
			console.log("Error fetching history meetings:", error);
			throw error;
		}
	},
};
