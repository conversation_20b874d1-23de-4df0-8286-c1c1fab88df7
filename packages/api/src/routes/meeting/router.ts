import { Hono } from 'hono';
import {
  getMeetingDetail,
} from './lib/detail';
import { authMiddleware } from "../../middleware/auth";
import { getUpcomingMeetingList, getHistoryMeetingList } from './lib/meetinglist';
import { getUserDocs, getMeetingDocs } from './lib/docs';
import { getSingleMeetingRecordDetail, getMeetingSignInList } from './lib/recording';
import { cancelMeeting } from './lib/cancel';
import { createMeeting, type CreateMeetingParams } from './lib/create';
import { exportParticipants } from './lib/participants';
import { updateMeeting } from './lib/update';
import { getDepartments, createDepartment, getUser, createUser, updateUser, deleteUser } from './lib/account';
import { getInviteActivateLinks } from './lib/invite';
import { modifyMeeting, type ModifyMeetingParams } from './lib/modify';

// Router to expose the query function
export const meetingApiRouter = new Hono();

/* 修改块开始: 路由器支持用户ID参数
 * 修改范围: getUpcomingMeetingList路由处理
 * 对应需求: 从查询参数获取userId并传递给handler
 * 恢复方法: 删除userId相关代码，恢复原有调用方式
 */
meetingApiRouter
	.get(getUpcomingMeetingList.path,authMiddleware, async (c) => {
		try {
			// 从查询参数中获取用户ID
			const userId = c.req.query("userId");
			const data = await getUpcomingMeetingList.handler(userId);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error:", err);

			return c.json(
				{
					success: false,
					error:
						err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.post(createMeeting.path, authMiddleware, async (c) => {
		try {
			const body = await c.req.json();

			// 验证必填参数
			if (
				!body.subject ||
				body.type === undefined ||
				!body.start_time ||
				!body.end_time
			) {
				return c.json(
					{
						success: false,
						error: "缺少必填参数: subject, type, start_time, end_time",
					},
					400,
				);
			}

			/* 修改块开始: 路由器支持用户ID参数 - 创建会议
			 * 修改范围: createMeeting路由处理
			 * 对应需求: 从请求体获取userid并传递给handler
			 * 恢复方法: 删除userid相关代码，恢复原有调用方式
			 */
			const createParams: CreateMeetingParams = {
				subject: body.subject,
				type: body.type,
				start_time: body.start_time,
				end_time: body.end_time,
			};

			// 添加用户ID参数（如果提供）
			if (body.userid) {
				createParams.userid = body.userid;
			}

			// 添加可选参数
			if (body.password) {
				createParams.password = body.password;
			}
			if (body.hosts) {
				createParams.hosts = body.hosts;
			}
			if (body.settings) {
				createParams.settings = body.settings;
			}
			if (body.meeting_type !== undefined) {
				createParams.meeting_type = body.meeting_type;
			}
			if (body.recurring_rule) {
				createParams.recurring_rule = body.recurring_rule;
			}
			if (body.enable_live !== undefined) {
				createParams.enable_live = body.enable_live;
			}
			if (body.live_config) {
				createParams.live_config = body.live_config;
			}
			if (body.enable_doc_upload_permission !== undefined) {
				createParams.enable_doc_upload_permission =
					body.enable_doc_upload_permission;
			}
			if (body.media_set_type !== undefined) {
				createParams.media_set_type = body.media_set_type;
			}
			if (body.enable_interpreter !== undefined) {
				createParams.enable_interpreter = body.enable_interpreter;
			}
			if (body.enable_enroll !== undefined) {
				createParams.enable_enroll = body.enable_enroll;
			}
			if (body.enable_host_key !== undefined) {
				createParams.enable_host_key = body.enable_host_key;
			}
			if (body.host_key) {
				createParams.host_key = body.host_key;
			}
			if (body.sync_to_wework !== undefined) {
				createParams.sync_to_wework = body.sync_to_wework;
			}
			if (body.time_zone) {
				createParams.time_zone = body.time_zone;
			}
			if (body.location) {
				createParams.location = body.location;
			}
			if (body.allow_enterprise_intranet_only !== undefined) {
				createParams.allow_enterprise_intranet_only =
					body.allow_enterprise_intranet_only;
			}

			const data = await createMeeting.handler(createParams);
			return c.json({ success: true, data }, 201);
		} catch (error: any) {
			console.error("Error creating meeting", error);

			const errorResponse =
				error.response?.data || error.message || "未知错误";

			return c.json(
				{
					success: false,
					error: errorResponse,
				},
				500,
			);
		}
	})
	.get(getMeetingDetail.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("id");

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			const data = await getMeetingDetail.handler(meetingId);
			return c.json({ success: true, data }, 200);
		} catch (error: any) {
			console.error("Error fetching meeting detail", error);

			const errorResponse =
				error.response?.data || error.message || "未知错误";

			return c.json(
				{
					success: false,
					error: errorResponse,
				},
				500,
			);
		}
	})
	.get(getHistoryMeetingList.path,authMiddleware, async (c) => {
		try {
			const pageSize = Number.parseInt(
				c.req.query("pageSize") || "20",
				10,
			);
			const page = Number.parseInt(c.req.query("page") || "1", 10);
			const userId = c.req.query("userId");

			const data = await getHistoryMeetingList.handler(
				pageSize,
				page,
				userId,
			);

			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error:", err);
			return c.json(
				{
					success: false,
					error:
						err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	/* 修改块开始: 路由器支持用户ID参数 - 取消会议
	 * 修改范围: cancelMeeting路由处理
	 * 对应需求: 从请求体获取userId并传递给handler，增强错误处理
	 * 恢复方法: 删除userId相关代码，恢复原有调用方式
	 */
	.post(cancelMeeting.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("id");

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			// 从请求体获取取消原因代码和用户ID
			const body = await c.req.json().catch(() => ({}));
			const reasonCode = body?.reason_code || 1; // 默认为1
			const userId = body?.userId; // 获取用户ID

			const data = await cancelMeeting.handler(meetingId, reasonCode, userId);
			return c.json({ success: true, data }, 200);
		} catch (error: any) {
			console.error("Error canceling meeting", error);

			// 增强错误处理，返回更友好的错误信息
			let errorMessage = "取消会议失败";
			
			if (error.message) {
				errorMessage = error.message;
			} else if (error.response?.data) {
				const errorData = error.response.data;
				errorMessage = errorData?.error_info?.message || 
							  errorData?.message || 
							  errorMessage;
			}

			return c.json(
				{
					success: false,
					error: errorMessage,
				},
				500,
			);
		}
	})
	/* 修改块开始: 获取会议文档路由支持用户ID
	 * 修改范围: 从查询参数获取userId并传递给handler
	 * 对应需求: 支持前端传入真实用户ID获取会议文档
	 * 恢复方法: 删除userId相关代码，恢复原有的单参数调用
	 */
	.get(getMeetingDocs.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("meetingId");
			const userId = c.req.query("userId"); // 从查询参数获取用户ID

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			const data = await getMeetingDocs.handler(meetingId, userId);

			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (getMeetingDocs):", err);
			return c.json(
				{
					success: false,
					error:
						err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.get(getSingleMeetingRecordDetail.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("meetingId");
			const recordFileId = c.req.query("recordFileId"); // 可选

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			const data = await getSingleMeetingRecordDetail.handler(
				meetingId,
				recordFileId,
			);

			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (getSingleMeetingRecordDetail):", err);
			return c.json(
				{
					success: false,
					error:
						err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.get(getUserDocs.path, authMiddleware, async (c) => {
		try {
			const userId = c.req.query("userId");
			const pageSize = Number.parseInt(
				c.req.query("pageSize") || "20",
				10,
			);
			const page = Number.parseInt(c.req.query("page") || "1", 10);

			const data = await getUserDocs.handler(userId, pageSize, page);

			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (getUserDocs):", err);
			return c.json(
				{
					success: false,
					error:
						err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.post(exportParticipants.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("meetingId");

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			const data = await exportParticipants.handler(meetingId);
			return c.json(data);
		} catch (error: any) {
			console.error("Error exporting participants:", error);

			const errorResponse =
				error.response?.data || error.message || "未知错误";

			return c.json(
				{
					success: false,
					error: errorResponse,
				},
				500,
			);
		}
	})
	.put(updateMeeting.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("meetingId");
			const body = await c.req.json();

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			// 将会议ID添加到请求体中
			const updateParams = {
				...body,
				meeting_id: meetingId,
			};

			const data = await updateMeeting.handler(updateParams);
			return c.json({ success: true, data });
		} catch (error: any) {
			console.error("Error updating meeting:", error);

			const errorResponse =
				error.response?.data || error.message || "未知错误";

			return c.json(
				{
					success: false,
					error: errorResponse,
				},
				500,
			);
		}
	})
	.get(getDepartments.path, authMiddleware, async (c) => {
		try {
			const data = await getDepartments.handler();
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (getDepartments):", err);
			return c.json(
				{
					success: false,
					error: err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.post(createDepartment.path, authMiddleware, async (c) => {
		try {
			const body = await c.req.json();
			const { departmentName } = body;

			if (!departmentName) {
				return c.json(
					{
						success: false,
						error: "部门名称不能为空",
					},
					400,
				);
			}

			const data = await createDepartment.handler(departmentName);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (createDepartment):", err);
			return c.json(
				{
					success: false,
					error: err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.get(getUser.path, authMiddleware, async (c) => {
    try {
        const userId = c.req.query("userId");
        if (!userId) {
            return c.json(
                {
                    success: false,
                    error: "userId parameter is required",
                },
                400
            );
        }
        
        const size = Number.parseInt(c.req.query("size") || "100", 10);
        const exists = await getUser.handler(userId);
		
        return c.json({ success: true, exists });
    } catch (err: any) {
        console.error("Router error (getUser):", err);
        return c.json(
            {
                success: false,
                error: err?.response?.data || err?.message || "Unknown error",
            },
            500
        );
    }
})
	.post(createUser.path, authMiddleware, async (c) => {
		try {
			const body = await c.req.json();
			
			// 验证必填参数
			if (!body.username || !body.userid) {
				return c.json(
					{
						success: false,
						error: "缺少必填参数: username, userid",
					},
					400,
				);
			}

			// 验证手机号和邮箱至少有一个
			if (!body.phone && !body.email) {
				return c.json(
					{
						success: false,
						error: "手机号和邮箱至少需要提供一个",
					},
					400,
				);
			}

			const data = await createUser.handler(body);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (createUser):", err);
			return c.json(
				{
					success: false,
					error: err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	.post(getInviteActivateLinks.path, authMiddleware, async (c) => {
		try {
			const body = await c.req.json();
			const { useridList } = body;

			if (!useridList || !Array.isArray(useridList) || useridList.length === 0) {
				return c.json(
					{
						success: false,
						error: "用户ID列表不能为空",
					},
					400,
				);
			}

			const data = await getInviteActivateLinks.handler(useridList);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (getInviteActivateLinks):", err);
			return c.json(
				{
					success: false,
					error: err?.response?.data || err?.message || "Unknown error",
				},
				500,
			);
		}
	})
	/* 修改块开始: 新增更新用户路由
	 * 修改范围: 添加updateUser路由处理
	 * 对应需求: 支持更新用户部门信息
	 * 恢复方法: 删除此整个路由处理块
	 */
	.put(updateUser.path, authMiddleware, async (c) => {
		try {
			const userid = c.req.param("userid");
			const body = await c.req.json();
			const { department_list } = body;

			if (!userid) {
				return c.json(
					{
						success: false,
						error: "用户ID不能为空",
					},
					400,
				);
			}

			if (!department_list || !Array.isArray(department_list)) {
				return c.json(
					{
						success: false,
						error: "部门列表不能为空且必须是数组",
					},
					400,
				);
			}

			const data = await updateUser.handler(userid, department_list);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (updateUser):", err);
			
			// 增强错误处理，返回更友好的错误信息
			let errorMessage = "更新用户信息失败";
			
			if (err.message) {
				errorMessage = err.message;
			} else if (err.response?.data) {
				const errorData = err.response.data;
				errorMessage = errorData?.error_info?.message || 
							  errorData?.message || 
							  errorMessage;
			}

			return c.json(
				{
					success: false,
					error: errorMessage,
				},
				500,
			);
		}
	})
	.delete(deleteUser.path, authMiddleware, async (c) => {
		try {
			const userid = c.req.param("userid");
			if (!userid) {
				return c.json({ success: false, error: "用户ID不能为空" }, 400);
			}
			const data = await deleteUser.handler(userid);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (deleteUser):", err);
			return c.json({ success: false, error: err?.response?.data || err?.message || "Unknown error" }, 500);
		}
	})
	.get(getMeetingSignInList.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("meetingId");
			const userId = c.req.query("userId"); // 从查询参数获取用户ID

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			const data = await getMeetingSignInList.handler(meetingId, userId);
			return c.json({ success: true, data });
		} catch (err: any) {
			console.error("Router error (getMeetingSignInList):", err);
			
			// 增强错误处理，返回更友好的错误信息
			let errorMessage = "获取会议签到列表失败";
			
			if (err.message) {
				errorMessage = err.message;
			} else if (err.response?.data) {
				const errorData = err.response.data;
				errorMessage = errorData?.error_info?.message || 
							  errorData?.message || 
							  errorMessage;
			}

			return c.json(
				{
					success: false,
					error: errorMessage,
				},
				500,
			);
		}
	})
	/* 修改块开始: 新增修改会议路由
	 * 修改范围: 添加modifyMeeting的PUT路由处理
	 * 修改时间: 2025-06-12
	 * 修改人: LLM
	 * 关联需求: 支持PUT方式修改会议
	 * 恢复方法: 删除此整个路由处理块
	 */
	.put(modifyMeeting.path, authMiddleware, async (c) => {
		try {
			const meetingId = c.req.param("meetingId");
			const body = await c.req.json();

			if (!meetingId) {
				return c.json(
					{
						success: false,
						error: "会议ID不能为空",
					},
					400,
				);
			}

			// 验证必填参数
			if (
				!body.subject ||
				!body.start_time ||
				!body.end_time
			) {
				return c.json(
					{
						success: false,
						error: "缺少必填参数: subject, start_time, end_time",
					},
					400,
				);
			}

			const modifyParams: ModifyMeetingParams = {
				meeting_id: meetingId,
				subject: body.subject,
				type: body.type || 0, // 默认为0
				start_time: body.start_time,
				end_time: body.end_time,
			};

			// 添加用户ID参数（如果提供）
			if (body.userid) {
				modifyParams.userid = body.userid;
			}

			// 添加可选参数
			if (body.password) {
				modifyParams.password = body.password;
			}
			if (body.hosts) {
				modifyParams.hosts = body.hosts;
			}
			if (body.settings) {
				modifyParams.settings = body.settings;
			}
			if (body.meeting_type !== undefined) {
				modifyParams.meeting_type = body.meeting_type;
			}
			if (body.recurring_rule) {
				modifyParams.recurring_rule = body.recurring_rule;
			}
			if (body.enable_live !== undefined) {
				modifyParams.enable_live = body.enable_live;
			}
			if (body.live_config) {
				modifyParams.live_config = body.live_config;
			}
			if (body.enable_doc_upload_permission !== undefined) {
				modifyParams.enable_doc_upload_permission =
					body.enable_doc_upload_permission;
			}
			if (body.media_set_type !== undefined) {
				modifyParams.media_set_type = body.media_set_type;
			}
			if (body.enable_interpreter !== undefined) {
				modifyParams.enable_interpreter = body.enable_interpreter;
			}
			if (body.enable_enroll !== undefined) {
				modifyParams.enable_enroll = body.enable_enroll;
			}
			if (body.enable_host_key !== undefined) {
				modifyParams.enable_host_key = body.enable_host_key;
			}
			if (body.host_key) {
				modifyParams.host_key = body.host_key;
			}
			if (body.sync_to_wework !== undefined) {
				modifyParams.sync_to_wework = body.sync_to_wework;
			}
			if (body.time_zone) {
				modifyParams.time_zone = body.time_zone;
			}
			if (body.location) {
				modifyParams.location = body.location;
			}
			if (body.allow_enterprise_intranet_only !== undefined) {
				modifyParams.allow_enterprise_intranet_only =
					body.allow_enterprise_intranet_only;
			}

			const data = await modifyMeeting.handler(modifyParams);
			return c.json({ success: true, data }, 200);
		} catch (error: any) {
			console.error("Error modifying meeting", error);

			// 增强错误处理，返回更友好的错误信息
			let errorMessage = "修改会议失败";
			
			if (error.message) {
				errorMessage = error.message;
			} else if (error.response?.data) {
				const errorData = error.response.data;
				errorMessage = errorData?.error_info?.message || 
							  errorData?.message || 
							  errorMessage;
			}

			return c.json(
				{
					success: false,
					error: errorMessage,
				},
				500,
			);
		}
	})
	/* 修改块结束: 新增修改会议路由
	 * 修改时间: 2025-06-12
	 */
