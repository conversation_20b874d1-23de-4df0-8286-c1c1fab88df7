import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import type { ListRegistryRequest, ListRegistryResponse, CompanyDetailInfo } from "./types";
import { ListRegistrySchema } from "./lib/validators";
import { HTTPException } from "hono/http-exception";
import { Prisma } from "@prisma/client";

/**
 * 股东名册列表查询路由
 * 
 * 功能：获取特定组织下的股东名册列表，支持分页和公司代码筛选
 * 路径：/list
 * 方法：POST
 * 中间件：
 * - authMiddleware: 验证用户身份和权限
 * - shareholderCryptoMiddleware: 处理请求和响应的加解密
 * 
 * 请求参数：
 * - organizationId: 组织ID(必填)
 * - page: 当前页码(默认1)
 * - limit: 每页记录数(默认10)
 * - companyCode: 公司代码(可选，用于筛选)
 * 
 * 响应数据：
 * - registries: 股东名册列表，包含基本信息和公司详情
 * - pagination: 分页信息
 */
export const listRouter = new Hono().post(
  "/list",
  authMiddleware,                 // 验证用户身份和权限
  shareholderCryptoMiddleware(),  // 处理请求和响应的加解密
  async (c) => {
    try {
      // 获取解密后的请求数据
      const requestData = c.get("requestData") as ListRegistryRequest;
      
      // 验证请求参数格式
      const validationResult = ListRegistrySchema.safeParse(requestData);
      if (!validationResult.success) {
        throw new HTTPException(400, { 
          message: "请求参数无效", 
          cause: validationResult.error 
        });
      }
      
      // 解构请求参数，设置默认值
      const { organizationId, page = 1, limit = 10, companyCode } = validationResult.data;
      
      /**
       * 构建数据库查询条件
       * - organizationId: 确保只返回指定组织的记录
       * - companyCode: 可选，按公司代码筛选记录
       */
      const where = {
        organizationId,
        ...(companyCode ? { companyCode } : {}),
      };
      
      /**
       * 查询总记录数并计算总页数
       * - 使用 count 方法获取符合条件的记录总数
       * - 根据总记录数和每页条数计算总页数
       */
      const total = await db.shareholderRegistry.count({ where });
      const totalPages = Math.ceil(total / limit);

      // 如果没有记录，返回空列表响应
      if (total === 0) {
        c.set("response", {
          code: 200,
          message: "获取股东名册列表成功",
          data: {
            registries: [],
            pagination: {
              total: 0,
              page,
              limit,
              totalPages: 0
            }
          }
        });
        return;
      }
      
      /**
       * 查询股东名册列表及关联的公司详细信息
       * 
       * 数据库操作说明：
       * 1. findMany：查询多条记录
       * 2. where：使用上面构建的查询条件
       * 3. orderBy：按上传时间降序排列，最新上传的记录排在前面
       * 4. skip：分页偏移量，跳过前(page-1)*limit条记录
       * 5. take：分页大小，取limit条记录
       * 6. select：选择需要返回的字段
       *    - 包含股东名册基本字段
       *    - 通过关联查询 companyInfo 获取公司详细信息
       *    - companyInfo 是一对多关系，每个名册可能有多条公司信息记录
       *    - 通过关联查询 user 获取上传用户信息
       */
      const registries = await db.shareholderRegistry.findMany({
			where,
			orderBy: { registerDate: "desc" },
			skip: (page - 1) * limit,
			take: limit,
			select: {
				id: true, // 股东名册ID
				fileName: true, // 文件名
				recordCount: true, // 记录数量
				registerDate: true, // 登记日期
				companyCode: true, // 公司代码
				uploadedAt: true, // 上传时间
				user: {
					// 关联的用户信息
					select: {
						name: true, // 用户名称
					},
				},
				companyInfo: {
					// 关联的公司信息，一对多关系
					select: {
						companyName: true, // 公司名称
						totalShares: true, // 总股数（Decimal类型）
						totalShareholders: true, // 总户数
						totalInstitutions: true, // 机构总数
						largeSharesCount: true, // 持有万份以上总份数（Decimal类型）
						institutionShares: true, // 总机构股数（Decimal类型）
						largeShareholdersCount: true, // 持有万份以上总户数
					},
				},
			},
		});
      
      /**
       * 查询每个股东名册的控股股东和前十大股东信息
       * 
       * 为了优化性能，我们采用一次性查询，而不是在循环中为每个名册单独查询
       * 1. 获取所有查询到的名册ID
       * 2. 使用IN操作符一次查询所有名册的控股股东和前十大股东
       * 3. 将结果按名册ID分组，以便后续处理
       */
      const registryIds = registries.map(registry => registry.id);
      
      // 一次性查询所有名册的控股股东（每个名册持股比例最大的股东）
      const controllingShareholders = await db.$queryRaw<Array<{
        registryId: string;
        securitiesAccountName: string;
        shareholdingRatio: Prisma.Decimal;
        numberOfShares: Prisma.Decimal;
      }>>`
        WITH RankedShareholders AS (
          SELECT 
            "registryId",
            "securitiesAccountName",
            "shareholdingRatio",
            "numberOfShares",
            ROW_NUMBER() OVER (PARTITION BY "registryId" ORDER BY "shareholdingRatio" DESC) as rn
          FROM "shareholder"
          WHERE "registryId" IN (${Prisma.join(registryIds)})
        )
        SELECT 
          "registryId",
          "securitiesAccountName",
          "shareholdingRatio",
          "numberOfShares"
        FROM RankedShareholders
        WHERE rn = 1
      `;

      // 一次性查询所有名册的前十大股东持股总和
      const topTenShareholders = await db.$queryRaw<Array<{
        registryId: string;
        totalRatio: Prisma.Decimal;
        totalShares: Prisma.Decimal;
      }>>`
        WITH RankedShareholders AS (
          SELECT 
            "registryId",
            "shareholdingRatio",
            "numberOfShares",
            ROW_NUMBER() OVER (PARTITION BY "registryId" ORDER BY "shareholdingRatio" DESC) as rn
          FROM "shareholder"
          WHERE "registryId" IN (${Prisma.join(registryIds)})
        ),
        TopTenShareholders AS (
          SELECT 
            "registryId",
            "shareholdingRatio",
            "numberOfShares"
          FROM RankedShareholders
          WHERE rn <= 10
        )
        SELECT 
          "registryId",
          SUM("shareholdingRatio") as "totalRatio",
          SUM("numberOfShares") as "totalShares"
        FROM TopTenShareholders
        GROUP BY "registryId"
      `;

      // 创建查找映射，方便快速访问
      const controllingShareholderMap = new Map(
        controllingShareholders.map(item => [item.registryId, item])
      );

      const topTenShareholdersMap = new Map(
        topTenShareholders.map(item => [
          item.registryId, 
          { totalRatio: item.totalRatio, totalShares: item.totalShares }
        ])
      );
      
      /**
       * 处理查询结果，转换为前端需要的格式
       * 
       * 数据处理说明：
       * 1. 遍历查询结果，处理每条记录
       * 2. 格式化日期和时间
       * 3. 将Decimal类型转换为字符串
       * 4. 构建包含公司详情的响应数据
       * 5. 添加用户名称信息
       */
      const responseData: ListRegistryResponse = {
        registries: registries.map(item => {
          // 获取第一个公司信息记录（通常只有一个）
          const companyInfoRecord = item.companyInfo[0];
          
          // 构建基本信息对象
          const registryItem = {
            id: item.id,
            fileName: item.fileName,
            recordCount: item.recordCount,
            // 将日期对象转换为YYYY-MM-DD格式的字符串
            registerDate: item.registerDate.toISOString().split('T')[0],
            companyCode: item.companyCode,
            companyName: companyInfoRecord?.companyName || "",
            // 将时间戳转换为ISO格式字符串
            uploadedAt: item.uploadedAt.toISOString(),
            // 添加用户名称，如果不存在则显示"未知用户"
            userName: item.user?.name || "未知用户",
          };
          
          // 如果存在公司信息记录，添加详细信息
          if (companyInfoRecord) {
            /**
             * 构建公司详细信息对象
             * 注意：需要将Prisma的Decimal类型转换为字符串
             * Decimal类型在序列化为JSON时会出错，必须先转为字符串
             */
            const companyDetail: CompanyDetailInfo = {
              companyName: companyInfoRecord.companyName,
              totalShares: companyInfoRecord.totalShares.toString(),
              totalShareholders: companyInfoRecord.totalShareholders,
              totalInstitutions: companyInfoRecord.totalInstitutions,
              largeSharesCount: companyInfoRecord.largeSharesCount.toString(),
              institutionShares: companyInfoRecord.institutionShares.toString(),
              largeShareholdersCount: companyInfoRecord.largeShareholdersCount,
            };
            
            // 添加控股股东信息（如果存在）
            const controllingShareholder = controllingShareholderMap.get(item.id);
            if (controllingShareholder) {
              companyDetail.controllingShareholderInfo = {
                securitiesAccountName: controllingShareholder.securitiesAccountName,
                shareholdingRatio: controllingShareholder.shareholdingRatio.toString(),
                numberOfShares: controllingShareholder.numberOfShares.toString()
              };
            }
            
            // 添加前十大股东信息（如果存在）
            const topTenInfo = topTenShareholdersMap.get(item.id);
            if (topTenInfo) {
              companyDetail.topTenShareholdersInfo = {
                totalRatio: topTenInfo.totalRatio.toString(),
                totalShares: topTenInfo.totalShares.toString()
              };
            }
            
            // 合并基本信息和详细信息
            return {
              ...registryItem,
              companyDetail,
            };
          }
          
          // 如果没有详细信息，只返回基本信息
          return registryItem;
        }),
        // 构建分页信息对象
        pagination: {
          total,        // 总记录数
          page,         // 当前页码
          limit,        // 每页条数
          totalPages,   // 总页数
        }
      };
      /**
       * 设置响应数据
       * 注意：响应数据会通过中间件进行加密处理
       */
      c.set("response", {
        code: 200,
        message: "获取股东名册列表成功",
        data: responseData
      });
      
      return;
    } catch (error) {
      /**
       * 错误处理逻辑
       * 1. 处理HTTP异常（如参数验证错误）
       * 2. 处理其他未预期的错误
       */
      if (error instanceof HTTPException) {
        // 处理已知的HTTP异常
        c.set("response", {
          code: error.status,
          message: error.message,
          data: null
        });
        return;
      }
      
      // 处理未知的服务器错误
      console.error("获取股东名册列表失败:", error);
      c.set("response", {
        code: 500,
        message: "获取股东名册列表失败",
        data: null
      });
      return;
    }
  }
); 