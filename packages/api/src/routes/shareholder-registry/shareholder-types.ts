import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { ShareholderTypesSchema } from "./lib/validators";

/**
 * 股东类型查询接口
 * 
 * @update 2025-06-10 创建股东类型查询接口
 * @update 2025-06-11 优化查询方式，修复空数据问题
 * <AUTHOR>
 * @function 获取指定组织下的所有股东类型
 * @path /shareholder-types
 * @method POST
 */
export const shareholderTypesRouter = new Hono()
  .post(
    "/shareholder-types",
    authMiddleware,
    shareholderCryptoMiddleware(),
    async (c) => {
      try {
        // 获取解密后的请求数据
        const requestData = c.get("requestData");
        
        // 验证请求参数格式
        const validationResult = ShareholderTypesSchema.safeParse(requestData);
        if (!validationResult.success) {
          throw new HTTPException(400, { 
            message: "请求参数无效", 
            cause: validationResult.error 
          });
        }
        
        const { organizationId } = validationResult.data;
        
        // 查询该组织下的所有股东类型（去重）
        const shareholderTypes = await db.shareholder.findMany({
			where: {
				organizationId,
			},
			select: {
				shareholderCategory: true,
			},
			distinct: ["shareholderCategory"],
			orderBy: {
				shareholderCategory: "asc", // 按字母顺序排序
			},
		});
        
        // 提取股东类型列表
        const types = shareholderTypes.map(item => item.shareholderCategory);
        
        // 设置响应数据
        c.set("response", {
          code: 200,
          message: "获取股东类型列表成功",
          data: {
            shareholderTypes: types
          }
        });
        
        return;
      } catch (error) {
        // 错误处理逻辑
        if (error instanceof HTTPException) {
          c.set("response", {
            code: error.status,
            message: error.message,
            data: null
          });
          return;
        }
        
        console.error("[股东类型查询] 错误:", error);
        c.set("response", {
          code: 500,
          message: "获取股东类型列表失败",
          data: null
        });
        return;
      }
    }
  ); 