import { Prisma } from "@prisma/client";
import type { Context } from "hono";
import { HTTPException } from "hono/http-exception";

/**
 * 名册类型定义
 * 
 * 深市: 01, 05
 * 沪市: t1, t2, t3
 * 未知: unknown
 */
export type RegistryType = "01" | "05" | "t1" | "t2" | "t3" | "unknown";

/**
 * 判断是否需要使用合并处理器
 * 
 * @param existingRegistry 现有名册记录
 * @param registryType 当前上传的名册类型
 * @returns 是否需要合并和对应的合并处理器类型
 * 
 * 更新记录:
 * - 2025-06-04 16:42:49: 修复文件名包含换行符的处理问题，对文件名进行分割处理后再进行类型检测
 * - 2025-06-04 13:38:24: 优化名册类型检测逻辑，使用startsWith方法替代正则表达式，
 *   确保与detectRegistryTypeByFileName函数保持一致的检测方式
 * - 2025-06-04 13:23:43.362: 优化名册类型检测逻辑，支持t1/t2/t3开头的文件名格式
 *   如t36053380320241231t200.c31.xls、t16053380320240625all.625.xls等
 */
export function shouldUseMergeHandler(
  existingRegistry: { id: string; fileName: string } | null,
  registryType: RegistryType
): { needMerge: boolean; mergeType: "01-05" | "t1-t2-t3" | null } {
  if (!existingRegistry) {
    return { needMerge: false, mergeType: null };
  }
  
  // 将文件名按换行符分割为数组，处理可能包含多个文件名的情况
  const existingFileNames = existingRegistry.fileName.toLowerCase().split('\n');
  
  // 检查现有名册类型，判断每个文件名
  let hasType01 = false;
  let hasType05 = false;
  let hasT1 = false;
  let hasT2 = false;
  let hasT3 = false;
  
  // 遍历所有文件名进行检测
  for (const fileName of existingFileNames) {
    // 跳过空文件名
    if (!fileName.trim()) {
      continue;
    }
    
    // 深市名册检测
    if (fileName.includes("dqmc01")) {
      hasType01 = true;
    }
    if (fileName.includes("dqmc05")) {
      hasType05 = true;
    }
    
    // 沪市名册精确检测 - 必须以t1、t2、t3开头，避免误判
    if (fileName.match(/^t1[^0-9]/i) || fileName.match(/^t1$/i) || fileName.match(/^t1\d{10,}/i)) {
      hasT1 = true;
    } else if (fileName.match(/^t2[^0-9]/i) || fileName.match(/^t2$/i) || fileName.match(/^t2\d{10,}/i)) {
      hasT2 = true;
    } else if (fileName.match(/^t3[^0-9]/i) || fileName.match(/^t3$/i) || fileName.match(/^t3\d{10,}/i)) {
      hasT3 = true;
    }
  }
  
  // 先检查是否存在相同类型的名册（不允许重复上传同类型）
  if ((registryType === "01" && hasType01) || 
      (registryType === "05" && hasType05) || 
      (registryType === "t1" && hasT1) || 
      (registryType === "t2" && hasT2) || 
      (registryType === "t3" && hasT3)) {
    // 存在相同类型，不需要合并（在后续处理中会抛出错误）
    return { needMerge: false, mergeType: null };
  }
  
  // 深市名册合并判断
  if ((registryType === "01" && hasType05) || (registryType === "05" && hasType01)) {
    return { needMerge: true, mergeType: "01-05" };
  }
  
  // 沪市名册合并判断
  if (registryType === "t1" || registryType === "t2" || registryType === "t3") {
    // 检查是否已有其他类型的沪市名册
    const hasOtherTRegistry = 
      (registryType === "t1" && (hasT2 || hasT3)) || 
      (registryType === "t2" && (hasT1 || hasT3)) || 
      (registryType === "t3" && (hasT1 || hasT2));
    
    if (hasOtherTRegistry) {
      return { needMerge: true, mergeType: "t1-t2-t3" };
    }
  }
  
  return { needMerge: false, mergeType: null };
}


/**
 * 通过文件名检测名册类型
 * 
 * 检测逻辑:
 * 1. 统一转换为小写进行判断
 * 2. 使用正则表达式匹配文件名开头的t1、t2、t3模式
 * 3. 如果正则匹配失败，则使用includes方法作为备选方案
 * 
 * 更新记录:
 * - 2025-06-04 13:36:16: 修复对t3开头文件名的识别问题，确保t36053380320241231t200.c31.xls等文件被正确识别为t3类型
 * - 2025-06-04 13:22:33.302: 优化名册类型检测逻辑，支持t1/t2/t3开头的文件名格式
 *   如t36053380320241231t200.c31.xls、t16053380320240625all.625.xls等
 * 
 * @param fileName 文件名
 * @return 名册类型: "01" | "05" | "t1" | "t2" | "t3" | "unknown"
 */
export function detectRegistryTypeByFileName(fileName: string): RegistryType {
  // 统一转换为小写进行判断
  const lowerFileName = fileName.toLowerCase();
  
  // 使用更严格的正则表达式匹配文件名开头的t1、t2、t3模式
  // 确保t3开头的文件不会被错误地识别为t2
  if (lowerFileName.startsWith('t3')) {
    return "t3";
  }
  
  if (lowerFileName.startsWith('t2')) {
    return "t2";
  }
  
  if (lowerFileName.startsWith('t1')) {
    return "t1";
  }
  
  // 如果上面的精确匹配失败，使用传统的关键字匹配作为备选方案
  if (lowerFileName.includes("dqmc01")) {
    return "01";
  } 
  
  if (lowerFileName.includes("dqmc05")) {
    return "05";
  } 
  
  return "unknown";
}

/**
 * 检测T3名册是否为全量数据
 *
 * 功能说明:
 * - 检测文件名中是否包含"all"标识，用于识别T3全量名册
 * - 全量T3名册用于替换前200股东版本的信用数据
 *
 * 更新记录:
 * - 2025-06-13 12:02:02: hayden 新增T3全量名册识别函数
 *
 * @param fileName 文件名
 * @returns 是否为全量T3名册
 */
export function isT3FullRegistry(fileName: string): boolean {
  const lowerFileName = fileName.toLowerCase();
  return lowerFileName.startsWith('t3') && lowerFileName.includes('all');
}

/**
 * 处理股东字段更新逻辑
 * 
 * 功能说明:
 * - 根据名册类型处理不同字段的更新
 * - 仅当现有记录中字段为空且新数据有值时才更新
 * - 支持所有名册类型的特有字段和共有字段的更新
 * 
 * 更新规则:
 * 1. 05名册特有字段
 * 2. 01名册特有字段
 * 3. t1名册特有字段
 * 4. t2名册特有字段
 * 5. t3名册特有字段
 * 6. 共有字段
 * 
 * @param updateData - 用于存储需要更新的字段和值的对象
 * @param existingShareholder - 数据库中现有的股东记录
 * @param newShareholder - 新的股东数据
 * @param registryType - 名册类型
 * @returns 布尔值，表示是否有字段需要更新
 */
export function processShareholderUpdateFields(
  updateData: Record<string, unknown>,
  existingShareholder: any,
  newShareholder: any,
  registryType: RegistryType
): boolean {
  let hasUpdates = false;
  
  // 处理05名册特有字段
  if (registryType === "05") {
    if (!existingShareholder.marginCollateralAccountNumber && newShareholder.marginCollateralAccountNumber) {
      updateData.marginCollateralAccountNumber = newShareholder.marginCollateralAccountNumber;
      hasUpdates = true;
    }
    
    if (!existingShareholder.marginCollateralAccountName && newShareholder.marginCollateralAccountName) {
      updateData.marginCollateralAccountName = newShareholder.marginCollateralAccountName;
      hasUpdates = true;
    }
    
    if (!existingShareholder.natureOfShares && newShareholder.natureOfShares) {
      updateData.natureOfShares = newShareholder.natureOfShares;
      hasUpdates = true;
    }
  }
  
  // 处理01名册特有字段
  if (registryType === "01") {
    if (!existingShareholder.contactNumber && newShareholder.contactNumber) {
      updateData.contactNumber = newShareholder.contactNumber;
      hasUpdates = true;
    }
    
    if (!existingShareholder.zipCode && newShareholder.zipCode) {
      updateData.zipCode = newShareholder.zipCode;
      hasUpdates = true;
    }
    
    if (!existingShareholder.relatedPartyIndicator && newShareholder.relatedPartyIndicator) {
      updateData.relatedPartyIndicator = newShareholder.relatedPartyIndicator;
      hasUpdates = true;
    }
    
    if (!existingShareholder.clientCategory && newShareholder.clientCategory) {
      updateData.clientCategory = newShareholder.clientCategory;
      hasUpdates = true;
    }
    
    if (!existingShareholder.remarks && newShareholder.remarks) {
      updateData.remarks = newShareholder.remarks;
      hasUpdates = true;
    }
  }
  
  // 处理t1名册特有字段
  if (registryType === "t1") {
    if (!existingShareholder.shareTradingCategory && newShareholder.shareTradingCategory) {
      updateData.shareTradingCategory = newShareholder.shareTradingCategory;
      hasUpdates = true;
    }
    
    if (!existingShareholder.rightsCategory && newShareholder.rightsCategory) {
      updateData.rightsCategory = newShareholder.rightsCategory;
      hasUpdates = true;
    }
  }
  
  // 处理t1/t2/t3名册共有特有字段 - 按照"先到先得"原则
  if (registryType === "t1" || registryType === "t2" || registryType === "t3") {
    if (!existingShareholder.investorName && newShareholder.investorName) {
      updateData.investorName = newShareholder.investorName;
      hasUpdates = true;
    }
    
    if (!existingShareholder.shareholderName && newShareholder.shareholderName) {
      updateData.shareholderName = newShareholder.shareholderName;
      hasUpdates = true;
    }
    
    if (!existingShareholder.certificateType && newShareholder.certificateType) {
      updateData.certificateType = newShareholder.certificateType;
      hasUpdates = true;
    }
    
    if (!existingShareholder.shareholderNature && newShareholder.shareholderNature) {
      updateData.shareholderNature = newShareholder.shareholderNature;
      hasUpdates = true;
    }
    
    if (!existingShareholder.accountStatus && newShareholder.accountStatus) {
      updateData.accountStatus = newShareholder.accountStatus;
      hasUpdates = true;
    }

    // 处理t1/t2/t3共有的其他字段
    if (!existingShareholder.cashAccount && newShareholder.cashAccount) {
      updateData.cashAccount = newShareholder.cashAccount;
      hasUpdates = true;
    }
  }
  
  // 处理共有字段，仅当现有值为空时更新
  if (!existingShareholder.contactAddress && newShareholder.contactAddress) {
    updateData.contactAddress = newShareholder.contactAddress;
    hasUpdates = true;
  }
  
  if (!existingShareholder.cashAccount && newShareholder.cashAccount) {
    updateData.cashAccount = newShareholder.cashAccount;
    hasUpdates = true;
  }
  
  if (!existingShareholder.marginAccount && newShareholder.marginAccount) {
    updateData.marginAccount = newShareholder.marginAccount;
    hasUpdates = true;
  }
  
  // 注意：持股数量相关字段不在这里处理，而是在processShareholdingAmountMerge中处理
  
  return hasUpdates;
}

/**
 * 处理t1/t2/t3名册持股数量合并逻辑
 * 
 * 功能说明:
 * - 针对沪市t1/t2/t3名册的持股数量字段进行"先到先得"处理
 * - 仅当现有记录中字段为空且新数据有值时才更新
 * - 前端已对相同一码通和股东ID的记录进行了预处理
 * 
 * @param existingShareholder - 数据库中现有的股东记录
 * @param newShareholder - 新的股东数据
 * @returns 更新后的数据对象
 */
export function processShareholdingAmountMerge(
  existingShareholder: any,
  newShareholder: any
): Record<string, unknown> {
  const updateData: Record<string, unknown> = {};
  
  // 处理普通账户持股数量 - 采用"先到先得"原则
  if (!existingShareholder.sharesInCashAccount && newShareholder.sharesInCashAccount) {
    const newAmount = Number.parseFloat(String(newShareholder.sharesInCashAccount));
    
    if (!Number.isNaN(newAmount)) {
      updateData.sharesInCashAccount = new Prisma.Decimal(newAmount);
    }
  }
  
  // 处理信用账户持股数量 - 采用"先到先得"原则
  if (!existingShareholder.sharesInMarginAccount && newShareholder.sharesInMarginAccount) {
    const newAmount = Number.parseFloat(String(newShareholder.sharesInMarginAccount));
    
    if (!Number.isNaN(newAmount)) {
      updateData.sharesInMarginAccount = new Prisma.Decimal(newAmount);
    }
  }
  
  // 处理总持股数量 - 采用"先到先得"原则
  if (!existingShareholder.numberOfShares && newShareholder.numberOfShares) {
    const newAmount = Number.parseFloat(String(newShareholder.numberOfShares));
    
    if (!Number.isNaN(newAmount)) {
      updateData.numberOfShares = new Prisma.Decimal(newAmount);
    }
  }
  
  return updateData;
}

/**
 * 处理日期转换的工具函数
 * 将字符串日期格式转换为日期对象
 * 
 * @param dateString 日期字符串，格式为YYYY-MM-DD
 * @returns 日期对象
 */
export function parseRegisterDate(dateString: string): Date {
	// 验证日期格式
	if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
		throw new Error(`无效的日期格式'${dateString}'，应为YYYY-MM-DD`);
	}
	return new Date(dateString);
}

/**
 * 将日期对象转换为YYYY-MM-DD格式的字符串
 * 
 * @param date 日期对象
 * @returns 格式化的日期字符串
 */
export function formatRegisterDate(date: Date): string {
	return date.toISOString().split("T")[0];
}

/**
 * 构建分页信息
 * 
 * @param total 总记录数
 * @param page 当前页码
 * @param limit 每页条数
 * @returns 分页信息对象
 */
export function buildPagination(total: number, page: number, limit: number) {
  const totalPages = Math.ceil(total / limit);
  return {
    total,
    page,
    limit,
    totalPages
  };
}

/**
 * 构建排序参数
 * 
 * @param sortBy 排序字段
 * @param sortOrder 排序方向，"asc"或"desc"
 * @returns Prisma排序参数对象
 */
export function buildOrderBy(sortBy: string, sortOrder: "asc" | "desc"): Prisma.ShareholderOrderByWithRelationInput {
  return {
    [sortBy]: sortOrder
  };
}

/**
 * 构建搜索条件
 * 
 * @param searchTerm 搜索关键词
 * @returns Prisma搜索条件对象
 */
export function buildSearchCondition(searchTerm?: string) {
  if (!searchTerm) {
    return {};
  }
  
  return {
    OR: [
      { securitiesAccountName: { contains: searchTerm } },
      { shareholderId: { contains: searchTerm } },
      { unifiedAccountNumber: { contains: searchTerm } },
      { contactNumber: { contains: searchTerm } }
    ]
  };
}

/**
 * 处理特殊字符串转换为十进制数
 * 主要用于处理DBF文件中的数值型字段
 * 
 * @param value 字符串数值
 * @returns Prisma.Decimal 对象
 */
export function toDecimal(value: string): Prisma.Decimal {
  try {
    return new Prisma.Decimal(value.trim().replace(/,/g, ''));
  } catch (error) {
    console.error(`无法转换为数值: ${value}`, error);
    return new Prisma.Decimal(0);
  }
}

/**
 * 错误码类型定义
 */
export type ErrorCode = 
  | "UNAUTHORIZED" 
  | "FORBIDDEN" 
  | "RESOURCE_NOT_FOUND" 
  | "VALIDATION_ERROR" 
  | "DUPLICATE_REGISTRY" 
  | "DUPLICATE_REGISTRY_TYPE" 
  | "COMPLETE_REGISTRY" 
  | "COMPANY_NAME_MISMATCH" 
  | "COMPANY_CODE_MISMATCH"
  | "RECORD_COUNT_MISMATCH"
  | "DATABASE_ERROR" 
  | "INTERNAL_SERVER_ERROR";

/**
 * 统一响应格式接口
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T | null;
}

/**
 * 错误响应格式接口
 */
export interface ApiErrorResponse {
  code: number;
  message: string;
  error?: {
    code: ErrorCode;
    message: string;
  };
  data: null;
}

/**
 * 成功响应处理函数
 * 
 * @param c Hono上下文
 * @param message 成功提示信息
 * @param data 响应数据
 * @param code 状态码，默认200
 */
export function successResponse<T>(
  c: Context,
  message: string,
  data: T,
  code = 200
): void {
  const response: ApiResponse<T> = {
    code,
    message,
    data
  };
  
  c.set("response", response);
}

/**
 * 错误响应处理函数
 * 
 * @param c Hono上下文
 * @param message 错误提示信息
 * @param code HTTP状态码
 * @param errorCode 错误码
 * @param errorMessage 详细错误信息
 */
export function errorResponse(
  c: Context,
  message: string,
  code = 500,
  errorCode?: ErrorCode,
  errorMessage?: string
): void {
  const response: ApiErrorResponse = {
    code,
    message,
    data: null
  };
  
  if (errorCode) {
    response.error = {
      code: errorCode,
      message: errorMessage || message
    };
  }
  
  c.set("response", response);
}

/**
 * HTTP异常处理函数
 * 将HTTPException转换为标准错误响应
 * 
 * @param c Hono上下文
 * @param error 错误对象
 * 
 * @update 2025-06-12 15:48:05 - 优化未知错误处理方式，添加更友好的错误信息并记录详细错误日志
 * @update 2025-06-04 16:51:36 - 移除直接打印错误日志到控制台，改为更规范的错误处理方式
 */
export function handleHttpException(c: Context, error: unknown): void {
  // P2002 是 Prisma 唯一约束错误代码
  const isPrismaUniqueConstraintError = 
    error instanceof Error && 
    (error.message.includes('Unique constraint failed') || 
    (error as any)?.code === 'P2002');

  // 对于唯一约束错误，提供友好的错误信息而不在控制台输出完整错误
  if (isPrismaUniqueConstraintError) {
    errorResponse(
      c, 
      "该组织在此报告日期已经上传过该公司的股东名册，请先删除原有名册后再上传", 
      409, 
      "DUPLICATE_REGISTRY",
      "同一组织在同一日期只能有一份相同公司代码的股东名册。请前往名册管理页面删除已有名册，然后再尝试上传。"
    );
    return;
  }
  
  // 记录详细错误信息到服务器日志，但不暴露给前端
  console.error("[股东名册] 错误详情:", error);
  
  if (error instanceof HTTPException) {
    // 从错误消息中解析错误类型
    const errorMessage = error.message;
    
    // 处理名册类型重复上传的错误
    if (errorMessage.startsWith("DUPLICATE_REGISTRY_TYPE:")) {
      const userMessage = errorMessage.split(":")[1];
      errorResponse(
        c, 
        userMessage, 
        error.status, 
        "DUPLICATE_REGISTRY_TYPE",
        "您已经上传过相同类型的名册，不能重复上传。请先删除原有名册后再上传。"
      );
      return;
    }
    
    // 处理已完成01和05名册合并的错误
    if (errorMessage.startsWith("COMPLETE_REGISTRY:")) {
      const userMessage = errorMessage.split(":")[1];
      errorResponse(
        c, 
        userMessage, 
        error.status, 
        "COMPLETE_REGISTRY",
        "该股东名册已经完成01和05类型的合并，不能继续上传。如需修改，请先删除原有名册后再上传。"
      );
      return;
    }
    
    // 处理重复上传同类型名册的普通错误
    if (errorMessage.includes("已存在同一登记日期的") && errorMessage.includes("类型名册")) {
      errorResponse(
        c, 
        errorMessage, 
        400, 
        "DUPLICATE_REGISTRY_TYPE",
        "您已经上传过相同类型的名册，不能重复上传。请先删除原有名册后再上传。"
      );
      return;
    }
    
    errorResponse(c, errorMessage, error.status);
    return;
  }
  
  if (error instanceof Error) {
    // 对于普通Error实例，记录详细错误但返回通用友好消息
    console.error(`[股东名册] 详细错误信息: ${error.message}`, error.stack);
    errorResponse(
      c, 
      "系统处理请求时发生错误，请稍后重试或联系管理员", 
      500,
      "INTERNAL_SERVER_ERROR",
      "请联系系统管理员并提供以下错误ID进行问题排查"
    );
    return;
  }
  
  // 默认错误处理 - 未知错误类型
  console.error("[股东名册] 未知类型错误:", error);
  errorResponse(
    c, 
    "系统遇到未知错误，请联系管理员处理", 
    500, 
    "INTERNAL_SERVER_ERROR",
    "如果问题持续存在，请联系系统管理员并说明您执行的操作"
  );
} 