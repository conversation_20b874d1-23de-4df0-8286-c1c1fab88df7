import { z } from "zod";
import type { RegistryType } from "./utils";
import { validateShareholderFields } from "./field-mapping";

/**
 * 根据名册类型创建股东验证模式
 *
 * @update 2025-06-03 更新为使用原始字段名进行验证，而不是数据库字段名
 * @param registryType 名册类型
 * @returns 对应名册类型的股东验证模式
 */
function createShareholderSchemaByType(
	registryType: RegistryType
): z.ZodObject<any> {
	// 基础共享字段 - 使用原始字段名
	const baseSchema = {
		// 01名册字段
		ZJDM: z.string().min(1, "证件号码不能为空").optional(),
		YMTH: z.string().min(1, "一码通账户号码不能为空").optional(),
		ZQZHMC: z.string().min(1, "证券账户名称不能为空").optional(),
		CYRLBMS: z.string().min(1, "持有人类别不能为空").optional(),
		
		// 05名册字段
		XYZHZJDM: z.string().min(1, "证件号码不能为空").optional(),
		XYZHMC: z.string().min(1, "证券账户名称不能为空").optional(),
		
		// t1/t2/t3名册字段
		ZJHM: z.string().min(1, "证件号码不能为空").optional(),
		YMTZHHM: z.string().min(1, "一码通账户号码不能为空").optional(),
		GDLB: z.string().min(1, "持有人类别不能为空").optional(),
		CYRMC: z.string().min(1, "持有人名称不能为空").optional(),
		
		// 可选的共享字段
		TXDZ: z.string().optional(), // 通讯地址
		BZ: z.string().optional(),   // 备注
	};

	// 根据名册类型添加特定字段
	switch (registryType) {
		case "01": {
			return z.object({
				...baseSchema,
				CGSL: z.string().min(1, "持股数量不能为空"),
				XSGSL: z.string().min(1, "限售股数量不能为空"),
				CGBL: z.string().min(1, "持股比例不能为空"),
				DJGS: z.string().min(1, "冻结股数不能为空"),
				PTZQZH: z.string().optional(),
				PTZHCGSL: z.string().optional(),
				XYZQZH: z.string().optional(),
				XYZHCGSL: z.string().optional(),
				DHHM: z.string().optional(),
				YZBM: z.string().optional(),
				GLGXBS: z.string().optional(),
				KHLB: z.string().optional(),
			});
		}
		case "05": {
			return z.object({
				...baseSchema,
				CGSL: z.string().min(1, "信用账户持股数量不能为空"),
				DJGS: z.string().min(1, "冻结股数不能为空"),
				XYZQZH: z.string().optional(),
				HZZQZH: z.string().min(1, "汇总账户号码不能为空"),
				HZZHMC: z.string().min(1, "汇总账户名称不能为空"),
				GFXZ: z.string().optional(),
			});
		}
		case "t1": {
			return z.object({
				...baseSchema,
				CYRMC: z.string().min(1, "持有人名称不能为空"),
				CYSL: z.string().optional(),
				LXDH: z.string().optional(),
				YZBM: z.string().optional(),
				LTLX: z.string().min(1, "流通类型不能为空"),
				YQLB: z.string().min(1, "权益类别不能为空"),
				TZRMC: z.string().optional(),
				GDMC: z.string().optional(),
				ZJLX: z.string().optional(),
				GDZL: z.string().optional(),
				ZHZT: z.string().optional(),
			});
		}
		case "t2": {
			return z.object({
				...baseSchema,
				CYRMC: z.string().min(1, "持有人名称不能为空"),
				CYSL: z.string().optional(),
				LXDH: z.string().optional(),
				YZBM: z.string().optional(),
				TZRMC: z.string().min(1, "投资者名称不能为空"),
				GDMC: z.string().min(1, "股东名称不能为空"),
				ZJLX: z.string().optional(),
				GDZL: z.string().optional(),
				ZHZT: z.string().optional(),
			});
		}
		case "t3": {
			return z.object({
				...baseSchema,
				CYRMC: z.string().min(1, "持有人名称不能为空"),
				ZCYSL: z.string().min(1, "总持股数量不能为空"),
				PTZQZHCYSL: z.string().min(1, "普通账户持股数量不能为空"),
				XYCYSL: z.string().min(1, "信用账户持股数量不能为空"),
				PTZQZH: z.string().optional(),
				XYZQZH: z.string().optional(),
				LXDH: z.string().optional(),
				YZBM: z.string().optional(),
				TZRMC: z.string().optional(),
				GDMC: z.string().optional(),
				ZJLX: z.string().optional(),
				GDZL: z.string().optional(),
				ZHZT: z.string().optional(),
			});
		}
		default: {
			// 通用模式，包含所有可能的字段
			return z.object({
				...baseSchema,
				// 01名册字段
				CGSL: z.string().optional(),
				XSGSL: z.string().optional(),
				CGBL: z.string().optional(),
				DJGS: z.string().optional(),
				PTZQZH: z.string().optional(),
				PTZHCGSL: z.string().optional(),
				XYZQZH: z.string().optional(),
				XYZHCGSL: z.string().optional(),
				DHHM: z.string().optional(),
				YZBM: z.string().optional(),
				GLGXBS: z.string().optional(),
				KHLB: z.string().optional(),
				
				// 05名册字段
				HZZQZH: z.string().optional(),
				HZZHMC: z.string().optional(),
				GFXZ: z.string().optional(),
				
				// t1/t2/t3名册字段
				CYRMC: z.string().optional(),
				CYSL: z.string().optional(),
				LXDH: z.string().optional(),
				LTLX: z.string().optional(),
				YQLB: z.string().optional(),
				TZRMC: z.string().optional(),
				GDMC: z.string().optional(),
				ZJLX: z.string().optional(),
				GDZL: z.string().optional(),
				ZHZT: z.string().optional(),
				ZCYSL: z.string().optional(),
				PTZQZHCYSL: z.string().optional(),
				XYCYSL: z.string().optional(),
			});
		}
	}
}

/**
 * 上传股东名册的请求参数验证模式
 */
export const UploadRegistrySchema = z
	.object({
		organizationId: z.string().min(1, "组织ID不能为空"),
		fileName: z.string().min(1, "文件名不能为空"),
		recordCount: z.number().int().positive("记录数量必须为正整数"),
		registerDate: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, "报告日期格式必须为YYYY-MM-DD"),
		companyCode: z.string().min(1, "公司代码不能为空"),
		companyInfo: z.object({
			companyName: z.string().optional().default(""),
			totalShares: z.string().min(1, "总股数不能为空"),
			totalShareholders: z
				.number()
				.int()
				.nonnegative("总户数必须为非负整数"),
			totalInstitutions: z
				.number()
				.int()
				.nonnegative("机构总数必须为非负整数"),
			largeSharesCount: z.string().min(1, "持有万份以上总份数不能为空"),
			institutionShares: z.string().min(1, "总机构股数不能为空"),
			largeShareholdersCount: z
				.number()
				.int()
				.nonnegative("持有万份以上总户数必须为非负整数"),
			// 新增信用股东统计字段验证 - 2025-06-13 12:02:02 hayden 添加
			marginAccounts: z
				.number()
				.int()
				.nonnegative("信用总户数必须为非负整数")
				.optional(),
			marginShares: z
				.string()
				.optional(),
		}),
		shareholders: z
			.array(
				// 使用动态验证，根据registryType字段选择对应的验证模式
				z.lazy(() => {
					// 在这里，我们不能直接访问父对象的registryType
					// 所以我们返回一个通用的验证模式，包含所有可能的字段
					return createShareholderSchemaByType("unknown");
				})
			)
			.min(1, "股东列表不能为空"),
		// 名册类型字段，用于区分不同类型名册
		registryType: z.enum(["01", "05", "t1", "t2", "t3"]).optional(), // 名册类型，可选，从文件名解析
	})
	.refine(
		(data) => {
			// 如果没有指定名册类型，则跳过验证
			if (!data.registryType) {
				return true;
			}

			// 使用validateShareholderFields函数验证股东数据是否符合指定名册类型的要求
			const validationResult = validateShareholderFields(
				data.shareholders,
				data.registryType
			);

			// 如果验证失败，抛出错误
			if (!validationResult.isValid) {
				throw new Error(
					`股东数据与名册类型不匹配，缺少必要字段: ${validationResult.missingFields.join(
						", "
					)}`
				);
			}

			return true;
		},
		{
			message: "股东数据与名册类型不匹配，请检查必要字段",
			path: ["shareholders"],
		}
	);

/**
 * 获取股东名册列表的请求参数验证模式
 */
export const ListRegistrySchema = z.object({
	organizationId: z.string().min(1, "组织ID不能为空"),
	page: z.number().int().positive().optional().default(1),
	limit: z.number().int().positive().optional().default(10),
	companyCode: z.string().optional(),
});

/**
 * 获取股东列表的请求参数验证模式
 */
export const ShareholdersSchema = z.object({
	registerDate: z
		.union([
			z
				.string()
				.regex(/^\d{4}-\d{2}-\d{2}$/, "报告日期格式必须为YYYY-MM-DD"),
			z.string().length(0), // 允许空字符串
			z.null(), // 允许null
		])
		.optional(), // 允许undefined
	organizationId: z.string().min(1, "组织ID不能为空"),
	page: z.number().int().positive().optional().default(1),
	limit: z.number().int().positive().optional().default(10),
	searchTerm: z.string().optional(),
	sortBy: z.string().optional().default("numberOfShares"),
	sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

/**
 * 期数日期查询请求模式
 */
export const RegisterDatesSchema = z.object({
	organizationId: z.string().min(1, "组织ID不能为空"),
	companyCode: z.string().optional(),
});

/**
 * 删除股东名册的请求参数验证模式
 */
export const DeleteRegistrySchema = z.object({
	registryId: z.string().min(1, "名册ID不能为空"),
});

/**
 * 批量删除股东名册的请求参数验证模式
 */
export const BatchDeleteRegistrySchema = z.object({
	registryIds: z
		.array(z.string().min(1, "名册ID不能为空"))
		.min(1, "至少需要提供一个名册ID"),
});

/**
 * 股东持股变化分析请求验证模式
 *
 * @update 2025-06-10 添加股东持股变化分析请求验证模式
 * @update 2025-06-17 16:32:51 添加sortType字段控制排序类型：rank(排名排序)或date(期数日期排序)
 * @update 2025-06-17 17:00:16 修改sortType支持具体期数日期格式(YYYY-MM-DD)作为排序字段
 * <AUTHOR>
 * @time 2025-06-17 17:00:16
 */
export const ShareholdingChangesSchema = z.object({
	organizationId: z.string().min(1, "组织ID不能为空"),
	startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "开始日期格式必须为YYYY-MM-DD"),
	endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "结束日期格式必须为YYYY-MM-DD"),
	shareholderType: z.string().optional(),
	searchTerm: z.string().optional(),
	sortType: z.union([
		z.enum(['rank', 'date']),
		z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "期数日期格式必须为YYYY-MM-DD")
	]).optional().default('rank'),
	sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
	page: z.number().int().positive().optional().default(1),
	limit: z.number().int().positive().max(100).optional().default(30),
}).refine(data => {
	const start = new Date(data.startDate);
	const end = new Date(data.endDate);
	return start <= end;
}, {
	message: "开始日期不能晚于结束日期",
	path: ["endDate"]
});

/**
 * 股东类型查询请求验证模式
 * 
 * @update 2025-06-10 添加股东类型查询请求验证模式
 */
export const ShareholderTypesSchema = z.object({
	organizationId: z.string().min(1, "组织ID不能为空"),
});
