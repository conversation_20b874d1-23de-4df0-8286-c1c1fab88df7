/**
 * 股东名册处理配置
 * 
 * 本模块提供了股东名册处理的配置参数，
 * 包括批量导入的大小、超时设置等。
 * 
 * @created 2025年06月04日 17:01:19
 * @lastModified 2025年06月04日 17:01:19
 */

/**
 * 获取批量创建记录的批次大小
 * 
 * 优先从环境变量获取，如果未设置则使用默认值200
 * 
 * @returns 批次大小
 */
export function getBatchSize(): number {
  const envBatchSize = process.env.SHAREHOLDER_BATCH_SIZE;
  return envBatchSize ? Number.parseInt(envBatchSize, 10) : 200;
}

/**
 * 获取并发更新的批次大小
 * 
 * 优先从环境变量获取，如果未设置则使用默认值20
 * 
 * @returns 并发更新批次大小
 */
export function getConcurrencyLimit(): number {
  const envConcurrencyLimit = process.env.SHAREHOLDER_CONCURRENCY_LIMIT;
  return envConcurrencyLimit ? Number.parseInt(envConcurrencyLimit, 10) : 20;
}

/**
 * 获取事务超时时间（毫秒）
 * 
 * 优先从环境变量获取，如果未设置则使用默认值30000
 * 
 * @returns 事务超时时间（毫秒）
 */
export function getTransactionTimeout(): number {
  const envTimeout = process.env.SHAREHOLDER_TRANSACTION_TIMEOUT;
  return envTimeout ? Number.parseInt(envTimeout, 10) : 30000;
}

/**
 * 导出配置对象，方便直接引用
 */
export const ShareholderRegistryConfig = {
  batchSize: getBatchSize(),
  concurrencyLimit: getConcurrencyLimit(),
  transactionTimeout: getTransactionTimeout()
}; 