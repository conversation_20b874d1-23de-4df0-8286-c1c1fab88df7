import type { RegistryType } from "./utils";

/**
 * 股东名册字段映射模块
 * 
 * 本模块定义了各类型股东名册的字段映射关系，用于将原始字段名映射到数据库字段名。
 * 
 * 更新记录：
 * - 2025年06月03日 17:18:00：临时修复，将t1名册的CYRMC字段设为可选，以兼容前端当前上传格式
 *   注意：此修复为临时措施，前端应尽快更新以支持CYRMC字段
 * - 2025年06月03日 17:02:32：根据《股东名册01_05_t123字段对应关系完整参照表》更新
 *   主要变更：t1/t2/t3名册的securitiesAccountName字段映射从ZQZHMC改为CYRMC
 *   同时更新了detectRegistryTypeByFields函数的检测逻辑，以适应新的字段映射关系
 */

/**
 * 名册字段映射接口
 * 定义从原始字段名到数据库字段名的映射
 */
interface FieldMapping {
  [key: string]: string;
}

/**
 * 名册字段验证接口
 * 定义每种名册类型必须包含的字段
 */
interface RequiredFields {
  [key: string]: string[];
}

/**
 * 01名册字段映射
 * 从01名册原始字段名到数据库字段名的映射
 */
export const TYPE_01_FIELD_MAPPING: FieldMapping = {
  "YMTH": "unifiedAccountNumber",         // 一码通账户号码
  "ZQZHMC": "securitiesAccountName",      // 证券账户名称
  "ZJDM": "shareholderId",                // 证件号码
  "CYRLBMS": "shareholderCategory",       // 持有人类别
  "CGSL": "numberOfShares",               // 总持股数量
  "XSGSL": "lockedUpShares",              // 限售股数量
  "CGBL": "shareholdingRatio",            // 持股比例
  "DJGS": "frozenShares",                 // 冻结股数
  "PTZQZH": "cashAccount",                // 普通证券账户
  "PTZHCGSL": "sharesInCashAccount",      // 普通账户持股数量
  "XYZQZH": "marginAccount",              // 信用证券账户
  "XYZHCGSL": "sharesInMarginAccount",    // 信用账户持股数量
  "TXDZ": "contactAddress",               // 通讯地址
  "DHHM": "contactNumber",                // 联系电话
  "YZBM": "zipCode",                      // 邮政编码
  "GLGXBS": "relatedPartyIndicator",      // 关联关系确认标识
  "KHLB": "clientCategory",               // 客户类别
  "BZ": "remarks"                         // 备注
};

/**
 * 05名册字段映射
 * 从05名册原始字段名到数据库字段名的映射
 */
export const TYPE_05_FIELD_MAPPING: FieldMapping = {
  "YMTH": "unifiedAccountNumber",                 // 一码通账户号码
  "XYZHMC": "securitiesAccountName",              // 证券账户名称
  "XYZHZJDM": "shareholderId",                    // 证件号码
  "CYRLBMS": "shareholderCategory",               // 持有人类别
  "CGSL": "sharesInMarginAccount",                // 信用账户持股数量
  "DJGS": "frozenShares",                         // 冻结股数
  "XYZQZH": "marginAccount",                      // 信用证券账户
  "TXDZ": "contactAddress",                       // 通讯地址
  "HZZQZH": "marginCollateralAccountNumber",      // 汇总账户号码
  "HZZHMC": "marginCollateralAccountName",        // 汇总账户名称
  "GFXZ": "natureOfShares"                        // 股份性质
};

/**
 * t1名册字段映射
 * 从t1名册原始字段名到数据库字段名的映射
 */
export const TYPE_T1_FIELD_MAPPING: FieldMapping = {
  "YMTZHHM": "unifiedAccountNumber",           // 一码通账户号码
  "CYRMC": "securitiesAccountName",            // 持有人名称，根据参照表更新于2025年06月03日 16:59:14
  "ZJHM": "shareholderId",                     // 证件号码
  "GDLB": "shareholderCategory",               // 持有人类别
  "CYSL": "numberOfShares",                   // 总持股数量
  "TXDZ": "contactAddress",                    // 通讯地址
  "LXDH": "contactNumber",                     // 联系电话
  "YZBM": "zipCode",                           // 邮政编码
  "BZ": "remarks",                             // 备注
  "LTLX": "shareTradingCategory",              // 流通类型
  "YQLB": "rightsCategory",                    // 权益类别
};

/**
 * t2名册字段映射
 * 从t2名册原始字段名到数据库字段名的映射
 */
export const TYPE_T2_FIELD_MAPPING: FieldMapping = {
	YMTZHHM: "unifiedAccountNumber", // 一码通账户号码
	CYRMC: "securitiesAccountName", // 持有人名称，根据参照表更新于2025年06月03日 16:59:14
	ZJHM: "shareholderId", // 证件号码
	GDLB: "shareholderCategory", // 持有人类别
	CYSL: "numberOfShares", // 普通账户持股数量
	TXDZ: "contactAddress", // 通讯地址
	LXDH: "contactNumber", // 联系电话
	YZBM: "zipCode", // 邮政编码
	BZ: "remarks", // 备注
};

/**
 * t3名册字段映射
 * 从t3名册原始字段名到数据库字段名的映射
 */
export const TYPE_T3_FIELD_MAPPING: FieldMapping = {
  "YMTZHHM": "unifiedAccountNumber",           // 一码通账户号码
  "CYRMC": "securitiesAccountName",            // 持有人名称，根据参照表更新于2025年06月03日 16:59:14
  "ZJHM": "shareholderId",                     // 证件号码
  "GDLB": "shareholderCategory",               // 持有人类别
  "ZCYSL": "numberOfShares",                   // 总持股数量
  "PTZQZHCYSL": "sharesInCashAccount",         // 普通账户持股数量
  "XYCYSL": "sharesInMarginAccount",           // 信用账户持股数量
  "PTZQZH": "cashAccount",                     // 普通证券账户
  "XYZQZH": "marginAccount",                   // 信用证券账户
  "TXDZ": "contactAddress",                    // 通讯地址
  "LXDH": "contactNumber",                     // 联系电话
  "YZBM": "zipCode",                           // 邮政编码
  "BZ": "remarks",                             // 备注
};

/**
 * 各类型名册必须包含的字段
 * 用于验证上传的数据是否符合对应名册类型的要求
 */
export const REQUIRED_FIELDS_BY_TYPE: RequiredFields = {
  "01": [
    "YMTH",           // 一码通账户号码
    "ZQZHMC",         // 证券账户名称
    "ZJDM",           // 证件号码
    "CYRLBMS",        // 持有人类别
    "CGSL",           // 总持股数量
    "XSGSL",          // 限售股数量
    "CGBL"            // 持股比例
  ],
  "05": [
    "YMTH",           // 一码通账户号码
    "XYZHMC",       // 证券账户名称
    "XYZHZJDM",       // 证件号码
    "CYRLBMS",        // 持有人类别
    "CGSL",           // 信用账户持股数量
    "HZZQZH",         // 汇总账户号码
    "HZZHMC"          // 汇总账户名称
  ],
  "t1": [
    "YMTZHHM",        // 一码通账户号码
    "CYRMC",          // 持有人名称，更新于2025年06月03日 16:59:14
    "ZJHM",           // 证件号码
    "GDLB",           // 持有人类别
    "LTLX",           // 流通类型
    "YQLB"            // 权益类别
  ],
  "t2": [
    "YMTZHHM",        // 一码通账户号码
    "CYRMC",          // 持有人名称，更新于2025年06月03日 16:59:14
    "ZJHM",           // 证件号码
    "GDLB",           // 持有人类别
  ],
  "t3": [
    "YMTZHHM",        // 一码通账户号码
    "CYRMC",          // 持有人名称，更新于2025年06月03日 16:59:14
    "ZJHM",           // 证件号码
    "GDLB",           // 持有人类别
    "ZCYSL",          // 总持股数量
    "PTZQZHCYSL",     // 普通账户持股数量
    "XYCYSL"          // 信用账户持股数量
  ]
};

/**
 * 根据名册类型获取字段映射
 * 
 * @param registryType 名册类型
 * @returns 对应的字段映射对象
 */
export function getFieldMappingByType(registryType: RegistryType): FieldMapping {
  switch (registryType) {
    case "01": {
      return TYPE_01_FIELD_MAPPING;
    }
    case "05": {
      return TYPE_05_FIELD_MAPPING;
    }
    case "t1": {
      return TYPE_T1_FIELD_MAPPING;
    }
    case "t2": {
      return TYPE_T2_FIELD_MAPPING;
    }
    case "t3": {
      return TYPE_T3_FIELD_MAPPING;
    }
    default: {
      return {};
    }
  }
}

/**
 * 验证股东数据是否符合指定名册类型的要求
 * 
 * @param shareholders 股东数据数组
 * @param registryType 名册类型
 * @returns 验证结果对象，包含是否有效和错误信息
 */
export function validateShareholderFields(
  shareholders: any[], 
  registryType: RegistryType
): { isValid: boolean; missingFields: string[] } {
  if (!shareholders || shareholders.length === 0) {
    return { isValid: false, missingFields: ["没有股东数据"] };
  }

  // 获取该类型名册必须包含的字段
  const requiredFields = REQUIRED_FIELDS_BY_TYPE[registryType];
  if (!requiredFields) {
    return { isValid: false, missingFields: [`未知的名册类型: ${registryType}`] };
  }

  // 检查第一条股东记录是否包含所有必须字段
  const firstRecord = shareholders[0];

  const missingFields: string[] = [];

  for (const field of requiredFields) {
    if (firstRecord[field] === undefined) {
      missingFields.push(field);
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

/**
 * 将原始股东数据转换为数据库字段格式
 * 
 * @param shareholder 原始股东数据
 * @param registryType 名册类型
 * @returns 转换后的数据
 */
export function mapShareholderFields(
  shareholder: any,
  registryType: RegistryType
): Record<string, any> {
  const fieldMapping = getFieldMappingByType(registryType);
  const result: Record<string, any> = {};

  // 遍历原始股东数据的所有字段
  for (const [originalField, value] of Object.entries(shareholder)) {
    // 如果有对应的映射字段，则使用映射后的字段名
    const mappedField = fieldMapping[originalField];
    if (mappedField) {
      result[mappedField] = value;
    }
  }

  return result;
}

/**
 * 检测名册类型是否与股东数据字段匹配
 * 
 * @param shareholders 股东数据数组
 * @param expectedType 预期的名册类型
 * @returns 验证结果对象，包含是否匹配和错误信息
 */
export function verifyRegistryTypeByFields(
  shareholders: any[],
  expectedType: RegistryType
): { isMatch: boolean; actualType: RegistryType; message: string } {
  if (!shareholders || shareholders.length === 0) {
    return { 
      isMatch: false, 
      actualType: "unknown", 
      message: "没有股东数据，无法验证名册类型" 
    };
  }

  // 使用字段特征检测实际名册类型
  const actualType = detectRegistryTypeByFields(shareholders);

  // 如果实际类型与预期类型不匹配
  if (actualType !== expectedType) {
    return {
      isMatch: false,
      actualType,
      message: `名册类型不匹配：文件名表明是${expectedType}类型，但字段特征表明是${actualType}类型`
    };
  }

  return {
    isMatch: true,
    actualType,
    message: `名册类型验证通过：${expectedType}`
  };
}

/**
 * 通过股东数据字段特征检测名册类型
 * 
 * @param shareholders 股东数据数组
 * @returns 检测到的名册类型
 * @update 2025-06-04 11:03:21.838 修复t2名册检测逻辑，避免t2名册被错误识别为01类型
 */
export function detectRegistryTypeByFields(shareholders: any[]): RegistryType {
  // 检查输入有效性
  if (!shareholders || shareholders.length === 0) {
    return "unknown";
  }
  
  // 仅检查第一条记录
  const firstRecord = shareholders[0];
  
  // 检查t1名册特有字段（优先级最高）
  if (firstRecord.LTLX || firstRecord.YQLB) {
    return "t1";
  }
  
  // 检查t2名册特有字段（第二优先级）
  if (
    (firstRecord.CYRMC && firstRecord.YMTZHHM && firstRecord.ZJHM && firstRecord.GDLB && 
     !firstRecord.LTLX && !firstRecord.YQLB && !firstRecord.ZCYSL)
  ) {
    return "t2";
  }
  
  // 检查t3名册特有字段（第三优先级）
  if (
    firstRecord.ZCYSL ||
    firstRecord.PTZQZHCYSL ||
    firstRecord.XYCYSL ||
    (firstRecord.CYRMC && firstRecord.PTZQZHHM)
  ) {
    return "t3";
  }
  
  // 检查05名册特有字段（第四优先级）
  if (
    firstRecord.HZZQZH || 
    firstRecord.HZZHMC || 
    firstRecord.GFXZ ||
    firstRecord.XYZQZHMC
  ) {
    return "05";
  }
  
  // 检查01名册特有字段（最低优先级）
  if (
    firstRecord.DHHM || 
    firstRecord.YZBM || 
    firstRecord.GLGXBS || 
    firstRecord.PTZQZH || 
    firstRecord.PTZHCGSL || 
    firstRecord.XSGSL || 
    firstRecord.CGBL || 
    firstRecord.KHLB ||
    firstRecord.ZQZHMC
  ) {
    return "01";
  }
  
  // 如果无法识别，返回未知类型
  return "unknown";
} 