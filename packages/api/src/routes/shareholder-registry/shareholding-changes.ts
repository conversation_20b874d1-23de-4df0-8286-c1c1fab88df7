import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { HTTPException } from "hono/http-exception";
import { ShareholdingChangesSchema } from "./lib/validators";
import { Prisma } from "@prisma/client";


/**
 * 股东持股变化分析接口
 *
 * @update 2025-06-10 创建股东持股变化分析接口
 * @update 2025-06-11 修复最早期变动计算逻辑，确保第一个报告期变动为0
 * @update 2025-06-11 16:06:08 优化排名计算逻辑，使用SQL查询计算全局排名
 * @update 2025-06-11 16:29:29 优化查询条件逻辑，当shareholderType为"all"或空字符串时查询所有股东类别
 * @update 2025-06-11 16:37:37 优化查询性能，减少内存计算，利用数据库原生功能进行聚合和计算
 * @update 2025-06-11 16:41:35 修复Prisma groupBy查询错误，改为使用原始findMany查询方式
 * @update 2025-06-11 16:46:20 修复searchTerm搜索和排名计算问题，确保排名正确且无undefined
 * @update 2025-06-17 16:20:53 修复排名和总数计算问题，排名基于当前查询结果而非全局数据
 * @update 2025-06-17 16:32:51 添加sortType字段控制排序类型：rank(排名排序)或date(期数日期排序)
 * @update 2025-06-17 17:00:16 修复sortType逻辑，支持具体期数日期（如2024-03-29）作为排序字段
 * <AUTHOR>
 * @function 分析指定时间范围内股东持股变化情况，支持按排名、期数日期或具体日期排序
 * @path /shareholding-changes
 * @method POST
 * @time 2025-06-17 17:00:16
 */
export const shareholdingChangesRouter = new Hono()
  .post(
    "/shareholding-changes",
    authMiddleware,
    shareholderCryptoMiddleware(),
    async (c) => {
      try {
        // 获取解密后的请求数据
        const requestData = c.get("requestData");
        
        // 验证请求参数格式
        const validationResult = ShareholdingChangesSchema.safeParse(requestData);
        if (!validationResult.success) {
          throw new HTTPException(400, { 
            message: "请求参数无效", 
            cause: validationResult.error 
          });
        }
        
        const {
          organizationId,
          startDate,
          endDate,
          shareholderType,
          searchTerm,
          sortType = 'rank',
          sortOrder = 'desc',
          page = 1,
          limit = 30
        } = validationResult.data;
// console.log("requestData:", validationResult.data);
        /**
         * 1. 获取指定时间范围内的所有报告日期
         * 
         * 数据库操作说明：
         * - 查询条件：按组织ID和日期范围过滤
         * - 去重操作：使用distinct获取不重复的registerDate
         * - 字段投影：仅选择registerDate字段
         * - 排序方式：按日期升序排列
         * - 索引使用：利用organizationId和registerDate的索引
         */
        const availableDates = await db.shareholder.findMany({
          where: {
            organizationId,
            registerDate: {
              gte: new Date(startDate),
              lte: new Date(endDate)
            }
          },
          distinct: ['registerDate'],
          select: { registerDate: true },
          orderBy: { registerDate: 'asc' }
        });

        // 如果没有找到任何报告日期，返回空结果
        if (availableDates.length === 0) {
          c.set("response", {
            code: 200,
            message: "没有找到指定日期范围内的报告数据",
            data: {
              analysisRange: {
                startDate,
                endDate,
                totalPeriods: 0
              },
              availableDates: [],
              shareholderChanges: [],
              pagination: {
                page,
                limit,
                total: 0,
                totalPages: 0,
                hasNext: false,
                hasPrev: false
              }
            }
          });
          return;
        }

        // 准备排序后的日期数组
        const sortedDates = availableDates
          .map(d => d.registerDate.toISOString().split('T')[0])
          .sort();
        const firstDate = sortedDates[0];
        const lastDate = sortedDates[sortedDates.length - 1];

        /**
         * 2. 构建查询条件
         * 
         * 数据库操作说明：
         * - 优化条件构建逻辑，减少重复代码
         * - 当shareholderType为"all"或空字符串时不添加此条件，查询所有类型
         */
        const whereCondition = {
          organizationId,
          registerDate: {
            gte: new Date(startDate),
            lte: new Date(endDate)
          },
          ...(shareholderType && shareholderType !== "all" && {
            shareholderCategory: shareholderType
          }),
          ...(searchTerm && {
            OR: [
              { securitiesAccountName: { contains: searchTerm, mode: Prisma.QueryMode.insensitive } },
              { shareholderId: { contains: searchTerm, mode: Prisma.QueryMode.insensitive } },
              { unifiedAccountNumber: { contains: searchTerm, mode: Prisma.QueryMode.insensitive } }
            ]
          })
        };

        /**
         * 3. 获取所有股东在指定日期范围内的持股数据
         * 
         * 数据库操作说明：
         * - 使用findMany进行查询，避免Prisma groupBy中的复杂聚合操作
         * - 仅选择必要字段，减少数据传输量
         * - 按股东ID和日期排序，便于后续处理
         */
        const shareholderData = await db.shareholder.findMany({
          where: whereCondition,
          select: {
            shareholderId: true,
            securitiesAccountName: true,
            shareholderCategory: true,
            unifiedAccountNumber: true,
            registerDate: true,
            numberOfShares: true
          },
          orderBy: [
            { shareholderId: 'asc' },
            { registerDate: 'asc' }
          ]
        });

        
        
        // 如果没有找到任何股东数据，返回空结果
        if (shareholderData.length === 0) {
          c.set("response", {
            code: 200,
            message: "没有找到符合条件的股东数据",
            data: {
              analysisRange: {
                startDate: firstDate,
                endDate: lastDate,
                totalPeriods: sortedDates.length
              },
              availableDates: sortedDates,
              shareholderChanges: [],
              pagination: {
                page,
                limit,
                total: 0,
                totalPages: 0,
                hasNext: false,
                hasPrev: false
              }
            }
          });
          return;
        }
        
        /**
         * 4. 处理数据，构建股东持股变化记录
         * 
         * 内存操作说明：
         * - 使用Map进行股东ID到股东信息的映射，提高查找效率
         * - 预先计算股东的首末期持股，减少循环中的查找操作
         */
        const shareholderMap = new Map();

        // 按股东ID分组
        shareholderData.forEach(record => {
          if (!shareholderMap.has(record.shareholderId)) {
            shareholderMap.set(record.shareholderId, {
              shareholderId: record.shareholderId,
              shareholderName: record.securitiesAccountName,
              shareholderType: record.shareholderCategory,
              unifiedAccountNumber: record.unifiedAccountNumber,
              holdings: new Map(),
              periodChanges: []
            });
          }
          
          // 记录每个日期的持股数
          shareholderMap.get(record.shareholderId).holdings.set(
            record.registerDate.toISOString().split('T')[0],
            Number(record.numberOfShares)
          );
        });

        /**
         * 5. 计算每个股东的净变化和各期变动
         *
         * 计算逻辑说明：
         * - 日期排序：确保日期按时间顺序排列，便于计算期间变化
         * - 期初期末：获取首个和最后一个日期作为分析期间的起止点
         * - 净变化计算：期末持股减去期初持股得到净变化值
         * - 期间变动：计算每个报告期相对于上一期的持股变化
         * @update 2025-06-17 16:15:26 修复期数时间排序问题，确保periodChanges按时间升序排列
         * <AUTHOR>
         */
        const shareholderChanges = [];

        for (const [_, shareholder] of shareholderMap) {
          // 获取期初和期末持股
          const startHolding = shareholder.holdings.get(firstDate) || 0;
          const endHolding = shareholder.holdings.get(lastDate) || 0;
          const netChange = endHolding - startHolding;

          // 计算各期变动
          let prevHolding = null;

          // 遍历所有日期，为每个日期添加变动数据
          for (let i = 0; i < sortedDates.length; i++) {
            const date = sortedDates[i];
            const currentHolding = shareholder.holdings.get(date) || 0;

            // 对于第一个报告期，变动为0
            if (i === 0) {
              shareholder.periodChanges.push({
                date,
                change: 0,
                holding: currentHolding
              });
            } else {
              // 对于后续报告期，计算与上一期的变动
              const change = currentHolding - prevHolding;
              shareholder.periodChanges.push({
                date,
                change,
                holding: currentHolding
              });
            }

            prevHolding = currentHolding;
          }

          // 确保periodChanges按日期升序排列
          shareholder.periodChanges.sort((a: { date: string; change: number; holding: number }, b: { date: string; change: number; holding: number }) => {
            return new Date(a.date).getTime() - new Date(b.date).getTime();
          });

          shareholderChanges.push({
            shareholderId: shareholder.shareholderId,
            shareholderName: shareholder.shareholderName,
            shareholderType: shareholder.shareholderType,
            unifiedAccountNumber: shareholder.unifiedAccountNumber,
            netChange,
            startHolding,
            endHolding,
            periodChanges: shareholder.periodChanges
          });
        }

        /**
         * 6. 排序和分页
         *
         * 排序和分页逻辑说明：
         * - 根据sortType决定排序方式：rank(按净变化排序)、date(按最新期数日期排序)或具体日期(按指定期数排序)
         * - 先对数据进行排序，然后计算总记录数
         * - 最后按页码和每页记录数进行分页
         * @update 2025-06-12 10:18:18 区分排序和排名两个概念：排序决定数据的展示顺序；排名始终按照净变化从大到小计算
         * @update 2025-06-17 16:32:51 添加sortType控制：rank排序按净变化，date排序按期数日期
         * @update 2025-06-17 17:00:16 支持具体期数日期作为sortType，按指定期数的持股变化排序
         * <AUTHOR>
         * @time 2025-06-17 17:00:16
         */
        // 根据sortType决定排序方式
        const sortedChanges = [...shareholderChanges].sort((a, b) => {
          if (sortType === 'rank') {
            // 按净变化排序
            if (sortOrder === 'desc') {
              return b.netChange - a.netChange;
            }
            return a.netChange - b.netChange;
          }

          if (sortType === 'date') {
            // 按期数日期排序 - 使用最新的期数日期进行排序
            const aLatestDate = a.periodChanges.length > 0 ? a.periodChanges[a.periodChanges.length - 1].date : '';
            const bLatestDate = b.periodChanges.length > 0 ? b.periodChanges[b.periodChanges.length - 1].date : '';

            if (sortOrder === 'desc') {
              return new Date(bLatestDate).getTime() - new Date(aLatestDate).getTime();
            }
            return new Date(aLatestDate).getTime() - new Date(bLatestDate).getTime();
          }

          // 检查是否为具体的期数日期格式 (YYYY-MM-DD)
          if (sortType && /^\d{4}-\d{2}-\d{2}$/.test(sortType)) {
            // 按指定期数日期的持股变化排序
            const aSpecificPeriod = a.periodChanges.find((p: { date: string; change: number; holding: number }) => p.date === sortType);
            const bSpecificPeriod = b.periodChanges.find((p: { date: string; change: number; holding: number }) => p.date === sortType);

            const aChange = aSpecificPeriod ? aSpecificPeriod.change : 0;
            const bChange = bSpecificPeriod ? bSpecificPeriod.change : 0;

            if (sortOrder === 'desc') {
              return bChange - aChange;
            }
            return aChange - bChange;
          }

          // 默认按净变化排序
          if (sortOrder === 'desc') {
            return b.netChange - a.netChange;
          }
          return a.netChange - b.netChange;
        });

        // 计算总记录数
        const total = sortedChanges.length;
        const totalPages = Math.ceil(total / limit);

        /**
         * 使用基于当前查询结果的排名计算
         *
         * 数据库操作说明：
         * - 基于当前查询条件过滤后的股东进行排名计算
         * - 使用内存排序方式，确保排名与实际查询结果一致
         * - 排名始终按照净变化从大到小计算，不受sortType影响
         * - sortType只影响数据展示顺序，不影响排名计算逻辑
         * @update 2025-06-17 16:20:53 修复排名计算逻辑，基于当前查询结果进行排名而非全局排名
         * @update 2025-06-17 16:32:51 明确排名计算独立于sortType，始终按净变化计算
         * <AUTHOR>
         * @time 2025-06-17 16:32:51
         */
        // 创建一个映射来存储股东ID到其排名的映射
        const rankMap = new Map<string, number>();

        // 基于当前查询结果计算排名
        // 先按净变化从大到小排序，然后分配排名
        const rankedForRanking = [...shareholderChanges].sort((a, b) => b.netChange - a.netChange);

        // 为每个股东分配排名
        rankedForRanking.forEach((shareholder, index) => {
          rankMap.set(shareholder.shareholderId, index + 1);
        });

        // 分页处理
        const paginatedChanges = sortedChanges.slice((page - 1) * limit, page * limit);
        
        // 添加排名信息，确保处理边界情况
        const rankedChanges = paginatedChanges.map(change => {
          const rank = rankMap.get(change.shareholderId) || 0;
          return {
            ...change,
            rank
          };
        });

        /**
         * 7. 构建响应数据
         * 
         * 响应数据结构说明：
         * - 分析范围：包含起止日期和总期数
         * - 可用日期：所有报告期的日期列表
         * - 股东变化：分页后的股东持股变化数据
         * - 分页信息：当前页码、每页记录数、总记录数、总页数等
         */
        const responseData = {
          analysisRange: {
            startDate: firstDate,
            endDate: lastDate,
            totalPeriods: sortedDates.length
          },
          availableDates: sortedDates,
          shareholderChanges: rankedChanges,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        };
        // console.log("排名修复验证 - responseData1:", responseData.shareholderChanges[0]);
        // console.log("排名修复验证 - responseData2:", responseData.shareholderChanges[1]);
        // console.log("总数修复验证 - total:", total, "totalPages:", totalPages);
        // 设置响应数据
        c.set("response", {
          code: 200,
          message: "获取股东持股变化分析数据成功",
          data: responseData
        });
        
        return;
      } catch (error) {
        // 错误处理逻辑
        if (error instanceof HTTPException) {
          c.set("response", {
            code: error.status,
            message: error.message,
            data: null
          });
          return;
        }
        
        // console.error("获取股东持股变化分析数据失败:", error);
        c.set("response", {
          code: 500,
          message: "获取股东持股变化分析数据失败",
          data: null
        });
        return;
      }
    }
  ); 

