import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type { UploadRegistryRequest } from "../types";
import { processShareholderUpdateFields } from "../lib/utils";
import { 
  validateOrganizationAndRegistry, 
  handleCompanyBinding, 
  handleRegistryRecord, 
  processShareholders,
  updateRecordCount
} from "../lib/registry-handlers";
import {
  verifyRegistryTypeByFields,
  mapShareholderFields,
  validateShareholderFields
} from "../lib/field-mapping";
import { ShareholderRegistryConfig } from "../lib/config";

/**
 * 处理深市01名册上传
 * 
 * 功能：
 * 1. 验证组织存在性和名册类型合法性
 * 2. 验证上传的名册字段是否符合01类型特征
 * 3. 处理组织与公司的绑定关系
 * 4. 创建或更新名册记录
 * 5. 批量处理股东数据
 * 6. 更新实际股东数量
 * 
 * 更新记录:
 * - 2025-06-04 17:15:28: 使用配置文件中的固定超时时间，而非动态计算
 * 
 * @param data 请求数据
 * @param userId 用户ID
 * @returns 处理后的名册记录
 */
export async function upload01Handler(
  data: UploadRegistryRequest,
  userId: string
) {
  // 使用配置文件中的固定超时时间
  const transactionTimeout = ShareholderRegistryConfig.transactionTimeout;
  
  // 验证上传的股东数据字段是否符合01类型特征
  const fieldValidation = validateShareholderFields(data.shareholders, "01");
  if (!fieldValidation.isValid) {
    throw new HTTPException(400, { 
      message: `上传的股东数据缺少01类型名册必要字段: ${fieldValidation.missingFields.join(", ")}`
    });
  }
  
  // 验证上传的名册类型是否与字段特征匹配
  const typeVerification = verifyRegistryTypeByFields(data.shareholders, "01");
  if (!typeVerification.isMatch) {
    throw new HTTPException(400, { 
      message: typeVerification.message
    });
  }
  
  // 使用事务处理所有数据库操作
  return db.$transaction(async (tx) => {
    // 1. 验证组织和名册类型
    const { organization, existingRegistry } = await validateOrganizationAndRegistry(
      tx, 
      data, 
      "01"
    );

    // 2. 处理组织元数据
    const metadata = organization.metadata
      ? JSON.parse(organization.metadata)
      : {};

    // 3. 处理组织与公司的绑定关系
    await handleCompanyBinding(
      tx,
      data.organizationId,
      metadata,
      data.companyCode,
      data.companyInfo.companyName || ""
    );

    // 4. 处理名册记录
    const { registry, isNewRegistry } = await handleRegistryRecord(
      tx,
      data,
      existingRegistry,
      userId
    );

    // 5. 处理股东数据
    // 定义自定义更新处理函数
    const processUpdateFn = (shareholder: any, existingShareholder: any) => {
      const updateData: Record<string, unknown> = {
        id: existingShareholder.id,
        shareholderId: existingShareholder.shareholderId,
      };

      // 使用辅助函数处理字段更新逻辑
      const shouldUpdate = processShareholderUpdateFields(
        updateData,
        existingShareholder,
        shareholder,
        "01"
      );

      return shouldUpdate ? updateData : null;
    };

    // 定义自定义创建处理函数 - 使用字段映射
    const processCreateFn = (shareholder: any) => {
      // 使用字段映射将原始字段转换为数据库字段
      const mappedData = mapShareholderFields(shareholder, "01");
      
      // 添加必要的关联字段
      return {
        ...mappedData,
        registryId: registry.id,
        organizationId: data.organizationId,
        registerDate: new Date(data.registerDate)
      };
    };

    // 批量处理股东数据
    await processShareholders(
      tx,
      data,
      registry,
      "01",
      processUpdateFn,
      processCreateFn
    );

    // 6. 更新名册记录数
    if (!isNewRegistry) {
      await updateRecordCount(tx, registry, data);
    }

    return registry;
  }, {
    timeout: transactionTimeout,
    isolationLevel: "ReadCommitted",
  });
} 