import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type { UploadRegistryRequest } from "../types";
import { processShareholderUpdateFields, processShareholdingAmountMerge } from "../lib/utils";
import { 
  validateOrganizationAndRegistry, 
  handleCompanyBinding, 
  handleRegistryRecord, 
  updateRecordCount
} from "../lib/registry-handlers";
import {
  verifyRegistryTypeByFields,
  mapShareholderFields,
  validateShareholderFields
} from "../lib/field-mapping";
import { ShareholderRegistryConfig } from "../lib/config";

/**
 * 处理沪市t1/t2/t3名册合并
 * 
 * 功能：
 * 1. 验证组织存在性和名册类型合法性
 * 2. 验证上传的名册字段是否符合t1、t2或t3类型特征
 * 3. 检查t1/t2/t3名册合并情况
 * 4. 创建或更新名册记录
 * 5. 批量处理股东数据，合并沪市特有字段
 * 6. 更新实际股东数量
 * 
 * 更新记录:
 * - 2025-06-04 14:58:43: 使用配置文件中的固定超时时间和批量大小，而非动态计算
 * - 2025-06-04 15:09:18: 修复当T3名册合并到T2名册时，机构总数和机构总持股被覆盖为0的问题
 * 
 * @param data 请求数据
 * @param userId 用户ID
 * @returns 处理后的名册记录
 */
export async function mergeT1T2T3Handler(
  data: UploadRegistryRequest,
  userId: string
) {
  // 使用配置文件中的固定超时时间
  const transactionTimeout = ShareholderRegistryConfig.transactionTimeout;
  
  // 确定当前上传的名册类型
  const lowerFileName = data.fileName.toLowerCase();
  
  // 使用startsWith方法检测名册类型，与detectRegistryTypeByFileName保持一致
  // 更新于2025-06-04 13:41:02: 修复对t3开头文件名的识别问题
  const isTypeT1 = lowerFileName.startsWith('t1');
  const isTypeT2 = lowerFileName.startsWith('t2');
  const isTypeT3 = lowerFileName.startsWith('t3');
  
  // 如果上面的精确匹配失败，使用传统的关键字匹配作为备选方案
  const hasT1 = !isTypeT1 && !isTypeT2 && !isTypeT3 && lowerFileName.includes("t1");
  const hasT2 = !isTypeT1 && !isTypeT2 && !isTypeT3 && lowerFileName.includes("t2");
  const hasT3 = !isTypeT1 && !isTypeT2 && !isTypeT3 && lowerFileName.includes("t3");
  
  // 如果无法确定类型，抛出错误
  if (!isTypeT1 && !isTypeT2 && !isTypeT3 && !hasT1 && !hasT2 && !hasT3) {
    throw new HTTPException(400, { 
      message: "无法确定名册类型，请检查文件名是否包含t1/t2/t3标识"
    });
  }
  
  // 确定当前上传的名册类型字符串
  let currentType = "t1"; // 默认值
  switch (true) {
    case isTypeT1 || hasT1: {
      currentType = "t1";
      break;
    }
    case isTypeT2 || hasT2: {
      currentType = "t2";
      break;
    }
    case isTypeT3 || hasT3: {
      currentType = "t3";
      break;
    }
  }

  // 验证上传的股东数据字段是否符合当前类型特征
  const fieldValidation = validateShareholderFields(data.shareholders, currentType as any);
  if (!fieldValidation.isValid) {
    throw new HTTPException(400, { 
      message: `上传的股东数据缺少${currentType}类型名册必要字段: ${fieldValidation.missingFields.join(", ")}`
    });
  }
  
  // 验证上传的名册类型是否与字段特征匹配
  const typeVerification = verifyRegistryTypeByFields(data.shareholders, currentType as any);
  if (!typeVerification.isMatch) {
    throw new HTTPException(400, { 
      message: typeVerification.message
    });
  }

  // 使用事务处理所有数据库操作
  return db.$transaction(async (tx) => {
    // 1. 验证组织和名册类型
    const { organization, existingRegistry } = await validateOrganizationAndRegistry(
      tx, 
      data, 
      currentType as any
    );

    // 确保存在同日期名册
    if (!existingRegistry) {
      throw new HTTPException(400, { 
        message: "未找到同日期名册，无法执行合并操作"
      });
    }

    // 检查现有名册类型
    const existingFileName = existingRegistry.fileName.toLowerCase();
    
    // 更新于2025-06-04 13:54:21: 修复对t2和t3名册的错误识别问题
    // 使用精确的startsWith匹配，避免t3名册被错误识别为包含t2
    let hasT1 = existingFileName.startsWith('t1');
    let hasT2 = existingFileName.startsWith('t2');
    let hasT3 = existingFileName.startsWith('t3');
    
    // 如果精确匹配失败，则使用includes作为备选方案，但要确保不会误判
    // 例如：t3开头的文件不应该被认为包含t1或t2
    if (!hasT1 && !hasT2 && !hasT3) {
      // 只有在没有找到精确匹配时，才使用includes方法
      if (existingFileName.includes("t1")) {
        hasT1 = true;
      } else if (existingFileName.includes("t2")) {
        hasT2 = true;
      } else if (existingFileName.includes("t3")) {
        hasT3 = true;
      }
    }
    
    // 验证合并条件 - 确保不是重复上传同类型
    if ((isTypeT1 && hasT1) || (isTypeT2 && hasT2) || (isTypeT3 && hasT3)) {
      throw new HTTPException(400, { 
        message: `DUPLICATE_REGISTRY_TYPE:已存在${currentType}类型名册，不能重复上传`
      });
    }
    
    // 如果已同时存在t1、t2和t3名册，不能继续上传
    if (hasT1 && hasT2 && hasT3) {
      throw new HTTPException(400, { 
        message: "COMPLETE_REGISTRY:已存在t1、t2和t3类型名册的合并数据，不能继续上传"
      });
    }

    // 2. 处理组织元数据
    const metadata = organization.metadata
      ? JSON.parse(organization.metadata)
      : {};

    // 3. 处理组织与公司的绑定关系
    await handleCompanyBinding(
      tx,
      data.organizationId,
      metadata,
      data.companyCode,
      data.companyInfo.companyName || ""
    );

    // 4. 处理名册记录
    const { registry, isNewRegistry } = await handleRegistryRecord(
      tx,
      data,
      existingRegistry,
      userId
    );

    // 5. 处理股东数据
    // 在循环外先对所有股东数据进行字段映射，提高效率
    const mappedShareholders = data.shareholders.map(shareholder => 
      mapShareholderFields(shareholder, currentType as any)
    );

    // 定义自定义更新处理函数 - 使用映射后的数据
    const processUpdateFn = (mappedShareholder: any, existingShareholder: any) => {
      const updateData: Record<string, unknown> = {
        id: existingShareholder.id,
        shareholderId: existingShareholder.shareholderId,
      };

      // 1. 处理特有字段更新 - 按照"先到先得"原则
      const hasFieldUpdates = processShareholderUpdateFields(
        updateData,
        existingShareholder,
        mappedShareholder,
        currentType as any
      );

      // 2. 处理持股数量合并 - 按照"先到先得"原则
      const amountMergeData = processShareholdingAmountMerge(
        existingShareholder,
        mappedShareholder
      );

      // 合并两个更新数据对象
      Object.assign(updateData, amountMergeData);
      
      // 只有当有字段需要更新时才返回更新数据
      const hasUpdates = hasFieldUpdates || Object.keys(amountMergeData).length > 0;
      return hasUpdates ? updateData : null;
    };

    // 定义自定义创建处理函数 - 使用映射后的数据
    const processCreateFn = (mappedData: any) => {
      // 添加必要的关联字段和默认值
      return {
        ...mappedData,
        registryId: registry.id,
        organizationId: data.organizationId,
        registerDate: new Date(data.registerDate),
        // 设置默认值，确保数值字段不为null
        numberOfShares: mappedData.numberOfShares || "0",
        lockedUpShares: mappedData.lockedUpShares || "0",
        shareholdingRatio: mappedData.shareholdingRatio || "0",
        frozenShares: mappedData.frozenShares || "0"
      };
    };

    // 准备查询条件 - 提取所有股东ID和一码通账号用于批量查询
    const shareholderIds = mappedShareholders
      .map((s) => s.shareholderId)
      .filter((id): id is string => id !== undefined && id !== null)
      .map(id => String(id));
    
    const unifiedAccountNumbers = mappedShareholders
      .map((s) => s.unifiedAccountNumber)
      .filter((num): num is string => num !== undefined && num !== null)
      .map(num => String(num));

    // 构建查询条件
    const existingShareholders = await tx.shareholder.findMany({
      where: {
        registryId: registry.id,
        organizationId: data.organizationId,
        registerDate: new Date(data.registerDate),
        OR: [
          { shareholderId: { in: shareholderIds } },
          {
            AND: [
              { unifiedAccountNumber: { in: unifiedAccountNumbers } },
              { shareholderId: { in: shareholderIds } }
            ]
          }
        ]
      }
    });

    // 构建高效查找映射
    const shareholderMap = new Map();
    for (const shareholder of existingShareholders) {
      // 使用"一码通账号_股东ID"作为复合键
      const lookupKey = `${shareholder.unifiedAccountNumber}_${shareholder.shareholderId}`;
      shareholderMap.set(lookupKey, shareholder);
    }

    // 准备批量创建和更新的数据集合
    const shareholdersToCreate: Array<Record<string, unknown>> = [];
    const shareholdersToUpdate: Array<Record<string, unknown>> = [];

    // 股东记录处理循环 - 使用映射后的数据
    for (const mappedData of mappedShareholders) {
      // 确保关键字段存在
      if (!mappedData.shareholderId || !mappedData.unifiedAccountNumber) {
        continue;
      }
      
      // 确保ID和账号非空并转换为字符串
      const shareholderId = String(mappedData.shareholderId);
      const unifiedAccountNumber = String(mappedData.unifiedAccountNumber);
      
      // 使用复合键查找现有记录
      const lookupKey = `${unifiedAccountNumber}_${shareholderId}`;
      const existingShareholder = shareholderMap.get(lookupKey);

      if (existingShareholder) {
        // 更新逻辑 - 使用自定义处理函数
        const updateData = processUpdateFn(mappedData, existingShareholder);
        if (updateData) {
          shareholdersToUpdate.push(updateData);
        }
      } else {
        // 创建逻辑 - 使用自定义处理函数
        const createData = processCreateFn(mappedData);
        shareholdersToCreate.push(createData);
      }
    }

    // 批量创建股东记录 - 使用配置文件中的固定批次大小
    if (shareholdersToCreate.length > 0) {
      // 使用配置文件中的批次大小
      const batchSize = ShareholderRegistryConfig.batchSize;
      
      // 分批执行创建操作
      for (let i = 0; i < shareholdersToCreate.length; i += batchSize) {
        const batch = shareholdersToCreate.slice(i, i + batchSize);
        await tx.shareholder.createMany({
          data: batch as any[],
          skipDuplicates: true,
        });
      }
    }

    // 批量更新股东记录 - 使用配置文件中的固定并发限制
    if (shareholdersToUpdate.length > 0) {
      // 使用配置文件中的并发限制
      const concurrencyLimit = ShareholderRegistryConfig.concurrencyLimit;

      // 分批并行处理更新
      for (let i = 0; i < shareholdersToUpdate.length; i += concurrencyLimit) {
        const updateBatch = shareholdersToUpdate.slice(i, i + concurrencyLimit);

        // 并行执行当前批次的更新操作
        await Promise.all(
          updateBatch.map((updateData) => {
            const { id, shareholderId, ...data } = updateData;
            return tx.shareholder.update({
              where: {
                shareholderId_id: {
                  shareholderId: shareholderId as string,
                  id: id as string,
                },
              },
              data,
            });
          })
        );
      }
    }

    // 6. 更新名册记录数
    if (!isNewRegistry) {
      await updateRecordCount(tx, registry, data);
    }

    return registry;
  }, {
    timeout: transactionTimeout,
    isolationLevel: "ReadCommitted",
  });
} 