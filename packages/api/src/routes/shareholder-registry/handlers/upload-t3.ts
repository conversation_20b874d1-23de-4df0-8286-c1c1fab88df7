import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type { UploadRegistryRequest } from "../types";
import { processShareholderUpdateFields, processShareholdingAmountMerge, isT3FullRegistry } from "../lib/utils";
import {
  validateOrganizationAndRegistry,
  handleCompanyBinding,
  handleRegistryRecord,
  processShareholders,
  updateRecordCount
} from "../lib/registry-handlers";
import {
  verifyRegistryTypeByFields,
  mapShareholderFields,
  validateShareholderFields
} from "../lib/field-mapping";
import { ShareholderRegistryConfig } from "../lib/config";

/**
 * 处理沪市t3名册上传
 * 
 * 功能：
 * 1. 验证组织存在性和名册类型合法性
 * 2. 验证上传的名册字段是否符合t3类型特征
 * 3. 处理组织与公司的绑定关系
 * 4. 创建或更新名册记录
 * 5. 批量处理股东数据，包含沪市特有字段
 * 6. 更新实际股东数量
 * 
 * 更新记录:
 * - 2025-06-04 17:15:28: 使用配置文件中的固定超时时间，而非动态计算
 * 
 * @param data 请求数据
 * @param userId 用户ID
 * @returns 处理后的名册记录
 */
export async function uploadT3Handler(
  data: UploadRegistryRequest,
  userId: string
) {
  // 使用配置文件中的固定超时时间
  const transactionTimeout = ShareholderRegistryConfig.transactionTimeout;
  
  // 验证上传的股东数据字段是否符合t3类型特征
  const fieldValidation = validateShareholderFields(data.shareholders, "t3");
  if (!fieldValidation.isValid) {
    throw new HTTPException(400, { 
      message: `上传的股东数据缺少t3类型名册必要字段: ${fieldValidation.missingFields.join(", ")}`
    });
  }
  
  // 验证上传的名册类型是否与字段特征匹配
  const typeVerification = verifyRegistryTypeByFields(data.shareholders, "t3");
  if (!typeVerification.isMatch) {
    throw new HTTPException(400, { 
      message: typeVerification.message
    });
  }
  
  // 检测是否为T3全量名册
  const isFullRegistry = isT3FullRegistry(data.fileName);

  // 使用事务处理所有数据库操作
  return db.$transaction(async (tx) => {
    // 1. 验证组织和名册类型
    const { organization, existingRegistry } = await validateOrganizationAndRegistry(
      tx,
      data,
      "t3"
    );

    // 2. T3全量替换逻辑处理 - 2025-06-13 12:02:02 hayden 添加
    if (isFullRegistry && existingRegistry) {
      // 检查是否存在同期T3前200版本名册
      const existingT3Registry = await tx.shareholderRegistry.findFirst({
        where: {
          organizationId: data.organizationId,
          companyCode: data.companyCode,
          registerDate: new Date(data.registerDate),
          fileName: {
            contains: "t3",
            not: {
              contains: "all"
            }
          }
        },
        include: {
          companyInfo: true
        }
      });

      if (existingT3Registry) {
        // 删除原有T3前200版本的股东数据
        await tx.shareholder.deleteMany({
          where: {
            registryId: existingT3Registry.id,
            organizationId: data.organizationId,
            registerDate: new Date(data.registerDate)
          }
        });

        // 更新CompanyInfo中的信用数据为全量数据
        if (existingT3Registry.companyInfo.length > 0) {
          await tx.companyInfo.updateMany({
            where: {
              registryId: existingT3Registry.id,
              organizationId: data.organizationId,
            },
            data: {
              marginAccounts: data.companyInfo.marginAccounts || 0,
              marginShares: data.companyInfo.marginShares || "0",
            },
          });
        }

        // 更新文件名以标识为全量版本
        await tx.shareholderRegistry.update({
          where: { id: existingT3Registry.id },
          data: {
            fileName: data.fileName,
            recordCount: data.recordCount
          }
        });

        // 使用现有的registry继续处理
        const registry = existingT3Registry;

        // 处理股东数据（使用简化的创建逻辑，因为已删除旧数据）
        const processCreateFn = (shareholder: any) => {
          const mappedData = mapShareholderFields(shareholder, "t3");
          return {
            ...mappedData,
            registryId: registry.id,
            organizationId: data.organizationId,
            registerDate: new Date(data.registerDate),
            numberOfShares: mappedData.numberOfShares || "0",
            lockedUpShares: mappedData.lockedUpShares || "0",
            shareholdingRatio: mappedData.shareholdingRatio || "0",
            frozenShares: mappedData.frozenShares || "0"
          };
        };

        // 批量创建新的股东数据
        await processShareholders(
          tx,
          data,
          registry,
          "t3",
          undefined, // 不需要更新函数，因为已删除旧数据
          processCreateFn
        );

        return registry;
      }
    }

    // 3. 处理组织元数据
    const metadata = organization.metadata
      ? JSON.parse(organization.metadata)
      : {};

    // 4. 处理组织与公司的绑定关系
    await handleCompanyBinding(
      tx,
      data.organizationId,
      metadata,
      data.companyCode,
      data.companyInfo.companyName || ""
    );

    // 5. 处理名册记录
    const { registry, isNewRegistry } = await handleRegistryRecord(
      tx,
      data,
      existingRegistry,
      userId
    );

    // 6. 处理股东数据
    // 定义自定义更新处理函数
    const processUpdateFn = (shareholder: any, existingShareholder: any) => {
      const updateData: Record<string, unknown> = {
        id: existingShareholder.id,
        shareholderId: existingShareholder.shareholderId,
      };

      // 处理特有字段更新
      const shouldUpdate = processShareholderUpdateFields(
        updateData,
        existingShareholder,
        shareholder,
        "t3"
      );

      // 处理持股数量合并
      const amountMergeData = processShareholdingAmountMerge(
        existingShareholder,
        shareholder
      );

      // 合并两个更新数据对象
      const mergedUpdateData = { ...updateData, ...amountMergeData };
      const hasUpdates = shouldUpdate || Object.keys(amountMergeData).length > 0;

      return hasUpdates ? mergedUpdateData : null;
    };

    // 定义自定义创建处理函数 - 使用字段映射
    const processCreateFn = (shareholder: any) => {
      // 使用字段映射将原始字段转换为数据库字段
      const mappedData = mapShareholderFields(shareholder, "t3");
      
      // 添加必要的关联字段
      return {
        ...mappedData,
        registryId: registry.id,
        organizationId: data.organizationId,
        registerDate: new Date(data.registerDate),
        // 设置默认值，确保数值字段不为null
        numberOfShares: mappedData.numberOfShares || "0",
        lockedUpShares: mappedData.lockedUpShares || "0",
        shareholdingRatio: mappedData.shareholdingRatio || "0",
        frozenShares: mappedData.frozenShares || "0"
      };
    };

    // 批量处理股东数据
    await processShareholders(
      tx,
      data,
      registry,
      "t3",
      processUpdateFn,
      processCreateFn
    );

    // 7. 更新名册记录数
    if (!isNewRegistry) {
      await updateRecordCount(tx, registry, data);
    }

    return registry;
  }, {
    timeout: transactionTimeout,
    isolationLevel: "ReadCommitted",
  });
} 