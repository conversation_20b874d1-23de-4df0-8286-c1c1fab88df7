import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { DeleteRegistrySchema, BatchDeleteRegistrySchema } from "./lib/validators";
import { successResponse, errorResponse, handleHttpException } from "./lib/utils";

/**
 * 删除股东名册路由
 * 
 * 功能：删除指定的股东名册记录，同时会级联删除关联的公司信息和股东信息
 * 路径：/delete
 * 方法：POST
 * 中间件：
 * - authMiddleware: 验证用户身份和权限
 * - shareholderCryptoMiddleware: 处理请求和响应的加解密
 * 
 * 请求参数：
 * - registryId: 股东名册ID(必填)
 * 
 * 响应数据：
 * - 成功：返回null，状态码200
 * - 失败：返回错误信息
 */
export const deleteRouter = new Hono()
  .post(
    "/delete",
    authMiddleware,  // 验证用户是否登录
    shareholderCryptoMiddleware(),  // 处理加解密
    async (c) => {
      try {
        // 从context中获取解密后的请求体
        const requestData = c.get("requestData");
        
        /**
         * 验证请求参数
         * 使用Zod验证器检查参数有效性
         */
        const parsed = DeleteRegistrySchema.safeParse(requestData);
        if (!parsed.success) {
          errorResponse(  
            c, 
            `请求参数验证失败：${parsed.error.errors[0]?.message}`, 
            400, 
            "VALIDATION_ERROR"
          );
          return;
        }
        
        // 从验证通过的数据中获取股东名册ID
        const { registryId } = parsed.data;

        /**
         * 查询股东名册记录是否存在
         * 
         * 数据库操作说明：
         * 1. findUnique: 根据主键查询单条记录
         * 2. where: 指定查询条件，使用ID作为唯一标识
         * 3. select: 只选择需要的字段，减少数据传输量
         */
        const registry = await db.shareholderRegistry.findUnique({
          where: { id: registryId },
          select: {
            id: true,
            organizationId: true,
            companyCode: true
          }
        });

        // 检查记录是否存在
        if (!registry) {
          errorResponse(
            c, 
            "未找到指定的股东名册记录", 
            404, 
            "RESOURCE_NOT_FOUND"
          );
          return;
        }

        // 获取用户信息，用于记录操作日志
        const user = c.get("user");
        const userId = user.id;
        
        /**
         * 检查该组织下是否还有其他同公司代码的股东名册
         * 如果这是最后一份名册，则需要清除组织的metadata中的绑定信息
         */
        const remainingRegistries = await db.shareholderRegistry.count({
          where: {
            organizationId: registry.organizationId,
            companyCode: registry.companyCode,
            id: {
              not: registryId // 排除当前要删除的记录
            }
          }
        });
        
        // 如果是最后一份名册，需要清除组织metadata中的绑定信息
        if (remainingRegistries === 0) {
          // 查询组织信息
          const organization = await db.organization.findUnique({
            where: { id: registry.organizationId },
            select: { metadata: true }
          });
          
          if (organization?.metadata) {
            // 解析现有的metadata
            const metadata = JSON.parse(organization.metadata);
            
            // 如果存在绑定信息且公司代码匹配，则清除绑定信息
            if (metadata.boundCompanyCode === registry.companyCode) {
              // 创建新的metadata对象，不包含绑定信息
              const newMetadata = { ...metadata };
              // 移除绑定信息
              newMetadata.boundCompanyCode = undefined;
              newMetadata.boundCompanyName = undefined;
              newMetadata.boundAt = undefined;
              
              // 过滤掉undefined值
              const filteredMetadata = Object.fromEntries(
                Object.entries(newMetadata).filter(([_, v]) => v !== undefined)
              );
              
              // 更新组织的metadata
              await db.organization.update({
                where: { id: registry.organizationId },
                data: { 
                  metadata: Object.keys(filteredMetadata).length > 0 
                    ? JSON.stringify(filteredMetadata) 
                    : null 
                }
              });
            }
          }
        }

        /**
         * 执行删除操作
         * 
         * 数据库操作说明：
         * 1. delete: 删除单条记录
         * 2. where: 指定删除条件，使用ID作为唯一标识
         * 3. 级联删除说明：
         *    - 数据库模型中配置了级联删除关系(onDelete: Cascade)
         *    - 删除股东名册记录会自动删除关联的以下数据：
         *      a. companyInfo表中相关记录(通过registryId外键关联)
         *      b. shareholder表中相关记录(通过registryId外键关联)
         *    - 不需要手动删除关联记录，数据库会自动处理关联关系
         */
        await db.shareholderRegistry.delete({
          where: { id: registryId }
        });

        // 使用成功响应函数，返回成功消息
        successResponse(c, "股东名册已成功删除", null);

        return;
      } catch (error) {
        /**
         * 错误处理
         * 
         * 通过统一的异常处理函数处理以下类型的错误：
         * 1. 数据库操作错误(如外键约束、连接失败等)
         * 2. 权限错误
         * 3. 其他未预期的错误
         */
        handleHttpException(c, error);
        return;
      }
    }
  )
  /**
   * 批量删除股东名册路由
   * 
   * 功能：批量删除多个股东名册记录，同时会级联删除关联的公司信息和股东信息
   * 路径：/batch-delete
   * 方法：POST
   * 中间件：
   * - authMiddleware: 验证用户身份和权限
   * - shareholderCryptoMiddleware: 处理请求和响应的加解密
   * 
   * 请求参数：
   * - registryIds: 股东名册ID数组(必填，至少包含一个ID)
   * 
   * 响应数据：
   * - 成功：返回删除成功和失败的统计信息，状态码200
   * - 失败：返回错误信息
   */
  .post(
    "/batch-delete",
    authMiddleware,  // 验证用户是否登录
    shareholderCryptoMiddleware(),  // 处理加解密
    async (c) => {
      try {
        // 从context中获取解密后的请求体
        const requestData = c.get("requestData");
        
        /**
         * 验证请求参数
         * 使用Zod验证器检查参数有效性
         */
        const parsed = BatchDeleteRegistrySchema.safeParse(requestData);
        if (!parsed.success) {
          errorResponse(  
            c, 
            `请求参数验证失败：${parsed.error.errors[0]?.message}`, 
            400, 
            "VALIDATION_ERROR"
          );
          return;
        }
        
        // 从验证通过的数据中获取股东名册ID数组
        const { registryIds } = parsed.data;

        // 获取用户信息，用于记录操作日志
        const user = c.get("user");
        const userId = user.id;

        /**
         * 查询所有指定ID的股东名册记录
         * 
         * 数据库操作说明：
         * 1. findMany: 查询多条记录
         * 2. where: 使用in操作符匹配多个ID
         * 3. select: 只选择需要的字段
         */
        const existingRegistries = await db.shareholderRegistry.findMany({
          where: { 
            id: { 
              in: registryIds 
            } 
          },
          select: {
            id: true,
            organizationId: true,
            companyCode: true
          }
        });

        // 提取存在的记录ID
        const existingIds = existingRegistries.map(reg => reg.id);
        
        // 找出不存在的记录ID
        const nonExistingIds = registryIds.filter(id => !existingIds.includes(id));

        // 如果没有任何存在的记录，返回错误
        if (existingIds.length === 0) {
          errorResponse(
            c, 
            "未找到任何指定的股东名册记录", 
            404, 
            "RESOURCE_NOT_FOUND"
          );
          return;
        }
        
        // 收集组织和公司代码的映射关系，用于检查是否需要清除metadata
        const orgCompanyMap = new Map();
        
        // 填充映射
        existingRegistries.forEach(registry => {
          if (!orgCompanyMap.has(registry.organizationId)) {
            orgCompanyMap.set(registry.organizationId, new Map());
          }
          
          const companyMap = orgCompanyMap.get(registry.organizationId);
          if (!companyMap.has(registry.companyCode)) {
            companyMap.set(registry.companyCode, []);
          }
          
          companyMap.get(registry.companyCode).push(registry.id);
        });
        
        // 需要检查和更新metadata的组织列表
        const metadataUpdates = [];
        
        // 检查每个组织和公司代码组合，确定是否需要清除metadata
        for (const [orgId, companyMap] of orgCompanyMap.entries()) {
          for (const [companyCode, regIds] of companyMap.entries()) {
            // 查询该组织下是否还有其他同公司代码的股东名册
            const remainingCount = await db.shareholderRegistry.count({
              where: {
                organizationId: orgId,
                companyCode: companyCode,
                id: {
                  notIn: regIds // 排除当前要删除的记录
                }
              }
            });
            
            // 如果删除后将没有剩余名册，则添加到更新列表
            if (remainingCount === 0) {
              metadataUpdates.push({ orgId, companyCode });
            }
          }
        }

        /**
         * 执行批量删除操作
         * 
         * 数据库操作说明：
         * 1. deleteMany: 删除多条记录
         * 2. where: 使用in操作符匹配多个ID
         * 3. 级联删除同样适用
         */
        const deleteResult = await db.shareholderRegistry.deleteMany({
          where: { 
            id: { 
              in: existingIds 
            } 
          }
        });
        
        // 处理需要更新metadata的组织
        for (const { orgId, companyCode } of metadataUpdates) {
          // 查询组织信息
          const organization = await db.organization.findUnique({
            where: { id: orgId },
            select: { metadata: true }
          });
          
          if (organization?.metadata) {
            // 解析现有的metadata
            const metadata = JSON.parse(organization.metadata);
            
            // 如果存在绑定信息且公司代码匹配，则清除绑定信息
            if (metadata.boundCompanyCode === companyCode) {
              // 创建新的metadata对象，不包含绑定信息
              const newMetadata = { ...metadata };
              // 移除绑定信息
              newMetadata.boundCompanyCode = undefined;
              newMetadata.boundCompanyName = undefined;
              newMetadata.boundAt = undefined;
              
              // 过滤掉undefined值
              const filteredMetadata = Object.fromEntries(
                Object.entries(newMetadata).filter(([_, v]) => v !== undefined)
              );
              
              // 更新组织的metadata
              await db.organization.update({
                where: { id: orgId },
                data: { 
                  metadata: Object.keys(filteredMetadata).length > 0 
                    ? JSON.stringify(filteredMetadata) 
                    : null 
                }
              });
            }
          }
        }

        // 构建响应数据
        const responseData = {
          success: {
            count: deleteResult.count,
            ids: existingIds
          },
          failed: {
            count: nonExistingIds.length,
            ids: nonExistingIds
          },
          total: registryIds.length
        };

        // 使用成功响应函数，返回批量删除结果
        successResponse(c, `已成功删除 ${deleteResult.count} 个股东名册`, responseData);

        return;
      } catch (error) {
        /**
         * 错误处理
         * 
         * 通过统一的异常处理函数处理各类错误
         */
        handleHttpException(c, "删除失败或联系管理员");
        return;
      }
    }
  ); 