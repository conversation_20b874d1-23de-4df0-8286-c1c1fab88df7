import { Hono } from "hono";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { shareholderCryptoMiddleware } from "../../middleware/shareholder-crypto";
import { formatRegisterDate } from "./lib/utils";
import { RegisterDatesSchema } from "./lib/validators";

/**
 * 期数日期查询响应类型
 */
type RegisterDatesResponse = {
	registerDates: {
		registerDate: string;
		companyCode: string;
	}[];
};

/**
 * 期数日期查询路由
 * 用于获取股东名册的所有报告期日期
 */
export const registerDatesRouter = new Hono().post(
  "/register-dates",
  authMiddleware,  // 验证用户是否登录
  shareholderCryptoMiddleware(),  // 处理加解密
  async (c) => {
    try {
      // 从context中获取解密后的请求体
      const requestData = c.get("requestData");
      
      // 验证请求数据
      const parsed = RegisterDatesSchema.safeParse(requestData);
      if (!parsed.success) {
        c.set("response", {
          code: 400,
          message: `请求参数验证失败：${parsed.error.errors[0]?.message}`,
          data: null
        });
        return;
      }
      
      const { organizationId, companyCode } = parsed.data;

      // 构建查询条件
      const where = {
        organizationId,
        ...(companyCode ? { companyCode } : {})
      };

      // 查询所有不重复的报告日期和公司代码
      const registerDates = await db.shareholderRegistry.findMany({
        where,
        select: {
          registerDate: true,
          companyCode: true
        },
        orderBy: {
          registerDate: 'desc'
        },
        distinct: ['registerDate', 'companyCode']
      });

      // 转换查询结果为响应格式
      const responseData: RegisterDatesResponse = {
        registerDates: registerDates.map(item => ({
          registerDate: formatRegisterDate(item.registerDate),
          companyCode: item.companyCode
        }))
      };

      // 设置响应，中间件会自动处理加密
      c.set("response", {
        code: 200,
        message: "获取期数日期列表成功",
        data: responseData
      });

      return;
    } catch (error) {
      // 处理错误情况
      console.error("[股东名册-期数日期查询] 错误:", error);
      c.set("response", {
        code: 500,
        message: "获取期数日期列表失败",
        data: null
      });
      return;
    }
  }
); 