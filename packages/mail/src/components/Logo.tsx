import React from "react";

export function Logo({
	withLabel = true,
}: {
	withLabel?: boolean;
}) {
	return (
		<span className="flex items-center font-semibold text-primary leading-none">
			{/* 修改块开始: 邮件Logo替换
			 * 修改范围: 将内联SVG替换为img标签引用icon.svg文件
			 * 修改时间: 2025-07-05
			 * 对应计划步骤: 步骤2
			 * 恢复方法: 删除此修改块内所有代码，恢复下方注释中的原代码
			 */}
			<img
				src="/logo.svg"
				alt="星链资本Logo"
				className="size-10"
			/>
			{/* 原代码:
			 * <svg
			 *   className="size-10 text-red-500"
			 *   xmlns="http://www.w3.org/2000/svg"
			 *   version="1.1"
			 *   viewBox="-5.0 -10.0 110.0 135.0"
			 * >
			 *   <title>StarlinkLogo</title>
			 *   <path
			 *     fill="currentColor"
			 *     d="m90.09 23.578h-9.75c0.46094-3.2148 0.34766-6.4922-0.33984-9.668-0.49219-2.1602-1.2305-4.2578-2.1992-6.25-0.91406-1.9414-3.0391-3-5.1367-2.5547-2.0977 0.44141-3.6172 2.2695-3.6641 4.4141-0.10156 5-2.2305 9.8398-6.1602 14.059h-25.68c-3.7305-3.7578-5.9258-8.7695-6.1602-14.059-0.027344-2.1562-1.5469-4.0039-3.6562-4.4492-2.1094-0.44531-4.2461 0.62891-5.1445 2.5898-0.97266 1.9922-1.7109 4.0898-2.1992 6.25-0.69531 3.1758-0.82031 6.4492-0.37109 9.668h-9.7188c-1.2266 0-2.4023 0.48828-3.2656 1.3555-0.86719 0.86719-1.3555 2.043-1.3555 3.2656 0.007813 5.3008 2.1016 10.383 5.8281 14.148 3.7266 3.7695 8.7852 5.918 14.082 5.9805l5 22.371c-1.2695 3.1055-2.0391 6.3945-2.2695 9.7422-0.03125 8.2969 9.4688 14.559 22.07 14.559s22.102-6.2617 22.102-14.559c-0.23437-3.3477-1-6.6367-2.2734-9.7422l5-22.371c5.293-0.070313 10.348-2.2227 14.066-5.9922 3.7188-3.7656 5.8086-8.8438 5.8164-14.137 0-2.5508-2.0703-4.6211-4.6211-4.6211zm-15.512 18.594c-0.60156-0.003906-1.2031-0.042969-1.7969-0.12109l-2.7812-0.37891-6.5508 29.5 0.32812 0.87109 0.003906-0.003907c1.1484 2.6758 1.8867 5.5078 2.1875 8.4023 0 4.0586-6.3984 8.3984-15.941 8.3984-9.5391 0-15.941-4.3398-15.941-8.3984h0.003906c0.28906-2.9062 1.0156-5.7539 2.1602-8.4414l0.32812-0.87109-6.5781-29.457-2.8086 0.37891c-0.59766 0.078125-1.1992 0.11719-1.8008 0.12109-7.1055-0.027344-13.062-5.3711-13.859-12.434h16l-1.1211-3.9375c-0.91016-3.1172-1.0977-6.4023-0.55078-9.6016 1.4922 4.8164 4.1875 9.168 7.832 12.652l0.89844 0.89063 30.82-0.003907 0.89844-0.89062v0.003906c3.6484-3.4805 6.3438-7.8359 7.832-12.652 0.55078 3.2031 0.35938 6.4844-0.5625 9.6016l-1.1289 3.9414h16v-0.003907c-0.79688 7.0664-6.7617 12.41-13.871 12.434z"
			 *   />
			 * </svg>
			 * 修改原因: 替换为新的LOGO文件引用，保持原始颜色，实现集中化管理
			 * 修改时间: 2025-07-05
			 * 修改人: LLM
			 * 关联需求: 将项目中所有带有StarlinkLogo标签的LOGO替换为icon.svg
			 * 恢复方法: 删除当前img标签，取消上方原代码的注释
			 */}
			{/* 修改块结束: 邮件Logo替换
			 * 修改时间: 2025-07-05
			 */}
			{withLabel && <span className="ml-3 text-xl">StarLink</span>}
		</span>
	);
}
