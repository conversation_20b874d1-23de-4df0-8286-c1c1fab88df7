import { withContentCollections } from "@content-collections/next";
import type { NextConfig } from "next";
import nextIntlPlugin from "next-intl/plugin";

const withNextIntl = nextIntlPlugin("./modules/i18n/request.ts");

const nextConfig: NextConfig = {
	transpilePackages: ["@repo/api", "@repo/auth"],
	// 确保在前端可以访问以NEXT_PUBLIC_开头的环境变量
	env: {
		NEXT_PUBLIC_SHAREHOLDER_API_KEY: process.env.NEXT_PUBLIC_SHAREHOLDER_API_KEY || '',
		NEXT_PUBLIC_SHAREHOLDER_API_SECRET: process.env.NEXT_PUBLIC_SHAREHOLDER_API_SECRET || '',
		NEXT_PUBLIC_SHAREHOLDER_API_IV: process.env.NEXT_PUBLIC_SHAREHOLDER_API_IV || '',
	},
	images: {
		remotePatterns: [
			{
				// google profile images
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
			},
			{
				// github profile images
				protocol: "https",
				hostname: "avatars.githubusercontent.com",
			},
		],
	},
	async redirects() {
		return [
			{
				source: "/app/settings",
				destination: "/app/settings/general",
				permanent: true,
			},
			{
				source: "/app/:organizationSlug/settings",
				destination: "/app/:organizationSlug/settings/general",
				permanent: true,
			},
			{
				source: "/app/admin",
				destination: "/app/admin/users",
				permanent: true,
			},
		];
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
};

export default withContentCollections(withNextIntl(nextConfig));
