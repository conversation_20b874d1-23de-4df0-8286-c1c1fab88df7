"use client";

import { useState, useEffect, useMemo } from "react";

// 定义通用样式配置接口
export interface ScaledStyleConfig {
  // 字体大小配置
  fontSize: {
    heading: string;      // 标题字体
    subheading: string;   // 副标题字体
    content: string;      // 内容字体
    label: string;        // 标签字体
  };
  // 间距配置
  spacing: {
    container: string;    // 容器内边距
    section: string;      // 区块间距
    item: string;         // 项目间距
    cell: string;         // 单元格内边距
    header: string;       // 表头内边距
  };
  // 按钮和表单元素配置
  elements: {
    buttonSize: string;   // 按钮大小
    inputHeight: string;  // 输入框高度
    iconSize: string;     // 图标大小
    borderRadius: string; // 边框圆角
  };
  // 布局配置  
  layout: {
    gap: string;          // 间隙大小
    padding: string;      // 内边距
    margin: string;       // 外边距
  };
  // 表格列宽配置
  tableCellWidths?: {
    [key: string]: string; // 各列宽度
  };
  // 标签样式
  tagSize: string;        // 标签大小
}

// 表格特定的样式配置
export interface TableStyleConfig {
  fontSize: {
    heading: string;
    label: string;
    content: string;
    subheading: string;
  };
  colors: {
    headerBg: string;
    rowHover: string;
  };
  spacing: {
    header: string;
    cell: string;
    container: string;
  };
  misc: {
    borderRadius: string;
    shadow: string;
  };
}

// 表单/过滤器特定的样式配置
export interface FormStyleConfig {
  buttonSize: "sm" | "md" | "lg" | "icon";
  fontSize: string;
  spacing: string;
  iconSize: string;
  selectWidth: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
  };
}

/**
 * 系统缩放级别钩子
 * 检测并响应用户系统的缩放设置变化，提供相应的样式配置
 * 
 * @returns 包含缩放比例和相应样式配置的对象
 */
export function useSystemScale() {
  // 初始化为1（100%缩放）
  const [scale, setScale] = useState<number>(1);
  
  useEffect(() => {
    // 初始化时获取当前缩放比例
    const updateScale = () => {
      // 使用window.devicePixelRatio作为缩放比例的参考
      // 在Windows系统上，这个值会随着系统缩放设置而变化
      const currentScale = window.devicePixelRatio || 1;
      setScale(currentScale);
    };
    
    // 首次运行时获取缩放比例
    updateScale();
    
    // 监听窗口的resize事件，因为缩放变化通常会触发resize
    window.addEventListener("resize", updateScale);
    
    // 如果浏览器支持此事件，则可以更精确地监测缩放变化
    if (window.matchMedia) {
      const mediaQueryList = window.matchMedia("(resolution: 1dppx)");
      // 部分浏览器支持resolution媒体查询的变化事件
      if (mediaQueryList.addEventListener) {
        mediaQueryList.addEventListener("change", updateScale);
      }
    }
    
    // 清理函数
    return () => {
      window.removeEventListener("resize", updateScale);
      if (window.matchMedia) {
        const mediaQueryList = window.matchMedia("(resolution: 1dppx)");
        if (mediaQueryList.removeEventListener) {
          mediaQueryList.removeEventListener("change", updateScale);
        }
      }
    };
  }, []);
  
  // 根据缩放比例提供通用样式配置
  const styles = useMemo<ScaledStyleConfig>(() => {
    // 高缩放比例（例如125%以上），使用更紧凑的样式
    if (scale > 1.25) {
      return {
        fontSize: {
          heading: "text-xs",
          subheading: "text-[10px]",
          content: "text-xs",
          label: "text-[10px]"
        },
        spacing: {
          container: "px-3 py-3",
          section: "mb-3",
          item: "py-1.5 px-2",
          cell: "py-2 px-3",
          header: "py-2.5"
        },
        elements: {
          buttonSize: "sm",
          inputHeight: "h-8",
          iconSize: "h-3 w-3",
          borderRadius: "rounded"
        },
        layout: {
          gap: "gap-2",
          padding: "p-2",
          margin: "my-2"
        },
        tagSize: "text-[10px]"
      };
    }
    
    // 默认样式配置（标准缩放）
    return {
      fontSize: {
        heading: "text-sm",
        subheading: "text-xs",
        content: "text-sm",
        label: "text-xs"
      },
      spacing: {
        container: "px-4 py-4",
        section: "mb-4",
        item: "py-2 px-3",
        cell: "py-2.5 px-4",
        header: "py-3"
      },
      elements: {
        buttonSize: "sm",
        inputHeight: "h-9",
        iconSize: "h-4 w-4",
        borderRadius: "rounded-md"
      },
      layout: {
        gap: "gap-3",
        padding: "p-3",
        margin: "my-3"
      },
      tagSize: "text-xs"
    };
  }, [scale]);
  
  // 计算表格样式配置
  const tableStyles = useMemo<TableStyleConfig>(() => {
    return {
      fontSize: styles.fontSize,
      spacing: {
        header: styles.spacing.header,
        cell: styles.spacing.cell,
        container: "px-0"
      },
      colors: {
        headerBg: "bg-slate-50",
        rowHover: "hover:bg-slate-50/80"
      },
      misc: {
        borderRadius: "rounded-md",
        shadow: "shadow-sm"
      }
    };
  }, [styles]);
  
  // 计算表单/过滤器样式配置
  const formStyles = useMemo<FormStyleConfig>(() => {
    // 缩放因子
    const scaleFactor = scale > 1.25 ? 0.85 : 1;
    
    return {
      buttonSize: "sm", // 按钮大小固定为sm
      fontSize: styles.fontSize.content,
      spacing: styles.layout.gap,
      iconSize: styles.elements.iconSize,
      selectWidth: {
        // 减小选择器尺寸，特别是在高缩放比例下
        xs: scale > 1.25 ? "w-[90px]" : `w-[${Math.round(110 * scaleFactor)}px]`,
        sm: scale > 1.25 ? "w-[110px]" : `w-[${Math.round(130 * scaleFactor)}px]`,
        md: scale > 1.25 ? "w-[130px]" : `w-[${Math.round(150 * scaleFactor)}px]`,
        lg: scale > 1.25 ? "w-[150px]" : `w-[${Math.round(180 * scaleFactor)}px]`
      }
    };
  }, [scale, styles]);
  
  // 创建表格列宽计算函数
  const getTableColumnWidths = (baseWidths: Record<string, number>) => {
    const scaleFactor = scale > 1.25 ? 0.85 : 1;
    
    // 将基础宽度转换为带有缩放系数的CSS类
    const result: Record<string, string> = {};
    for (const [key, width] of Object.entries(baseWidths)) {
      result[key] = `w-[${Math.round(width * scaleFactor)}px]`;
    }
    
    return result;
  };
  
  // 计算表格总宽度的辅助函数
  const calculateTableWidth = (baseWidth: number) => {
    const scaleFactor = scale > 1.25 ? 0.85 : 1;
    return Math.round(baseWidth * scaleFactor);
  };
  
  return {
    scale,              // 当前缩放比例
    styles,             // 通用样式配置
    tableStyles,        // 表格样式配置
    formStyles,         // 表单样式配置
    getTableColumnWidths, // 表格列宽计算函数
    calculateTableWidth   // 表格总宽度计算函数
  };
} 