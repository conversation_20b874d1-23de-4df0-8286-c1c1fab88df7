/**
 * 股东名册解析配置文件
 * 定义了解析过程中使用的常量和配置项
 *
 * @version 1.0.0 (2025-06-05)
 * <AUTHOR> 2025-06-05
 */

/**
 * 股东记录最大数量
 * 解析出的股东记录将按持股数量排序，只保留前MAX_RECORDS_COUNT条记录
 *
 * <AUTHOR>
 * @created 2025-06-05
 * @update 2025-06-17 19:15:34 修正环境变量获取方式，使用正确的语法和类型转换
 */
export const MAX_RECORDS_COUNT = process.env.NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE
	? Number(process.env.NEXT_PUBLIC_SHAREHOLDER_BATCH_SIZE)
	: 200;

/**
 * 名册类型定义
 */
export enum RegistryType {
  TYPE_01 = '01',
  TYPE_05 = '05',
  TYPE_T1 = 't1',
  TYPE_T2 = 't2',
  TYPE_T3 = 't3',
  UNKNOWN = 'unknown'
}

/**
 * 名册文件格式定义
 */
export enum FileFormat {
  DBF = 'dbf',
  EXCEL = 'excel',
  ZIP = 'zip',
  UNKNOWN = 'unknown'
}

/**
 * 名册解析阶段定义
 */
export enum ParsePhase {
  READING = 'reading',
  PARSING = 'parsing',
  PREPROCESSING = 'preprocessing',
  COMPLETE = 'complete'
} 