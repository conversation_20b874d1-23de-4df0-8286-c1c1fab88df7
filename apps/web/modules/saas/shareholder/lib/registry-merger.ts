/**
 * 股东名册合并预处理工具
 * 用于处理不同类型名册的合并和预处理
 *
 * 主要功能:
 * 1. 支持t1/t2/t3名册的预处理和合并
 * 2. 实现基于组合键的记录匹配和合并
 * 3. 处理持股数量的叠加和非叠加字段的保留
 * 4. 提供按持股数量排序和记录数量限制功能
 *
 * 使用场景:
 * - 用于合并多个t1/t2/t3名册的数据
 * - 用于预处理上传前的名册数据
 * - 用于按持股数量排序和限制记录数量
 * 
 * @version 1.2.0 (2025-06-03)
 * <AUTHOR> 2025-06-03
 * @update 2025-06-03 19:22:01 添加对t2名册预处理的支持
 * @update 2025-06-03 19:30:24 添加对t3名册预处理的支持
 */

import { MAX_RECORDS_COUNT } from "./config";
import { preprocessT1Records } from "./excel-parser/type-t1";
import { preprocessT2Records } from "./excel-parser/type-t2";
import { preprocessT3Records } from "./excel-parser/type-t3";

/**
 * 预处理t1名册数据
 * 根据ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配和合并
 * 
 * @param records t1名册记录
 * @returns 预处理后的记录
 */
export { preprocessT1Records };

/**
 * 预处理t2名册数据
 * 根据ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配和合并
 * 
 * @param records t2名册记录
 * @returns 预处理后的记录
 */
export { preprocessT2Records };

/**
 * 预处理t3名册数据
 * 根据ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配和合并
 * 
 * @param records t3名册记录
 * @returns 预处理后的记录
 */
export { preprocessT3Records };

/**
 * 按持股数量排序并限制记录数量
 * 
 * @param records 记录数组
 * @param shareField 持股数量字段名
 * @param limit 记录数量限制
 * @returns 排序并限制后的记录
 */
export function sortAndLimitRecords(
  records: any[],
  shareField = "CYSL",
  limit: number = MAX_RECORDS_COUNT
): any[] {
  if (!records || records.length === 0) {
    return [];
  }
  
  // 按持股数量排序
  const sortedRecords = [...records].sort((a, b) => {
    const sharesA = Number(a[shareField] || 0);
    const sharesB = Number(b[shareField] || 0);
    return sharesB - sharesA; // 降序排序
  });
  
  // 限制记录数量
  return sortedRecords.slice(0, limit);
}

/**
 * 合并多个名册的记录
 * 
 * @param recordsArray 多个名册记录数组
 * @param shareField 持股数量字段名，默认为CYSL，t3名册可能需要使用ZCYSL
 * @returns 合并后的记录
 */
export function mergeRecords(
  recordsArray: any[][],
  shareField = "CYSL"
): any[] {
  // 将所有记录合并为一个数组
  const allRecords = recordsArray.flat();
  
  // 使用Map存储合并后的记录，键为"ZJHM+YMTZHHM"
  const mergedRecordsMap = new Map<string, any>();
  
  // 遍历所有记录
  for (const record of allRecords) {
    // 生成组合键
    const zjhm = record.ZJHM || '';
    const ymtzhhm = record.YMTZHHM || '';
    const key = `${zjhm}|${ymtzhhm}`;
    
    // 如果Map中已存在该键，合并记录
    if (mergedRecordsMap.has(key)) {
      const existingRecord = mergedRecordsMap.get(key);
      
      // 叠加持股数量
      if (shareField === "CYSL") {
        // t1/t2名册使用CYSL字段
        const existingShares = Number(existingRecord[shareField] || 0);
        const newShares = Number(record[shareField] || 0);
        existingRecord[shareField] = (existingShares + newShares).toString();
      } else if (shareField === "ZCYSL") {
        // t3名册使用ZCYSL、PTZQZHCYSL和XYCYSL字段
        // 叠加普通持股数量
        const existingPtShares = Number(existingRecord.PTZQZHCYSL || 0);
        const newPtShares = Number(record.PTZQZHCYSL || 0);
        existingRecord.PTZQZHCYSL = (existingPtShares + newPtShares).toString();
        
        // 叠加信用持股数量
        const existingXyShares = Number(existingRecord.XYCYSL || 0);
        const newXyShares = Number(record.XYCYSL || 0);
        existingRecord.XYCYSL = (existingXyShares + newXyShares).toString();
        
        // 叠加总持股数量
        const existingTotalShares = Number(existingRecord.ZCYSL || 0);
        const newTotalShares = Number(record.ZCYSL || 0);
        existingRecord.ZCYSL = (existingTotalShares + newTotalShares).toString();
      }
      
      // 其他字段保留先到先得的值
    } else {
      // 如果Map中不存在该键，添加记录
      mergedRecordsMap.set(key, { ...record });
    }
  }
  
  // 将Map转换为数组
  return Array.from(mergedRecordsMap.values());
} 