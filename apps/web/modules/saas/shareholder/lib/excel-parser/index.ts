/**
 * Excel文件解析器入口
 * 用于处理股东名册Excel文件的解析和验证
 * 支持t1/t2/t3名册的解析和合并
 *
 * 主要功能:
 * 1. 根据文件名和内容判断名册类型(t1/t2/t3)
 * 2. 调用相应的解析器处理不同类型的名册
 * 3. 支持t1/t2/t3名册的字段映射和数据合并
 * 4. 提供统一的解析结果格式
 *
 * 使用场景:
 * - 用于解析证券公司提供的股东名册Excel文件
 * - 支持处理包含股东信息、持股数量等数据的Excel文件
 * - 支持t1/t2/t3名册的合并处理
 * 
 * @version 1.2.0 (2025-06-03)
 * <AUTHOR> 2025-06-03
 * @update 2025-06-03 19:22:01 添加对t2名册解析的支持
 * @update 2025-06-03 19:30:24 添加对t3名册解析的支持
 */

// 导入通用解析工具和类型
import { detectExcelRegistryType, isExcelFile, isTSeriesRegistry } from "./common";
import { RegistryType } from "../config";

// 导入t1名册解析器
import { parseExcelFileT1 } from "./type-t1";

// 导入t2名册解析器
import { parseExcelFileT2 } from "./type-t2";

// 导入t3名册解析器
import { parseExcelFileT3 } from "./type-t3";

// 导入ShareholderRegistryParseResult类型
import type { ShareholderRegistryParseResult } from "../dbf-parser/common";

/**
 * 检查文件类型是否为Excel文件(XLS/XLSX)
 * 
 * @param fileName 文件名
 * @returns 是否为Excel文件
 */
export { isExcelFile };

/**
 * 检查文件是否为t1/t2/t3名册
 * 
 * @param fileName 文件名
 * @returns 是否为t1/t2/t3名册
 */
export { isTSeriesRegistry };

/**
 * Excel股东名册文件解析器
 * 
 * 该函数根据文件名判断名册类型（t1/t2/t3），并调用相应的解析器处理
 *
 * @param file 要解析的Excel文件
 * @returns 解析结果，包含公司信息和股东记录
 */
export async function parseExcelFile(
  file: File,
): Promise<ShareholderRegistryParseResult> {
  try {
    // 判断文件类型(t1/t2/t3)
    const registryType = detectExcelRegistryType(file.name);

    // 根据文件类型调用相应的解析器
    if (registryType === RegistryType.TYPE_T1) {
      return await parseExcelFileT1(file);
    }
    
    // 处理t2名册
    if (registryType === RegistryType.TYPE_T2) {
      return await parseExcelFileT2(file);
    }

    // 处理t3名册
    if (registryType === RegistryType.TYPE_T3) {
      return await parseExcelFileT3(file);
    }

    // 如果无法确定类型，返回错误
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: "无法确定名册类型，请确保文件名包含t1/t2/t3标识",
      },
    };
  } catch (error) {
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message:
          error instanceof Error ? error.message : "解析Excel文件失败",
      },
    };
  }
} 