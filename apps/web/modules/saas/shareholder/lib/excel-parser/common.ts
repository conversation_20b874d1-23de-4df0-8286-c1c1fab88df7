/**
 * Excel文件解析和验证共享工具
 * 提供Excel解析器共享的类型定义和工具函数
 *
 * 主要功能:
 * 1. 定义共享的数据类型和接口
 * 2. 提供通用的Excel文件解析函数
 * 3. 提供通用的验证和工具函数
 * 4. 处理中文编码问题
 * 
 * @version 1.4.0 (2025-06-04)
 * <AUTHOR> 2025-06-03
 * @update 2025-06-04 10:14:18 参考parseExcel.js重构中文编码处理逻辑，简化实现
 * @update 2025-06-04 10:06:17 增强fixEncoding函数，提高中文编码转换成功率
 * @update 2025-06-03 增加读取隐藏列的功能，解决字段缺失问题
 * @update 2025-06-03 19:46:41 增强编码处理能力，解决t2和t3名册中文乱码问题
 * @update 2025-06-03 20:15:00 参考parseExcel.js优化中文编码处理，解决乱码问题
 */

import * as XLSX from "xlsx";
import { decode } from "iconv-lite";
import { RegistryType } from "../config";

/**
 * Excel记录数据
 * 使用索引签名允许动态的字段名
 */
export interface ExcelRecord {
  [key: string]: any;
}

/**
 * 检测名册类型
 * 根据文件名判断是t1、t2还是t3名册
 *
 * @param fileName 文件名
 * @returns 名册类型
 * @update 2025-06-04 13:13:49 修复文件名中同时包含多个类型标识的情况，确保正确识别t3名册
 */
export function detectExcelRegistryType(fileName: string): RegistryType {
  const lowercaseName = fileName.toLowerCase();

  // 更精确的检测方式：检查文件名是否以t1/t2/t3开头
  if (lowercaseName.startsWith('t3')) {
    return RegistryType.TYPE_T3;
  }
  
  if (lowercaseName.startsWith('t2')) {
    return RegistryType.TYPE_T2;
  }
  
  if (lowercaseName.startsWith('t1')) {
    return RegistryType.TYPE_T1;
  }
  
  // 如果不是以t1/t2/t3开头，则使用更宽松的检测方式
  // 但要注意检测顺序：先检测t3，再检测t2，最后检测t1
  // 这样可以避免t3被误判为t2（如t36053380320241231t200.c31.xls）
  if (/\bt3\b/.test(lowercaseName) || lowercaseName.includes('t3')) {
    return RegistryType.TYPE_T3;
  }
  
  if (/\bt2\b/.test(lowercaseName) || lowercaseName.includes('t2')) {
    return RegistryType.TYPE_T2;
  }
  
  if (/\bt1\b/.test(lowercaseName) || lowercaseName.includes('t1')) {
    return RegistryType.TYPE_T1;
  }
  
  return RegistryType.UNKNOWN;
}

/**
 * 检查文件类型是否为Excel文件(XLS/XLSX)
 *
 * @param fileName 文件名
 * @returns 是否为Excel文件
 */
export function isExcelFile(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.endsWith(".xls") || lowercaseName.endsWith(".xlsx");
}

/**
 * 检查文件是否为t1/t2/t3名册
 *
 * @param fileName 文件名
 * @returns 是否为t1/t2/t3名册
 */
export function isTSeriesRegistry(fileName: string): boolean {
  const lowercaseName = fileName.toLowerCase();
  return lowercaseName.includes('t1') || 
         lowercaseName.includes('t2') || 
         lowercaseName.includes('t3');
}

/**
 * 处理字符串，去除多余空格和特殊字符
 * 参考parseExcel.js中的cleanString函数实现
 *
 * @param str 输入字符串
 * @returns 处理后的字符串
 */
export function cleanString(str: string | null | undefined): string {
  if (!str || typeof str !== 'string') {
    return '';
  }
  
  return str.trim();
}

/**
 * 处理可能的乱码字符串
 * 直接参考parseExcel.js中的fixEncoding函数实现
 * 
 * @param text 输入的可能包含乱码的字符串
 * @returns 处理后的字符串
 * @update 2025-06-04 10:14:18 简化实现，直接参考parseExcel.js
 */
export function fixEncoding(text: string | any): string | any {
  if (typeof text !== 'string') {
    return text;
  }
  
  // 如果字符串为空，直接返回
  if (!text || text.trim() === '') {
    return text;
  }
  
  try {
    // 检测是否包含乱码特征
    if (/[\u0080-\u00FF]/.test(text)) {
      // 将字符串转换为Buffer，然后使用iconv-lite解码
      const buffer = Buffer.from(text, 'binary');
      return decode(buffer, 'gbk');
    }
    return text;
  } catch (error) {
    console.warn(`编码转换失败: ${error instanceof Error ? error.message : String(error)}`);
    return text;
  }
}

/**
 * 检测文本是否包含乱码字符
 *
 * @param text 要检查的文本
 * @returns 是否包含乱码
 */
export function containsGarbledChars(text: string): boolean {
  if (!text) {
    return false;
  }

  // 检查常见乱码字符
  const hasGarbledChars = /[\ufffd\u0080-\u009f]/.test(text) || 
                         text.includes("\ufffd");
                         
  // 检查异常的字符重复模式
  const hasAbnormalRepetition = /([\u4e00-\u9fa5])\1{3,}/.test(text) || 
                               /([^\u4e00-\u9fa5])\1{5,}/.test(text);
  
  // 检查特定乱码组合
  const hasSpecificPatterns = /°Í|±È|Ê³|Æ·|Ó¢|Îä|Ãû|²É|¹Ø|Ô±|Ò»|Ö®|Ð¡|Ìì|Èõ|Ñî|ÎÀ|ÉÏ|ÊÐ|Ð£|Ë¥|Ëû|Ëþ|Ïà|Ïé|Ò¶|Ìá|Ìû|Ïë|Ìô|Ìõ/.test(text);
  
  // 检查t2和t3名册特有的乱码模式
  const hasT2T3Patterns = /ÐÕÃû|¹ÉÊý|ÖÇÄÜ|ÍøÂç|¿Æ¼¼|ÓÊ±à|µØÖ·|µç»°|×ªÕÊ|´æ¿î|Ö§¸¶|ÁÄÌì|ÊÕ¼Ó|ÊÕ²Ø|·ÖÏí|ÒÆ¶¯|Óë|Ìá|Ïë|ÆÀ|ÂÛ|ÍÆ|¼ö|ÑÔ|Í¼|ÏÂ|ÉÏ|ÖÐ|Ïà|Î÷|¶«|ÄÏ|±±|Ð¡|´ó|ÖÐ|¸ß|µÍ|¿ì|Âý|ÓÒ|×ó|Ç°|ºó|ÄÚ|Íâ|ÖÐ|¼ä|Éú|Ëé|Ò©|Ê³|Æ·|Îï|ÁÏ|Ò»|¶þ|Èý|ËÄ|ÎÞ|ÓÐ|Êý|×Ö|¶¨|¶©|¿Õ|Ãâ|Ò£|Óà/.test(text);
  
  // 检查乱码比例
  const garbledChars = text.match(/[\ufffd\u0080-\u009f\u00a0-\u00ff]/g) || [];
  const garbledRatio = text.length > 0 ? garbledChars.length / text.length : 0;
  
  // 检查是否包含正常中文字符
  const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
  const chineseRatio = text.length > 0 ? chineseChars.length / text.length : 0;
  
  // 如果文本包含足够多的正常中文字符，不太可能是乱码
  if (text.length > 5 && chineseRatio > 0.5) {
    return false;
  }
  
  return hasGarbledChars || hasAbnormalRepetition || hasSpecificPatterns || hasT2T3Patterns || garbledRatio > 0.3;
}

/**
 * 从Excel文件中读取工作表数据
 * 参考parseExcel.js的实现，简化编码处理逻辑
 *
 * @param file Excel文件
 * @returns 工作表数据
 * @update 2025-06-04 10:14:18 简化实现，直接参考parseExcel.js
 */
export async function readExcelWorksheet(file: File): Promise<{
  worksheet: XLSX.WorkSheet;
  workbook: XLSX.WorkBook;
}> {
  try {
    // 读取Excel文件数据为ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // 将ArrayBuffer转换为Uint8Array
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // 将Uint8Array转换为二进制字符串，这是XLSX.js在type="binary"时所需的格式
    let binary = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    
    // 使用GBK编码(代码页936)读取工作簿
    const options: XLSX.ParsingOptions = { 
      type: "binary",
      codepage: 936, // GBK编码的代码页
      cellStyles: true,
      cellDates: true,
      raw: true,
      dateNF: 'yyyy-mm-dd'
    };
    
    const workbook = XLSX.read(binary, options);
    
    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    return { worksheet, workbook };
  } catch (error) {
    console.error('读取Excel文件失败:', error);
    throw new Error(`读取Excel文件失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 识别特殊记录（表头、汇总行等）
 *
 * @param data 原始数据
 * @returns 特殊记录对象
 */
export function identifySpecialRecords(data: Record<string, any>[]): {
  headerRow?: Record<string, any>;
  summaryRows: Record<string, any>[];
} {
  const specialRecords = {
    headerRow: undefined as Record<string, any> | undefined,
    summaryRows: [] as Record<string, any>[],
  };
  
  // 如果数据为空，直接返回
  if (!data || data.length === 0) {
    return specialRecords;
  }
  
  // 尝试识别表头行（通常是第一行）
  specialRecords.headerRow = data[0];
  
  // 尝试识别汇总行（通常在最后几行）
  // 汇总行通常包含"合计"、"总计"等关键词
  for (let i = data.length - 1; i >= Math.max(0, data.length - 5); i--) {
    const row = data[i];
    const rowValues = Object.values(row);
    const rowString = rowValues.join(' ');
    
    if (
      rowString.includes('合计') ||
      rowString.includes('总计') ||
      rowString.includes('汇总')
    ) {
      specialRecords.summaryRows.push(row);
    }
  }
  
  return specialRecords;
}

/**
 * 提取常规记录（排除表头和汇总行）
 *
 * @param data 原始数据
 * @param specialRecords 特殊记录
 * @returns 常规记录数组
 */
export function extractRegularRecords(
  data: any[],
  specialRecords: { headerRow?: any; summaryRows: any[] }
): any[] {
  if (!data || data.length === 0) {
    return [];
  }
  
  const regularRecords = [...data];
  
  // 如果有表头行，从结果中移除
  if (specialRecords.headerRow) {
    regularRecords.shift();
  }
  
  // 如果有汇总行，从结果中移除
  for (const summaryRow of specialRecords.summaryRows) {
    const index = regularRecords.findIndex(
      (row) => JSON.stringify(row) === JSON.stringify(summaryRow)
    );
    if (index !== -1) {
      regularRecords.splice(index, 1);
    }
  }
  
  return regularRecords;
}

/**
 * 从文件名解析公司代码和报告日期
 * 从文件名中提取公司代码和报告日期信息
 *
 * @param fileName 文件名，标准格式为t1XXXXXXxxyyyymmddall.mdd.xls
 * @returns 解析结果对象，包含公司代码和报告日期
 */
export function parseExcelFileName(fileName: string): {
  companyCode?: string;
  registerDate?: string;
} {
  try {
    // 文件名示例: t1XXXXXXxxyyyymmddall.mdd.xls
    // XXXXXX: 证券代码
    // xx: 股份性质
    // yyyymmdd: 权益登记日
    // all: 表示发送对象为全体证券持有人
    // mdd: 文件发送日期
    
    // 移除文件扩展名
    const nameWithoutExt = fileName.split('.')[0];
    
    // 提取公司代码（第3到8个字符）
    let companyCode: string | undefined;
    if (nameWithoutExt.length >= 8) {
      companyCode = nameWithoutExt.substring(2, 8);
    }
    
    // 提取日期（第10到18个字符）
    let registerDate: string | undefined;
    if (nameWithoutExt.length >= 18) {
      const dateStr = nameWithoutExt.substring(10, 18);
      
      // 解析日期
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      
      // 验证日期有效性
      const date = new Date(Number(year), Number(month) - 1, Number(day));
      if (
        date.getFullYear() === Number(year) &&
        date.getMonth() === Number(month) - 1 &&
        date.getDate() === Number(day)
      ) {
        registerDate = `${year}-${month}-${day}`;
      }
    }
    
    return { companyCode, registerDate };
  } catch (error) {
    // 解析文件名失败
    return {};
  }
}

/**
 * 验证必填字段是否存在
 *
 * @param record 记录对象
 * @param requiredFields 必填字段列表
 * @returns 缺失的字段列表
 */
export function checkRequiredFields(
  record: any,
  requiredFields: Array<{ name: string; label: string }>
): string[] {
  if (!record) {
    return requiredFields.map(field => field.label);
  }
  
  return requiredFields
    .filter(field => !(field.name in record))
    .map(field => field.label);
}

/**
 * 验证必填字段的内容是否为空
 *
 * @param records 记录数组
 * @param requiredFields 必填字段列表
 * @returns 空值字段列表
 */
export function validateRequiredFieldsContent(
  records: any[],
  requiredFields: Array<{ name: string; label: string }>
): string[] {
  
  const emptyFields = new Set<string>();
  
  // 遍历所有记录，检查必填字段
  for (const record of records) {
    for (const field of requiredFields) {
      const fieldValue = record[field.name];
      // 增强空值检测逻辑
      const isEmpty = 
        fieldValue === undefined || 
        fieldValue === null || 
        fieldValue === "" || 
        (typeof fieldValue === 'string' && fieldValue.trim() === "") ||
        (typeof fieldValue === 'number' && Number.isNaN(fieldValue));
      
      if (isEmpty) {
        emptyFields.add(field.label);
      }
    }
  }
  
  const result = Array.from(emptyFields);
  return result;
}

/**
 * 提取公司信息
 *
 * @param specialRecords 特殊记录
 * @returns 公司信息
 */
export function extractCompanyInfo(specialRecords: {
  headerRow?: any;
  summaryRows: any[];
}): {
  companyName: string;
  companyCode: string;
  registerDate: string;
  totalShares: string;
  totalShareholders: number;
  totalInstitutions: number;
  institutionShares: string;
  largeSharesCount: string;
  largeShareholdersCount: number;
} {
  // 默认值
  const defaultInfo = {
    companyName: "",
    companyCode: "",
    registerDate: "",
    totalShares: "0",
    totalShareholders: 0,
    totalInstitutions: 0,
    institutionShares: "0",
    largeSharesCount: "0",
    largeShareholdersCount: 0,
  };
  
  try {
    // 如果没有特殊记录，返回默认值
    if (!specialRecords.headerRow && specialRecords.summaryRows.length === 0) {
      return defaultInfo;
    }
    
    // 从表头行提取信息
    if (specialRecords.headerRow) {
      const headerValues = Object.values(specialRecords.headerRow).join(' ');
      
      // 尝试提取公司名称
      const companyNameMatch = headerValues.match(/公司名称[:：]?\s*([^\s,，]+)/);
      if (companyNameMatch?.[1]) {
        defaultInfo.companyName = companyNameMatch[1];
      }
      
      // 尝试提取公司代码
      const companyCodeMatch = headerValues.match(/公司代码[:：]?\s*(\d+)/);
      if (companyCodeMatch?.[1]) {
        defaultInfo.companyCode = companyCodeMatch[1];
      }
      
      // 尝试提取日期
      const dateMatch = headerValues.match(/日期[:：]?\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2})/);
      if (dateMatch?.[1]) {
        defaultInfo.registerDate = dateMatch[1].replace(/[年月]/g, '-').replace(/日/g, '');
      }
    }
    
    // 从汇总行提取信息
    if (specialRecords.summaryRows.length > 0) {
      // 合并所有汇总行的文本
      const summaryText = specialRecords.summaryRows
        .map(row => Object.values(row).join(' '))
        .join(' ');
      
      // 尝试提取总股本
      const totalSharesMatch = summaryText.match(/总股本[:：]?\s*([\d,，]+)/);
      if (totalSharesMatch?.[1]) {
        defaultInfo.totalShares = totalSharesMatch[1].replace(/[,，]/g, '');
      }
      
      // 尝试提取股东总数
      const totalShareholdersMatch = summaryText.match(/股东总数[:：]?\s*(\d+)/);
      if (totalShareholdersMatch?.[1]) {
        defaultInfo.totalShareholders = Number.parseInt(totalShareholdersMatch[1], 10);
      }
      
      // 尝试提取机构股东数
      const totalInstitutionsMatch = summaryText.match(/机构股东[:：]?\s*(\d+)/);
      if (totalInstitutionsMatch?.[1]) {
        defaultInfo.totalInstitutions = Number.parseInt(totalInstitutionsMatch[1], 10);
      }
      
      // 尝试提取机构持股
      const institutionSharesMatch = summaryText.match(/机构持股[:：]?\s*([\d,，]+)/);
      if (institutionSharesMatch?.[1]) {
        defaultInfo.institutionShares = institutionSharesMatch[1].replace(/[,，]/g, '');
      }
      
      // 尝试提取大股东持股
      const largeSharesMatch = summaryText.match(/大股东持股[:：]?\s*([\d,，]+)/);
      if (largeSharesMatch?.[1]) {
        defaultInfo.largeSharesCount = largeSharesMatch[1].replace(/[,，]/g, '');
      }
      
      // 尝试提取大股东数量
      const largeShareholdersMatch = summaryText.match(/大股东数量[:：]?\s*(\d+)/);
      if (largeShareholdersMatch?.[1]) {
        defaultInfo.largeShareholdersCount = Number.parseInt(largeShareholdersMatch[1], 10);
      }
    }
    
    return defaultInfo;
  } catch (error) {
    return defaultInfo;
  }
}

/**
 * 统一提取公司基本信息
 * 适用于t1、t2、t3名册，处理不同名册间的字段差异
 * 
 * @param data 原始数据
 * @param registryType 名册类型 (t1/t2/t3)
 * @returns 公司基本信息
 * @version 1.0.0 (2025-06-04)
 * <AUTHOR> 2025-06-04 12:14:27
 */
export function extractUnifiedCompanyInfo(
  data: Record<string, any>[],
  registryType: string
): {
  companyCode: string;
  companyName: string;
  totalShares: string;
  totalShareholders: number;
  totalInstitutions: number;
  institutionShares: string;
  registerDate: string;
  largeSharesCount: string;
  largeShareholdersCount: number;
} {
  // 默认值
  const defaultInfo = {
    companyCode: "",
    companyName: "",
    totalShares: "0",
    totalShareholders: 0,
    totalInstitutions: 0,
    institutionShares: "0",
    registerDate: "",
    largeSharesCount: "0",
    largeShareholdersCount: 0
  };
  
  try {
    // 处理数据，修复可能的编码问题
    const processedData = data.map(record => {
      const processedRecord: Record<string, any> = {};
      for (const key in record) {
        if (Object.prototype.hasOwnProperty.call(record, key)) {
          if (typeof record[key] === 'string') {
            processedRecord[key] = fixEncoding(record[key]);
          } else {
            processedRecord[key] = record[key];
          }
        }
      }
      return processedRecord;
    });
    
    // 寻找SJLX=802的记录，这通常包含公司基本信息
    const infoRecord = processedData.find((record) => record.SJLX === 802 || record.SJLX === "802");
    
    if (!infoRecord) {
      // 如果找不到SJLX=802的记录，尝试从第一条记录获取基本信息
      if (processedData.length > 0) {
        const firstRecord = processedData[0];
        
        // 提取公司代码
        if (firstRecord.ZQDM) {
          defaultInfo.companyCode = typeof firstRecord.ZQDM === 'string' ? firstRecord.ZQDM.trim() : firstRecord.ZQDM.toString();
        }
        
        // 提取公司名称
        if (firstRecord.ZQJC) {
          defaultInfo.companyName = typeof firstRecord.ZQJC === 'string' ? 
            firstRecord.ZQJC.trim() : 
            firstRecord.ZQJC.toString();
        }
        
        // 提取权益登记日
        if (firstRecord.QYDJR) {
          defaultInfo.registerDate = typeof firstRecord.QYDJR === 'string' ? firstRecord.QYDJR.trim() : firstRecord.QYDJR.toString();
        }
      }
      
      return defaultInfo;
    }
    
    // 提取公司代码 (三种名册一致)
    if (infoRecord.ZQDM) {
      defaultInfo.companyCode = typeof infoRecord.ZQDM === 'string' ? infoRecord.ZQDM.trim() : infoRecord.ZQDM.toString();
    }
    
    // 提取公司名称 (三种名册一致，但需处理乱码)
    let companyName = "";
    
    // 1. 首先尝试从ZQJC字段获取
    if (infoRecord.ZQJC && typeof infoRecord.ZQJC === 'string' && infoRecord.ZQJC.trim() !== '') {
      companyName = infoRecord.ZQJC.trim();
    }
    
    // 2. 如果ZQJC为空或包含乱码，尝试其他可能的字段
    if (companyName === '' || containsGarbledChars(companyName)) {
      // 尝试从其他字段获取公司名称
      const possibleNameFields = ['GSJC', 'GSMC', 'ZQMC', 'ZQJC', 'ZQZH', 'ZQZHMC', 'JGMC', 'CYRMC'];
      
      for (const field of possibleNameFields) {
        if (infoRecord[field] && 
            typeof infoRecord[field] === 'string' && 
            infoRecord[field].trim() !== '') {
          const fieldValue = infoRecord[field].trim();
          if (!containsGarbledChars(fieldValue)) {
            companyName = fieldValue;
            break;
          }
        }
      }
    }
    
    // 3. 如果仍然没有找到有效的公司名称，尝试从所有记录中查找
    if (companyName === '' || containsGarbledChars(companyName)) {
      // 遍历所有记录，寻找可能包含公司名称的字段
      for (const record of processedData) {
        if (record.ZQJC && 
            typeof record.ZQJC === 'string' && 
            record.ZQJC.trim() !== '') {
          const fieldValue = record.ZQJC.trim();
          if (!containsGarbledChars(fieldValue)) {
            companyName = fieldValue;
            break;
          }
        }
      }
    }
    
    // 4. 如果仍然无法获取有效的公司名称，使用公司代码作为名称
    if (companyName === '' || containsGarbledChars(companyName)) {
      companyName = defaultInfo.companyCode ? 
        `公司(${defaultInfo.companyCode})` : 
        "未知公司";
    }
    
    defaultInfo.companyName = companyName;
    
    // 提取证券总数量 (三种名册一致)
    if (infoRecord.ZQZSL) {
      defaultInfo.totalShares = typeof infoRecord.ZQZSL === 'string' ? 
        infoRecord.ZQZSL.trim().replace(/[,，]/g, '') : 
        infoRecord.ZQZSL.toString();
    }
    
    // 提取权益登记日 (三种名册一致，但需要标准化格式)
    if (infoRecord.QYDJR) {
      let registerDate = typeof infoRecord.QYDJR === 'string' ? infoRecord.QYDJR.trim() : infoRecord.QYDJR.toString();
      
      // 标准化日期格式
      if (/^\d{8}$/.test(registerDate)) {
        // 如果是YYYYMMDD格式，转换为YYYY-MM-DD
        registerDate = `${registerDate.substring(0, 4)}-${registerDate.substring(4, 6)}-${registerDate.substring(6, 8)}`;
      }
      
      defaultInfo.registerDate = registerDate;
    }
    
    // 根据名册类型处理特有字段
    if (registryType === 'TYPE_T1') {
      // t1名册特有处理
      
      // 提取总户数
      if (infoRecord.ZHS) {
        defaultInfo.totalShareholders = typeof infoRecord.ZHS === 'string' ? 
          Number.parseInt(infoRecord.ZHS.trim(), 10) : 
          Number(infoRecord.ZHS);
      }
      
      // 提取机构户数合计
      if (infoRecord.JGHSHJ) {
        defaultInfo.totalInstitutions = typeof infoRecord.JGHSHJ === 'string' ? 
          Number.parseInt(infoRecord.JGHSHJ.trim(), 10) : 
          Number(infoRecord.JGHSHJ);
      }
      
      // 提取机构持有合计
      if (infoRecord.JGCYHJ) {
        defaultInfo.institutionShares = typeof infoRecord.JGCYHJ === 'string' ? 
          infoRecord.JGCYHJ.trim().replace(/[,，]/g, '') : 
          infoRecord.JGCYHJ.toString();
      }
    } else if (registryType === 'TYPE_T2') {
      // t2名册特有处理
      
      // 提取总户数
      if (infoRecord.ZHS) {
        defaultInfo.totalShareholders = typeof infoRecord.ZHS === 'string' ? 
          Number.parseInt(infoRecord.ZHS.trim(), 10) : 
          Number(infoRecord.ZHS);
      }
      
      // 提取机构户数合计
      if (infoRecord.JGHSHJ) {
        defaultInfo.totalInstitutions = typeof infoRecord.JGHSHJ === 'string' ? 
          Number.parseInt(infoRecord.JGHSHJ.trim(), 10) : 
          Number(infoRecord.JGHSHJ);
      }
      
      // 提取机构持股数
      if (infoRecord.JGCYHJ) {
			defaultInfo.institutionShares =
				typeof infoRecord.JGCYHJ === "string"
					? infoRecord.JGCYHJ.trim().replace(/[,，]/g, "")
					: infoRecord.JGCYHJ.toString();
		}
    } else if (registryType === 'TYPE_T3') {
      // t3名册特有处理
      // 提取总户数 - t3名册使用CYRS字段
      if (infoRecord.CYRS || infoRecord.ZHS) {
        const shareholdersField = infoRecord.CYRS || infoRecord.ZHS;
        defaultInfo.totalShareholders = typeof shareholdersField === 'string' ? 
          Number.parseInt(shareholdersField.trim(), 10) : 
          Number(shareholdersField);
      }
       
 
    }
    
    return defaultInfo;
  } catch (error) {
    console.error('提取公司信息失败:', error);
    return defaultInfo;
  }
} 