/**
 * t2名册Excel文件解析器
 * 专门用于处理t2类型股东名册的Excel文件解析
 * 
 * 主要功能:
 * 1. 解析t2名册特有的字段结构
 * 2. 提取t2名册中的公司信息和股东信息
 * 3. 处理t2名册特有的数据格式和规则
 * 
 * t2名册特点:
 * - 文件名格式为t2XXXXXXxxyyyymmddzz.mdd
 * - 包含前N名股东信息
 * - 不包含t1名册特有的流通类型(LTLX)和权益类别(YQLB)字段
 * 
 * @version 1.1.3 (2025-06-04)
 * <AUTHOR> 2025-06-03 19:22:01
 * @update 2025-06-04 11:23:10 修改股东数据筛选逻辑，只保留SJLX=801的记录作为股东数据
 * @update 2025-06-04 10:06:17 修复所有字段的中文编码问题，确保每个字段都经过fixEncoding处理
 * @update 2025-06-03 20:07:21 修复公司名称提取问题，增强对ZQJC为空或乱码的处理
 * @update 2025-06-03 19:46:41 增强编码处理能力，解决中文乱码问题
 * @update 2025-06-03 20:15:00 使用fixEncoding函数处理ZQJC字段，解决中文乱码问题
 */

import * as XLSX from "xlsx";
import { 
  checkRequiredFields,
  cleanString,
  extractCompanyInfo,
  extractRegularRecords,
  fixEncoding,
  identifySpecialRecords,
  parseExcelFileName,
  readExcelWorksheet,
  validateRequiredFieldsContent,
  extractUnifiedCompanyInfo
} from "./common";
import { MAX_RECORDS_COUNT } from "../config";

// t2名册必填字段列表
const REQUIRED_FIELDS_T2 = [
  { name: "YMTZHHM", label: "一码通账户号码" },
  { name: "CYRMC", label: "证券账户名称" },
  { name: "ZJHM", label: "证件号码" },
  { name: "GDLB", label: "股东类别" }
];

// 根据参照表中的字段列表，定义t2名册所有可能的字段
const T2_ALL_FIELDS = [
  "YMTZHHM",   // 一码通账户号码 (unifiedAccountNumber)
  "CYRMC",     // 持有人名称 (securitiesAccountName)
  "ZJHM",      // 证件号码 (shareholderId)
  "CYRLB",     // 持有人类别 (shareholderCategory)
  "QYDJR",     // 权益登记日 (reportDate)
  "CYSL",      // 持有数量 (sharesInCashAccount)
  "ZYDJZS",    // 质押冻结总数 (frozenShares)
  "TXDZ",      // 通讯地址 (contactAddress)
  "LXDH",      // 联系电话 (contactNumber)
  "YZBM",      // 邮政编码 (zipCode)
  "GLGXSFQR",  // 关联关系确认标识 (relatedPartyIndicator)
  "ZQZHHM",    // 证券账户号码 (cashAccount)
  "BZ",        // 备注 (remarks)
  "SJLX",      // 数据类型
  "XH",        // 序号
  "ZQDM",      // 证券代码
  "ZQJC",      // 证券简称
  "JGCGSL",     // 机构持股数
  "ZQZSL",     // 证券总数量
  "ZHS",       // 总户数
  "JGHSHJ"     // 机构户数合计
];

// t2名册字段映射
const T2_FIELD_MAPPING: { [key: string]: string } = {
	// 保留原始字段
	ZQZHHM: "ZQZHHM",
	CYRMC: "CYRMC",
	CYRLB: "CYRLB", // 持有人类别
	QYDJR: "QYDJR",
	CYSL: "CYSL",
	ZYDJZS: "ZYDJZS",
	TXDZ: "TXDZ",
	LXDH: "LXDH",
	YZBM: "YZBM",
	GLGXSFQR: "GLGXSFQR",
	BZ: "BZ",
	SJLX: "SJLX",
	XH: "XH",
	ZQDM: "ZQDM",
	ZQJC: "ZQJC",
	JGCGSL: "JGCGSL",
	ZQZSL: "ZQZSL",
	ZHS: "ZHS",
	JGHSHJ: "JGHSHJ",
	JGCYHJ: "JGCYHJ",
};

/**
 * 检查记录是否包含参照表中列出的所有t2名册字段
 * 
 * @param record 记录对象
 * @returns 缺失的字段列表
 */
function checkT2FieldsCompleteness(record: Record<string, any>): string[] {
  const missingFields: string[] = [];
  
  // 检查每个字段是否存在
  for (const field of T2_ALL_FIELDS) {
    if (!Object.prototype.hasOwnProperty.call(record, field)) {
      missingFields.push(field);
    }
  }
  
  return missingFields;
}

/**
 * 校验文件名中的公司代码和期数时间与SJLX=802记录中的信息是否一致
 * 
 * @param fileNameInfo 从文件名中提取的信息
 * @param basicInfo 从SJLX=802记录中提取的基本信息
 * @returns 校验结果
 */
function validateFileNameWithBasicInfo(
  fileNameInfo: { companyCode?: string; registerDate?: string },
  basicInfo: { companyCode: string; registerDate: string }
): {
  isValid: boolean;
  error?: {
    type: "COMPANY_MISMATCH" | "DATE_MISMATCH";
    message: string;
  };
} {
  // 默认校验通过
  const result = {
    isValid: true
  };
  
  // 如果文件名和基本信息中都有公司代码，进行校验
  if (fileNameInfo.companyCode && basicInfo.companyCode && 
      // 修复：在比较前对公司代码进行格式化处理，去除空格和不可见字符
      String(fileNameInfo.companyCode).trim() !== String(basicInfo.companyCode).trim()) {
    return {
      isValid: false,
      error: {
        type: "COMPANY_MISMATCH",
        message: `文件名中的公司代码(${fileNameInfo.companyCode})与名册中的公司代码(${basicInfo.companyCode})不一致`
      }
    };
  }
  
  // 如果文件名和基本信息中都有期数时间，进行校验
  if (fileNameInfo.registerDate && basicInfo.registerDate) {
    // 标准化日期格式为YYYY-MM-DD
    const fileNameDate = fileNameInfo.registerDate.replace(/\//g, '-');
    const basicInfoDate = basicInfo.registerDate.replace(/\//g, '-');
    
    // 比较日期，忽略格式差异
    const fileNameDateObj = new Date(fileNameDate);
    const basicInfoDateObj = new Date(basicInfoDate);
    
    if (!Number.isNaN(fileNameDateObj.getTime()) && !Number.isNaN(basicInfoDateObj.getTime()) && 
        fileNameDateObj.getTime() !== basicInfoDateObj.getTime()) {
      return {
        isValid: false,
        error: {
          type: "DATE_MISMATCH",
          message: `文件名中的期数时间(${fileNameInfo.registerDate})与名册中的权益登记日(${basicInfo.registerDate})不一致`
        }
      };
    }
  }
  
  return result;
}

/**
 * 解析t2名册Excel文件
 * 
 * @param file t2名册Excel文件
 * @returns 解析结果
 * @update 2025-06-04 12:14:27 使用统一提取函数extractUnifiedCompanyInfo替换原有的extractCompanyBasicInfo函数
 */
export async function parseExcelFileT2(file: File): Promise<{
  success: boolean;
  fileName: string;
  companyCode?: string;
  registerDate?: string;
  records?: any[];
  recordCount?: number;
  companyInfo?: {
    companyName: string;
    totalShares: string;
    totalShareholders: number;
    totalInstitutions: number;
    largeSharesCount: string;
    largeShareholdersCount: number;
    institutionShares: string;
  };
  error?: {
    type: "FILE_ERROR" | "COMPANY_MISMATCH" | "DATE_MISMATCH" | "MISSING_FIELDS" | "EMPTY_FIELDS";
    message: string;
    details?: string[];
  };
}> {
  try {
    // 读取Excel文件
    const { worksheet } = await readExcelWorksheet(file);

    // 将工作表转换为JSON对象数组，使用默认选项
    const rawData = XLSX.utils.sheet_to_json(worksheet, {
      raw: true,
      defval: null, // 空单元格的默认值
      blankrows: false // 跳过空行
    }) as Record<string, any>[];

    // 如果没有数据，返回错误
    if (!rawData || rawData.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "FILE_ERROR",
          message: "Excel文件不包含有效数据",
        },
      };
    }
    
    // 分析文件名，提取公司代码和报告日期
    const fileNameInfo = parseExcelFileName(file.name);
    
    // 从原始数据中提取SJLX=802的公司基本信息
    const companyBasicInfo = extractUnifiedCompanyInfo(
      rawData as Record<string, any>[],
      'TYPE_T2'
    );
    
    // 校验文件名中的公司代码和期数时间与SJLX=802记录中的信息是否一致
    const validationResult = validateFileNameWithBasicInfo(fileNameInfo, companyBasicInfo);

    if (!validationResult.isValid && validationResult.error) {
      return {
        success: false,
        fileName: file.name,
        error: validationResult.error,
      };
    }
    
    // 尝试识别特殊记录（通常是表头或汇总行）
    const specialRecords = identifySpecialRecords(rawData);
    
    // 筛选出SJLX=801的记录作为股东数据
    const shareholderRecords = rawData.filter(record => 
      record.SJLX === 801 || record.SJLX === "801"
    );
    
    // 如果没有找到SJLX=801的股东数据记录，尝试使用常规方法提取记录
    const regularRecords = shareholderRecords.length > 0 
      ? shareholderRecords 
      : extractRegularRecords(rawData, specialRecords);
    
    // 标准化字段名 - 处理可能的中文字段名或其他别名
    const standardizedRecords = regularRecords.map(record => {
      const standardRecord: Record<string, any> = {};
      
      // 复制原始记录的所有字段
      for (const key in record) {
        if (Object.prototype.hasOwnProperty.call(record, key)) {
          // 保留原始字段
          standardRecord[key] = typeof record[key] === 'string' ? record[key].trim() : record[key];
          
          // 如果字段名在映射表中，同时添加标准字段名
          const standardKey = T2_FIELD_MAPPING[key];
          if (standardKey && standardKey !== key) {
            standardRecord[standardKey] = typeof record[key] === 'string' ? record[key].trim() : record[key];
          }
        }
      }
     
      // 确保必填字段存在，尝试从其他字段推断
      if (!standardRecord.GDLB && standardRecord.CYRLB) {
        standardRecord.GDLB = typeof standardRecord.CYRLB === 'string' ? standardRecord.CYRLB.trim() : standardRecord.CYRLB;
      }
      
      // 确保必填字段有默认值
      if (!standardRecord.YMTZHHM) {
        standardRecord.YMTZHHM = "未提供";
      }
      
      if (!standardRecord.CYRMC) {
        standardRecord.CYRMC = "未提供";
      }
      
      if (!standardRecord.ZJHM) {
        standardRecord.ZJHM = "未提供";
      }
      
      if (!standardRecord.GDLB) {
        standardRecord.GDLB = "未提供";
      }
      
      return standardRecord;
    });

    // 检查是否包含必要字段
    if (standardizedRecords.length === 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "MISSING_FIELDS",
          message: "未找到有效的股东数据记录(SJLX=801)",
        },
      };
    }

    const missingFields = checkRequiredFields(standardizedRecords[0], REQUIRED_FIELDS_T2);
    
    if (missingFields.length > 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "MISSING_FIELDS",
          message: `名册中缺少字段"${missingFields.join('", "')}"`,
          details: missingFields,
        },
      };
    }
    
    // 检查必填字段是否有空值
    const emptyFields = validateRequiredFieldsContent(standardizedRecords, REQUIRED_FIELDS_T2);
    if (emptyFields.length > 0) {
      return {
        success: false,
        fileName: file.name,
        error: {
          type: "EMPTY_FIELDS",
          message: `名册中字段"${emptyFields.join('", "')}"缺少内容`,
          details: emptyFields,
        },
      };
    }
    
    // 检查是否包含参照表中的所有字段
    const missingReferenceFields = checkT2FieldsCompleteness(standardizedRecords[0]);

    // 提取公司信息
    const companyInfoFromSpecial = extractCompanyInfo(specialRecords);
    
    // 合并公司信息，绝对使用SJLX=802的信息
    const companyInfo = {
      // 绝对使用SJLX=802的信息，只有在SJLX=802没有提供信息时才使用其他来源
      companyName: companyBasicInfo.companyName,
      companyCode: companyBasicInfo.companyCode,
      registerDate: companyBasicInfo.registerDate || fileNameInfo.registerDate,
      totalShares: companyBasicInfo.totalShares,
      totalShareholders: companyBasicInfo.totalShareholders,
      totalInstitutions: companyBasicInfo.totalInstitutions,
      institutionShares: companyBasicInfo.institutionShares,
      largeSharesCount: companyInfoFromSpecial.largeSharesCount,
      largeShareholdersCount: companyInfoFromSpecial.largeShareholdersCount,
    };
    
    // 处理记录，清理字段值，但保留所有原始字段
    const processedRecords = standardizedRecords.map(record => {
      const processedRecord: Record<string, any> = {};
      
      // 处理每个字段
      for (const key in record) {
        if (Object.prototype.hasOwnProperty.call(record, key)) {
          // 如果是字符串类型，清理字符串
          if (typeof record[key] === 'string') {
            processedRecord[key] = cleanString(record[key]);
          } else {
            processedRecord[key] = record[key];
          }
        }
      }
      
      // 确保所有参照表字段都有值（即使是空值）
      for (const field of T2_ALL_FIELDS) {
        if (!Object.prototype.hasOwnProperty.call(processedRecord, field)) {
          processedRecord[field] = "";
        }
      }
      
      return processedRecord;
    });
    
    // 预处理：合并重复记录
    const mergedRecords = preprocessT2Records(processedRecords);
    // 按持股数量排序
    const sortedRecords = [...mergedRecords].sort((a, b) => {
      const sharesA = Number(a.CYSL || 0);
      const sharesB = Number(b.CYSL || 0);
      return sharesB - sharesA; // 降序排序
    });
    
    // 只保留前MAX_RECORDS_COUNT条记录
    const limitedRecords = sortedRecords.slice(0, MAX_RECORDS_COUNT);
    
    return {
      success: true,
      fileName: file.name,
      companyCode: companyInfo.companyCode,
      registerDate: companyInfo.registerDate,
      records: limitedRecords,
      recordCount: limitedRecords.length,
      companyInfo,
    };
  } catch (error) {
    return {
      success: false,
      fileName: file.name,
      error: {
        type: "FILE_ERROR",
        message: error instanceof Error ? error.message : "解析Excel文件失败",
      },
    };
  }
}

/**
 * 预处理t2名册数据
 * 根据ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配和合并
 * 
 * @param records t2名册记录
 * @returns 预处理后的记录
 * @update 2025-06-04 10:06:17 增强对所有字段的编码处理，确保中文正确显示
 * @update 2025-06-04 09:58:35 增强对缺失CYSL字段的处理
 */
export function preprocessT2Records(records: any[]): any[] {
  if (!records || records.length === 0) {
    return [];
  }
  
  // 使用Map存储合并后的记录，键为"ZJHM+YMTZHHM"
  const mergedRecordsMap = new Map<string, any>();
  
  // 遍历所有记录
  for (const record of records) {
    // 处理所有字段的编码问题
    const processedRecord: Record<string, any> = {};
    for (const key in record) {
      if (Object.prototype.hasOwnProperty.call(record, key)) {
        // 对所有字符串类型的字段应用fixEncoding
        if (typeof record[key] === 'string') {
          processedRecord[key] = fixEncoding(record[key]);
        } else {
          processedRecord[key] = record[key];
        }
      }
    }
    
    // 生成组合键
    const zjhm = processedRecord.ZJHM || '';
    const ymtzhhm = processedRecord.YMTZHHM || '';
    const key = `${zjhm}|${ymtzhhm}`;
    
    // 确保记录中有CYSL字段，如果没有，尝试从其他可能的字段获取或设置为0
    if (!processedRecord.CYSL) {
      // 尝试从其他可能的字段名获取持股数量
      const possibleSharesFields = ['CGSL', 'ZQZL', 'CYZL', 'CGGS', 'CGZL', 'CYGS'];
      for (const field of possibleSharesFields) {
        if (processedRecord[field] && !Number.isNaN(Number(processedRecord[field]))) {
          processedRecord.CYSL = processedRecord[field];
          break;
        }
      }
      
      // 如果仍然没有有效的持股数量，设置为0
      if (!processedRecord.CYSL) {
        processedRecord.CYSL = "0";
      }
    }
    
    // 如果Map中已存在该键，合并记录
    if (mergedRecordsMap.has(key)) {
      const existingRecord = mergedRecordsMap.get(key);
      
      // 叠加持股数量
      const existingShares = Number(existingRecord.CYSL || 0);
      const newShares = Number(processedRecord.CYSL || 0);
      existingRecord.CYSL = (existingShares + newShares).toString();
      
      // 其他字段保留先到先得的值
    } else {
      // 如果Map中不存在该键，添加记录
      mergedRecordsMap.set(key, { ...processedRecord });
    }
  }
  
  // 将Map转换为数组
  return Array.from(mergedRecordsMap.values());
}

/**
 * 合并多个t2名册的记录
 * 
 * @param recordsArray 多个t2名册记录数组
 * @returns 合并后的记录
 */
export function mergeT2Records(recordsArray: any[][]): any[] {
  // 将所有记录合并为一个数组
  const allRecords = recordsArray.flat();
  
  // 使用预处理函数合并记录
  return preprocessT2Records(allRecords);
} 