import { apiClient } from "@shared/lib/api-client";
import { decryptData, createEncryptedRequestAsync } from "@repo/utils";

// 定义API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data: string | any;
}

// 定义股东数据结构
interface ShareholderData {
	shareholders: Array<Record<string, any>>;
	total?: number;
	page?: number;
	limit?: number;
	[key: string]: any;
}


// 导出股东API客户端
export const shareholderApi = {
	/**
	 * 获取股东列表
	 * @param organizationId 组织ID
	 * @param params 查询参数
	 * @returns 股东列表
	 */
	getShareholderList: async (
		organizationId: string, 
		params?: {
			registerDate?: string;
			page?: number;
			limit?: number;
			searchTerm?: string;
			sortBy?: string;
			sortOrder?: 'asc' | 'desc';
		}
	): Promise<ShareholderData> => {
		try {
			// 创建加密且带签名的请求参数
			const requestData = await createEncryptedRequestAsync({ 
				organizationId,
				...params
			});

			// 发送请求到股东查询接口 (使用股东名册的股东查询接口)
			const response = await apiClient["shareholder-registry"].shareholders.$post({
				json: requestData
			});

			// 处理响应
			const responseData = await response.json() as ApiResponse;
			
			let resultData: ShareholderData = { shareholders: [] };
			
			// 如果data是字符串，直接解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data);
				resultData = (typeof decryptedData === 'object' ? decryptedData : {}) as ShareholderData;
				
				// 如果返回的是字符串，尝试解析为JSON对象
				if (typeof decryptedData === 'string') {
					try {
						resultData = JSON.parse(decryptedData) as ShareholderData;
					} catch (e) {
						// 解析失败，保持原样
						resultData = { shareholders: [] };
					}
				}
			} 
			// 否则返回原始数据
			else {
				resultData = responseData.data as ShareholderData;
			}
			
			// 验证数据结构
			if (resultData && typeof resultData === 'object') {
				if (!resultData.shareholders) {
					// 股东字段缺失
					resultData.shareholders = [];
				} else if (!Array.isArray(resultData.shareholders)) {
					// 尝试将shareholders转换为数组形式
					if (resultData.shareholders && typeof resultData.shareholders === 'object') {
						resultData.shareholders = [resultData.shareholders];
					}
				}
			}
			
			return resultData;
		} catch (error) {
			// 返回错误对象，不打印错误到控制台
			return { shareholders: [] };
		}
	},

	/**
	 * 获取股东类型列表
	 * @param organizationId 组织ID
	 * @returns 股东类型列表
	 * @created 2024-07-01 15:30:45.621
	 */
	getShareholderTypes: async (organizationId: string): Promise<string[]> => {
		try {
			// 创建加密且带签名的请求参数
			const requestData = await createEncryptedRequestAsync({ 
				organizationId 
			});

			// 发送请求到股东类型查询接口
			const response = await apiClient["shareholder-registry"]["shareholder-types"].$post({
				json: requestData
			});

			// 处理响应
			const responseData = await response.json() as ApiResponse;

			let shareholderTypes: string[] = [];
			
			// 如果data是字符串，直接解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data);
				
				// 尝试将解密后的数据解析为JSON对象
				try {
					// 如果是JSON字符串，解析它
					const parsedData = typeof decryptedData === 'string' 
						? JSON.parse(decryptedData) 
						: decryptedData;
					
					// 检查是否有shareholderTypes字段
					if (parsedData && typeof parsedData === 'object' && Array.isArray(parsedData.shareholderTypes)) {
						shareholderTypes = parsedData.shareholderTypes;
					}
				} catch (error) {
					console.error('解析股东类型数据失败:', error);
				}
			} 
			// 否则返回原始数据
			else if (responseData.data?.shareholderTypes) {
				shareholderTypes = Array.isArray(responseData.data.shareholderTypes) 
					? responseData.data.shareholderTypes 
					: [];
			}
			
			return shareholderTypes;
		} catch (error) {
			// 返回空数组，不打印错误到控制台
			return [];
		}
	},

	/**
	 * 获取股东持股变化分析数据
	 * @param organizationId 组织ID
	 * @param params 查询参数
	 * @returns 股东持股变化分析数据
	 * @created 2024-07-01 15:30:45.621
	 * @modified 2025-06-17 17:09:31.041 - 更新sortType参数支持，支持按排名、期数日期或具体期数日期排序
	 * <AUTHOR>
	 * @time 2025-06-17 17:09:31.041
	 */
	getShareholdingChanges: async (
		organizationId: string,
		params: {
			startDate: string;
			endDate: string;
			shareholderType?: string;
			searchTerm?: string;
			sortType?: 'rank' | 'date' | string; // 修改：排序类型，支持按排名、期数日期或具体期数日期(YYYY-MM-DD)排序
			sortOrder?: 'asc' | 'desc';
			page?: number;
			limit?: number;
		}
	) => {
		try {
			// 创建加密且带签名的请求参数
			const requestData = await createEncryptedRequestAsync({ 
				organizationId,
				...params
			});

			// 发送请求到股东持股变化分析接口
			const response = await apiClient["shareholder-registry"]["shareholding-changes"].$post({
				json: requestData
			});

			// 处理响应
			const responseData = await response.json() as ApiResponse;
			
			let resultData = {
				analysisRange: {
					startDate: params.startDate,
					endDate: params.endDate,
					totalPeriods: 0
				},
				availableDates: [],
				shareholderChanges: [],
				pagination: {
					page: params.page || 1,
					limit: params.limit || 30,
					total: 0,
					totalPages: 0
				}
			};
			
			// 定义结果数据类型
			type ShareholdingChangesData = typeof resultData;
			
			// 如果data是字符串，直接解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data) as ShareholdingChangesData | string;
				
				if (typeof decryptedData === 'object') {
					resultData = decryptedData as ShareholdingChangesData;
				} else if (typeof decryptedData === 'string') {
					try {
						resultData = JSON.parse(decryptedData) as ShareholdingChangesData;
					} catch (e) {
						// 解析失败，保持原样
					}
				}
			} 
			// 否则返回原始数据
			else if (typeof responseData.data === 'object') {
				resultData = responseData.data as ShareholdingChangesData;
			}
			
			return resultData;
		} catch (error) {
			// 返回默认结构，不打印错误到控制台
			return {
				analysisRange: {
					startDate: params.startDate,
					endDate: params.endDate,
					totalPeriods: 0
				},
				availableDates: [],
				shareholderChanges: [],
				pagination: {
					page: params.page || 1,
					limit: params.limit || 30,
					total: 0,
					totalPages: 0
				}
			};
		}
	},
};
