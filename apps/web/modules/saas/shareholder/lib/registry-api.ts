import { apiClient } from "@shared/lib/api-client";
import { decryptData, createEncryptedRequestAsync } from "@repo/utils";

// 定义API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data: string | any;
	error?: {
		code: string;
		message: string;
		details?: any;
	};
}

// 确保返回的期数数据格式正确
function normalizeRegisterDates(data: any): { registerDates: any[] } {
	// 如果已经是正确格式，直接返回
	if (
		data &&
		typeof data === "object" &&
		data.registerDates &&
		Array.isArray(data.registerDates)
	) {
		return { registerDates: data.registerDates };
	}

	// 如果数据本身是数组
	if (Array.isArray(data)) {
		return { registerDates: data };
	}

	// 如果是对象，尝试找到有数组的属性
	if (data && typeof data === "object") {
		for (const key in data) {
			if (Array.isArray(data[key])) {
				return { registerDates: data[key] };
			}
		}
	}

	// 默认返回空数组
	return { registerDates: [] };
}

// 股东名册API客户端
export const shareholderRegistryApi = {
	/**
	 * 上传股东名册
	 * @param organizationId 组织ID
	 * @param data 股东名册数据
	 * @returns 上传结果
	 */
	uploadShareholderRegistry: async (
		organizationId: string,
		data: {
			fileName: string;
			recordCount: number;
			registerDate: string;
			companyCode: string;
			companyInfo: {
				companyName: string;
				totalShares: string;
				totalShareholders: number;
				totalInstitutions: number;
				largeSharesCount: string;
				largeShareholdersCount: number;
				institutionShares?: string;
				/**
				 * 新增信用股东统计字段
				 *
				 * <AUTHOR>
				 * @created 2025-06-13 12:54:01
				 * @description 支持05名册和T3名册的信用股东统计数据
				 */
				marginAccounts?: number; // 信用总户数（全量数据统计）
				marginShares?: string; // 信用总持股（全量数据统计）
			};
			shareholders: Array<{
				// 使用原始字段名，支持01和05名册
				// 01名册字段
				YMTH?: string;               // 一码通账户号码
				ZQZHMC?: string;             // 证券账户名称
				ZJDM?: string;               // 证件号码
				CYRLBMS?: string;            // 持有人类别
				CGSL?: string;               // 总持股数量
				XSGSL?: string;              // 限售股数量
				CGBL?: string;               // 持股比例
				DJGS?: string;               // 冻结股数
				PTZQZH?: string;             // 普通证券账户
				PTZHCGSL?: string;           // 普通账户持股数量
				XYZQZH?: string;             // 信用证券账户
				XYZHCGSL?: string;           // 信用账户持股数量
				TXDZ?: string;               // 通讯地址
				DHHM?: string;               // 联系电话
				YZBM?: string;               // 邮政编码
				GLGXBS?: string;             // 关联关系确认标识
				KHLB?: string;               // 客户类别
				BZ?: string;                 // 备注
				
				// 05名册字段
				XYZHMC?: string;             // 信用证券账户名称
				XYZHZJDM?: string;           // 信用证券账户证件号码
				HZZQZH?: string;             // 汇总账户号码
				HZZHMC?: string;             // 汇总账户名称
				GFXZ?: string;               // 股份性质
				
				// 兼容旧版本API（向后兼容）
				shareholderId?: string;
				unifiedAccountNumber?: string;
				securitiesAccountName?: string;
				shareholderCategory?: string;
				numberOfShares?: string;
				lockedUpShares?: string;
				shareholdingRatio?: string;
				frozenShares?: string;
				cashAccount?: string;
				sharesInCashAccount?: string;
				marginAccount?: string;
				sharesInMarginAccount?: string;
				contactAddress?: string;
				contactNumber?: string;
				zipCode?: string;
				relatedPartyIndicator?: string;
				clientCategory?: string;
				remarks?: string;
				marginCollateralAccountNumber?: string;
				marginCollateralAccountName?: string;
				natureOfShares?: string;
			}>;
		},
	) => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({
			organizationId,
			...data,
		});

		try {
			// 发送请求到股东名册上传接口
			const response = await apiClient["shareholder-registry"].upload.$post({
				json: requestData
			});
			
			// 获取响应数据
			const responseData = await response.json() as ApiResponse;
			
			// 检查响应状态码，处理非200响应
			if (responseData.code !== 200) {				
				// 直接返回错误响应，不抛出错误
				return {
					success: false,
					code: responseData.code,
					error: responseData.error || {
						code: "UPLOAD_FAILED",
						message: responseData.message || "上传股东名册失败"
					}
				};
			}
			
			// 如果data是字符串，解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data);
				return {
					success: true,
					...(typeof decryptedData === 'object' ? decryptedData : { data: decryptedData })
				};
			}
			
			// 返回原始数据
			return {
				success: true,
				...(typeof responseData.data === 'object' ? responseData.data : { data: responseData.data })
			};
		} catch (error) {
			// 返回错误对象，而不是抛出错误
			return {
				success: false,
				error: {
					code: "API_ERROR",
					message: error instanceof Error ? error.message : "上传股东名册失败"
				}
			};
		}
	},

	/**
	 * 获取股东名册列表
	 * @param organizationId 组织ID
	 * @param params 查询参数
	 * @returns 股东名册列表
	 */
	getShareholderRegistryList: async (
		organizationId: string,
		params?: {
			page?: number;
			limit?: number;
			companyCode?: string;
		},
	) => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({
			organizationId,
			...params,
		});

		try {
			// 发送请求到股东名册列表接口
			const response = await apiClient["shareholder-registry"].list.$post({
				json: requestData
			});
			
			// 获取响应数据
			const responseData = await response.json() as ApiResponse;
			
			// 检查响应状态
			if (responseData.code !== 200) {
				throw new Error(responseData.message || "获取股东名册列表失败");
			}
			
			let decryptedData: any;
			
			// 解密数据
			if (typeof responseData.data === 'string') {
				try {
					// 尝试解密
					decryptedData = decryptData(responseData.data);
					
					// 如果解密后是字符串，尝试解析为JSON
					if (typeof decryptedData === 'string') {
						try {
							decryptedData = JSON.parse(decryptedData);
						} catch (error) {
							// 解析失败，使用默认结构
							decryptedData = {
								registries: [],
								pagination: {
									total: 0,
									page: params?.page || 1,
									limit: params?.limit || 10,
									totalPages: 0
								}
							};
						}
					}
				} catch (error) {
					// 解密失败，使用默认结构
					decryptedData = {
						registries: [],
						pagination: {
							total: 0,
							page: params?.page || 1,
							limit: params?.limit || 10,
							totalPages: 0
						}
					};
				}
			} else {
				// 如果数据不是字符串，直接使用
				decryptedData = responseData.data;
			}
			
			// 确保返回格式正确
			if (!decryptedData || typeof decryptedData !== 'object') {
				return {
					registries: [],
					pagination: {
						total: 0,
						page: params?.page || 1,
						limit: params?.limit || 10,
						totalPages: 0
					}
				};
			}
			
			// 确保registries是数组
			if (!decryptedData.registries) {
				decryptedData.registries = [];
			}
			
			// 确保pagination存在
			if (!decryptedData.pagination) {
				decryptedData.pagination = {
					total: 0,
					page: params?.page || 1,
					limit: params?.limit || 10,
					totalPages: 0
				};
			}
			
			return decryptedData;
		} catch (error) {
			// 失败时返回默认结构
			return {
				registries: [],
				pagination: {
					total: 0,
					page: params?.page || 1,
					limit: params?.limit || 10,
					totalPages: 0
				}
			};
		}
	},

	/**
	 * 获取期数日期列表
	 * @param organizationId 组织ID
	 * @param companyCode 公司代码(可选)
	 * @returns 期数日期列表
	 */
	getRegisterDates: async (organizationId: string, companyCode?: string) => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({
			organizationId,
			companyCode,
		});

		try {
			const response = await apiClient["shareholder-registry"]["register-dates"].$post({
				json: requestData
			});
			
			// 获取响应数据
			const responseData = await response.json() as ApiResponse;
			
			let decryptedData: any;
			
			try {
				// 如果data是字符串，尝试解密
				if (typeof responseData.data === 'string') {
					try {
						decryptedData = decryptData(responseData.data);
						
						// 如果解密后是字符串，尝试解析为JSON
						if (typeof decryptedData === 'string') {
							try {
								decryptedData = JSON.parse(decryptedData);
							} catch (error) {
								// 解析失败，使用空对象
								decryptedData = {};
							}
						}
					} catch (error) {
						// 解密失败，使用空对象
						decryptedData = {};
					}
				} else {
					// 如果不是字符串，直接使用
					decryptedData = responseData.data;
				}
			} catch (error) {
				// 处理过程中出错，使用空对象
				decryptedData = {};
			}
			
			// 标准化数据格式
			return normalizeRegisterDates(decryptedData);
		} catch (error) {
			// 发生错误时返回空数组
			return { registerDates: [] };
		}
	},

	/**
	 * 删除股东名册
	 * @param registryId 股东名册ID
	 * @returns 删除结果
	 */
	deleteShareholderRegistry: async (registryId: string) => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({
			registryId,
		});

		try {
			const response = await apiClient["shareholder-registry"].delete.$post({
				json: requestData
			});
			
			// 获取响应数据
			const responseData = await response.json() as ApiResponse;
			
			// 检查响应状态码
			if (responseData.code !== 200) {
				// 直接返回错误响应，不抛出错误
				return {
					success: false,
					code: responseData.code,
					error: responseData.error || {
						code: "DELETE_FAILED",
						message: responseData.message || "删除股东名册失败"
					}
				};
			}
			
			// 如果data是字符串，解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data);
				return {
					success: true,
					...(typeof decryptedData === 'object' ? decryptedData : { data: decryptedData })
				};
			}
			
			// 返回原始数据
			return {
				success: true,
				...(typeof responseData.data === 'object' ? responseData.data : { data: responseData.data })
			};
		} catch (error) {
			// 返回错误对象，而不是抛出错误
			return {
				success: false,
				error: {
					code: "API_ERROR",
					message: error instanceof Error ? error.message : "删除股东名册失败"
				}
			};
		}
	},

	/**
	 * 批量删除股东名册
	 * @param registryIds 股东名册ID数组
	 * @returns 批量删除结果
	 */
	batchDeleteShareholderRegistry: async (registryIds: string[]) => {
		// 创建加密且带签名的请求参数
		const requestData = await createEncryptedRequestAsync({
			registryIds,
		});

		try {
			const response = await apiClient["shareholder-registry"]["batch-delete"].$post({
				json: requestData
			});
			
			// 获取响应数据
			const responseData = await response.json() as ApiResponse;
			
			// 检查响应状态码
			if (responseData.code !== 200) {
				// 直接返回错误响应，不抛出错误
				return {
					success: false,
					code: responseData.code,
					error: responseData.error || {
						code: "BATCH_DELETE_FAILED",
						message: responseData.message || "批量删除股东名册失败"
					}
				};
			}
			
			// 如果data是字符串，解密
			if (typeof responseData.data === 'string') {
				const decryptedData = decryptData(responseData.data);
				return {
					success: true,
					...(typeof decryptedData === 'object' ? decryptedData : { data: decryptedData })
				};
			}
			
			// 返回原始数据
			return {
				success: true,
				...(typeof responseData.data === 'object' ? responseData.data : { data: responseData.data })
			};
		} catch (error) {
			// 返回错误对象，而不是抛出错误
			return {
				success: false,
				error: {
					code: "API_ERROR",
					message: error instanceof Error ? error.message : "批量删除股东名册失败"
				}
			};
		}
	},
};
