"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@ui/components/card";
import { 
  FilesIcon,
  CheckCircleIcon,
  AlertCircleIcon,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { useShareholderRegistry } from "@saas/shareholder/hooks/useShareholders";
import { parseShareholderRegistryFile } from "@saas/shareholder/lib/file-parser";
import type { ShareholderRegistryParseResult } from "@saas/shareholder/lib/dbf-parser";
import { ShareholderRegistryHelpDialog } from "../dialogs";
import { ShareholderRegistryImportErrorDialog } from "../dialogs/ShareholderRegistryImportErrorDialog";
import { FileUploadArea } from "./FileUploadArea";
import { FileList } from "./FileList";
import { ImportProgress } from "./ImportProgress";
import { 
  filterFiles, 
  filterDuplicateFiles, 
  isRegistryExcelFile, 
  showImportSummary,
  createProgressTimer,
  finalizeProgressAnimation,
  showFailureAnimation,
  validateFiles
} from "./utils";

interface ShareholderRegistryImportProps {
  organizationId: string;
  onComplete?: () => void;
}

/**
 * 股东名册导入表单组件
 * 支持拖拽上传和文件选择
 * 支持DBF和Excel(XLS/XLSX)文件解析和验证
 * 支持上传多个文件
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.organizationId - 组织ID
 * @param {Function} [props.onComplete] - 导入完成的回调函数
 * 
 * @update 2025-06-12 11:41:51 优化错误处理逻辑，多文件导入时一次只展示一个错误
 * @update 2025-06-04 16:26:45 重构为模块化组件结构，修复同时多选t名册时预处理状态不正确的问题
 * @update 2025-06-03 20:03:10 修复t2、t3名册文件预处理问题，扩展预处理逻辑以支持所有类型的名册文件
 * @update 2025-06-03 修复t1名册文件重复校验问题，使用预处理结果直接进入导入阶段，提升用户体验
 * @update 2025-06-03 修复t1名册字段映射问题，优化预处理流程，确保在选择文件后立即进行预处理
 * @update 2025-06-03 添加对Excel(XLS/XLSX)文件的支持，特别是t1名册格式
 * @update 2025-05-28 更新为支持DBF、XLS、XLSX和ZIP文件格式
 * @update 2025-05-26 优化了上传进度条，在调用API前开始模拟进度，在90%处等待后端响应，给用户更好的反馈
 * @update 2025-05-20 更新了导入逻辑，优化了导入体验
 * @update 2025-05-19 更新了校验流程，优化了导入体验
 * @update 2025-05-19 更新错误处理流程，统一展示错误信息
 */
export function ShareholderRegistryImport({
  organizationId,
  onComplete,
}: ShareholderRegistryImportProps) {
  // 基础状态
  const [files, setFiles] = useState<File[]>([]);
  const [currentFileIndex, setCurrentFileIndex] = useState<number>(-1);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importPhase, setImportPhase] = useState<"idle" | "validating" | "importing">("idle");
  const [parseResults, setParseResults] = useState<ShareholderRegistryParseResult[]>([]);
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const [validationError, setValidationError] = useState<{
    message: string;
    fileIndex: number;
    fileName?: string;
  } | null>(null);
  
  // 进度定时器引用
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 用于跟踪正在预处理的文件，解决多个t名册同时预处理状态问题
  const processingFilesRef = useRef<Set<string>>(new Set());
  
  // 使用自定义钩子来处理上传
  const { uploadRegistry, isUploading } = useShareholderRegistry(organizationId);
  
  // 处理文件选择
  const handleFileSelect = async (selectedFiles: File[]) => {
    // 过滤不支持的文件格式和大小限制
    const validFiles = filterFiles(selectedFiles);
    
    if (validFiles.length === 0) {
      setError("请选择有效的DBF或Excel文件，且文件大小不超过20MB");
      return;
    }
    
    // 如果当前有文件且处于闲置状态，则追加新文件而不是替换
    if (files.length > 0 && importPhase === "idle") {
      // 检查是否有重复文件
      const newFiles = filterDuplicateFiles(files, validFiles);
      
      if (newFiles.length === 0) {
        setError("所选文件已在列表中");
        return;
      }
      
      // 追加新文件
      setFiles(prevFiles => [...prevFiles, ...newFiles]);
      // 清除可能的错误信息，但保留其他状态
      setError(null);
      
      // 对新文件进行预处理
      await preprocessFiles(newFiles, files.length);
    } else {
      // 如果当前没有文件或者不是闲置状态，则重置状态并设置新文件
      resetState();
      setFiles(validFiles);
      
      // 对所有文件进行预处理
      await preprocessFiles(validFiles, 0);
    }
  };
  
  // 预处理文件函数
  const preprocessFiles = async (filesToProcess: File[], startIndex: number) => {
    try {
      // 设置为验证状态
      setImportPhase("validating");
      
      // 创建一个预处理任务数组
      const preprocessTasks = filesToProcess.map((file, index) => {
        const fileIndex = startIndex + index;
        
        // 如果不是Excel名册文件，直接返回null（不需要预处理）
        if (!isRegistryExcelFile(file)) {
          return Promise.resolve(null);
        }
        
        // 将文件标记为正在处理
        const fileKey = `${file.name}-${file.size}`;
        processingFilesRef.current.add(fileKey);
        
        // 返回一个预处理任务
        return (async () => {
          try {
            // 设置当前处理的文件索引
            setCurrentFileIndex(fileIndex);
            
            // 解析文件
            const result = await parseShareholderRegistryFile(file);
            
            // 处理完成后，从正在处理集合中移除
            processingFilesRef.current.delete(fileKey);
            
            return { result, fileIndex };
          } catch (error) {
            // 处理错误，从正在处理集合中移除
            processingFilesRef.current.delete(fileKey);
            return { error, fileIndex };
          }
        })();
      });
      
      // 并行执行所有预处理任务
      const results = await Promise.all(preprocessTasks);
      
      // 更新解析结果
      const newResults = [...parseResults];
      
      // 填充解析结果数组
      results.forEach(taskResult => {
        if (!taskResult) {
          return; // 跳过不需要预处理的文件
        }
        
        const { result, fileIndex, error: taskError } = taskResult;
        
        // 确保数组有足够的位置
        while (newResults.length <= fileIndex) {
          newResults.push({ success: false, fileName: "" });
        }
        
        if (taskError) {
          // 处理错误
          newResults[fileIndex] = {
            success: false,
            fileName: filesToProcess[fileIndex - startIndex].name,
            error: {
              type: "FILE_ERROR",
              message: (taskError as Error).message || "文件预处理失败"
            }
          };
        } else if (result) {
          // 更新结果
          newResults[fileIndex] = {
            ...result,
            success: result.success
          };
          
          // 如果解析失败，显示错误
          if (!result.success) {
            setValidationError({
              message: result.error?.message || "文件验证失败", 
              fileIndex: fileIndex,
              fileName: filesToProcess[fileIndex - startIndex].name,
            });
          }
        }
      });
      
      // 更新UI显示
      setParseResults(newResults);
      
      // 如果所有文件都已处理完成，恢复闲置状态
      if (processingFilesRef.current.size === 0) {
        setImportPhase("idle");
        setCurrentFileIndex(-1);
      }
    } catch (error: any) {
      // 处理错误
      setError(error?.message || "文件预处理失败");
      setImportPhase("idle");
      setCurrentFileIndex(-1);
      processingFilesRef.current.clear();
    }
  };
  
  // 重置状态
  const resetState = () => {
    setFiles([]);
    setError(null);
    setSuccess(false);
    setUploadProgress(0);
    setParseResults([]);
    setCurrentFileIndex(-1);
    setImportPhase("idle");
    setValidationError(null);
    
    // 清除正在处理的文件集合
    processingFilesRef.current.clear();
    
    // 清除进度定时器
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }
  };
  
  // 清除选择的文件
  const clearFiles = useCallback(() => {
    resetState();
  }, []);
  
  // 移除单个文件
  const removeFile = useCallback((index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
    
    // 如果有对应的解析结果，也移除
    setParseResults((prev) => {
      const newResults = [...prev];
      if (index < newResults.length) {
        newResults.splice(index, 1);
      }
      return newResults;
    });
  }, []);
  
  // 处理验证错误
  const handleValidationError = (
    message: string,
    fileIndex: number,
    results: ShareholderRegistryParseResult[] = [],
  ) => {
    setValidationError({
      message,
      fileIndex,
      fileName: files[fileIndex]?.name,
    });
    setImportPhase("idle");
    
    // 存储当前的结果数组，以便后续继续处理
    if (results.length > 0) {
      setParseResults(results);
    }
  };
  
  // 处理跳过当前文件
  const handleSkipFile = useCallback(() => {
    if (validationError && validationError.fileIndex >= 0) {
      // 创建一个新的解析结果数组，将失败的文件标记为跳过
      const newResults = [...parseResults];
      newResults[validationError.fileIndex] = {
        success: false,
        fileName: files[validationError.fileIndex].name,
        error: {
          type: "FILE_ERROR",
          message: "已跳过此文件",
        },
      };
      
      // 更新UI
      setParseResults(newResults);
      
      // 清除错误并继续下一个文件
      setValidationError(null);
      
      // 继续处理下一个文件
      validateAndImportNext(validationError.fileIndex + 1, newResults);
    }
  }, [validationError, parseResults, files]);
  
  // 在组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }
    };
  }, []);
  
  // 验证并导入下一个文件
  const validateAndImportNext = async (
    nextIndex: number,
    currentResults: ShareholderRegistryParseResult[] = [],
  ) => {
    if (nextIndex >= files.length) {
      // 所有文件处理完成
      setImportPhase("idle");
      
      // 使用传入的currentResults数组，而不是依赖parseResults状态
      showImportSummary(files, currentResults);
      setParseResults(currentResults);
      setSuccess(true);
      setCurrentFileIndex(-1);
      
      // 立即调用onComplete函数，不再延迟
      if (onComplete) {
        onComplete();
      }
      return;
    }
    
    setCurrentFileIndex(nextIndex);
    
    // 检查是否已经有预处理的结果
    const existingResult = parseResults[nextIndex];
    
    if (existingResult && existingResult.success === true) {
      // 如果已经有成功的预处理结果，直接进入导入阶段
      // 创建新的结果数组，复制现有结果
      const newResults = [...currentResults];
      while (newResults.length <= nextIndex) {
        newResults.push({ success: false, fileName: "" });
      }
      newResults[nextIndex] = existingResult;
      
      // 直接进入导入阶段
      setImportPhase("importing");
      setUploadProgress(0);
      
      // 在调用uploadRegistry之前就开始模拟进度条
      progressTimerRef.current = createProgressTimer(setUploadProgress);
      
      // 上传到服务器
      try {
        await uploadRegistry({
          fileName: existingResult.fileName,
          recordCount: existingResult.recordCount || 0,
          registerDate: existingResult.registerDate || "",
          companyCode: existingResult.companyCode || "",
          companyInfo: existingResult.companyInfo || {
            companyName: "",
            totalShares: "0",
            totalShareholders: 0,
            totalInstitutions: 0,
            largeSharesCount: "0",
            largeShareholdersCount: 0,
            institutionShares: "0",
          },
          shareholders: existingResult.records?.map((record) => {
            // 直接使用原始字段名，不转换为数据库字段名
            // 后端会根据名册类型自动进行字段映射
            return {
              // 保留记录中的所有原始字段
              ...record,
              // 确保所有数值字段都是字符串类型
              CGSL: record.CGSL ? String(record.CGSL) : "0",
              XSGSL: record.XSGSL ? String(record.XSGSL) : "0",
              CGBL: record.CGBL ? String(record.CGBL) : "0",
              DJGS: record.DJGS ? String(record.DJGS) : "0",
              PTZHCGSL: record.PTZHCGSL ? String(record.PTZHCGSL) : "0",
              XYZHCGSL: record.XYZHCGSL ? String(record.XYZHCGSL) : "0"
            };
          }) || [],
        });
        
        // 清除进度模拟定时器
        if (progressTimerRef.current) {
          clearInterval(progressTimerRef.current);
          progressTimerRef.current = null;
        }
        
        // 设置进度为100%，模拟从当前进度到100%的平滑过渡
        finalizeProgressAnimation(uploadProgress, setUploadProgress);
        
        // 标记当前文件为成功，但不依赖状态更新
        newResults[nextIndex] = {
          ...existingResult,
          success: true,
        };
        
        // 更新UI，但主要是为了显示
        setParseResults(newResults);
        
        // 继续处理下一个文件，传递当前的结果数组
        validateAndImportNext(nextIndex + 1, newResults);
      } catch (uploadError: any) {
        // 清除进度模拟定时器
        if (progressTimerRef.current) {
          clearInterval(progressTimerRef.current);
          progressTimerRef.current = null;
        }
        
        // 显示上传失败，进度回退到0
        showFailureAnimation(uploadProgress, setUploadProgress);
        
        // 显示上传错误
        handleValidationError(
          uploadError.message || "上传失败",
          nextIndex,
          newResults,
        );
      }
      return;
    }
    
    // 如果没有预处理结果或预处理失败，执行正常的验证流程
    setImportPhase("validating");
    
    try {
      // 解析文件
      const result = await parseShareholderRegistryFile(files[nextIndex]);
      
      // 创建新的结果数组，而不是修改parseResults状态
      const newResults = [...currentResults];
      while (newResults.length <= nextIndex) {
        newResults.push({ success: false, fileName: "" });
      }
      
      newResults[nextIndex] = {
        ...result,
        success: result.success,
      };
      
      // 更新UI显示，但不依赖这个状态进行后续处理
      setParseResults(newResults);
      
      // 检查解析是否成功
      if (!result.success) {
        // 显示验证错误
        handleValidationError(
          result.error?.message || "文件验证失败",
          nextIndex,
          newResults,
        );
        return;
      }
      
      // 验证成功，开始导入
      setImportPhase("importing");
      setUploadProgress(0);
      
      // 在调用uploadRegistry之前就开始模拟进度条
      progressTimerRef.current = createProgressTimer(setUploadProgress);
      
      // 上传到服务器
      try {
        await uploadRegistry({
          fileName: result.fileName,
          recordCount: result.recordCount || 0,
          registerDate: result.registerDate || "",
          companyCode: result.companyCode || "",
          companyInfo: result.companyInfo || {
            companyName: "",
            totalShares: "0",
            totalShareholders: 0,
            totalInstitutions: 0,
            largeSharesCount: "0",
            largeShareholdersCount: 0,
            institutionShares: "0",
          },
          shareholders: result.records?.map((record) => {
            // 直接使用原始字段名，不转换为数据库字段名
            // 后端会根据名册类型自动进行字段映射
            return {
              // 保留记录中的所有原始字段
              ...record,
              // 确保所有数值字段都是字符串类型
              CGSL: record.CGSL ? String(record.CGSL) : "0",
              XSGSL: record.XSGSL ? String(record.XSGSL) : "0",
              CGBL: record.CGBL ? String(record.CGBL) : "0",
              DJGS: record.DJGS ? String(record.DJGS) : "0",
              PTZHCGSL: record.PTZHCGSL ? String(record.PTZHCGSL) : "0",
              XYZHCGSL: record.XYZHCGSL ? String(record.XYZHCGSL) : "0"
            };
          }) || [],
        });
        
        // 清除进度模拟定时器
        if (progressTimerRef.current) {
          clearInterval(progressTimerRef.current);
          progressTimerRef.current = null;
        }
        
        // 设置进度为100%，模拟从当前进度到100%的平滑过渡
        finalizeProgressAnimation(uploadProgress, setUploadProgress);
        
        // 标记当前文件为成功，但不依赖状态更新
        newResults[nextIndex] = {
          ...result,
          success: true,
        };
        
        // 更新UI，但主要是为了显示
        setParseResults(newResults);
        
        // 继续处理下一个文件，传递当前的结果数组
        validateAndImportNext(nextIndex + 1, newResults);
      } catch (uploadError: any) {
        // 清除进度模拟定时器
        if (progressTimerRef.current) {
          clearInterval(progressTimerRef.current);
          progressTimerRef.current = null;
        }
        
        // 显示上传失败，进度回退到0
        showFailureAnimation(uploadProgress, setUploadProgress);
        
        // 显示上传错误
        handleValidationError(
          uploadError.message || "上传失败",
          nextIndex,
          newResults,
        );
      }
    } catch (err: any) {
      // 显示解析错误
      handleValidationError(
        err.message || "文件验证失败",
        nextIndex,
        currentResults,
      );
    }
  };
  
  // 处理文件上传
  const handleUpload = async () => {
    if (files.length === 0 || !organizationId) {
      setError("请先选择要上传的文件");
      return;
    }
    
    try {
      setError(null);
      
      // 在点击开始导入按钮时校验文件
      const validationResult = validateFiles(files);
      
      if (!validationResult.allValid) {
        // 有无效文件，但我们只显示第一个错误，不再展示所有错误
        if (validationResult.errorFiles.length > 0) {
          // 获取第一个错误文件
          const firstErrorFile = validationResult.errorFiles[0];
          const fileIndex = files.findIndex(f => 
            f.name === firstErrorFile.file.name && 
            f.size === firstErrorFile.file.size
          );
          
          if (fileIndex >= 0) {
            // 设置验证错误对话框，只显示第一个错误
            setValidationError({
              message: firstErrorFile.reason,
              fileIndex: fileIndex,
              fileName: firstErrorFile.file.name,
            });
            return;
          }
        }
      }
      
      // 使用当前的parseResults而不是清空它，以保留预处理结果
      setValidationError(null);
      
      // 开始验证第一个文件，传递当前的解析结果作为初始结果
      validateAndImportNext(0, parseResults);
    } catch (err: any) {
      // 统一错误处理，将错误展示到界面上而不是打印到控制台
      setError(err?.message || "上传失败，请检查文件格式后重试");
      setImportPhase("idle");
    }
  };
  
  // 处理取消
  const handleCancel = useCallback(() => {
    clearFiles();
    if (onComplete) {
      onComplete();
    }
  }, [clearFiles, onComplete]);
  
  // 渲染组件
  return (
    // 主卡片容器
    <Card>
      {/* 卡片头部 */}
      <CardHeader>
        <div className="flex items-center gap-2">
          <FilesIcon className="text-primary h-5 w-5" />
          <CardTitle>导入股东名册</CardTitle>
        </div>
        <CardDescription>
          支持DBF和Excel(XLS/XLSX/ZIP)格式，可同时上传多个文件
        </CardDescription>
      </CardHeader>
      
      {/* 卡片内容区域 */}
      <CardContent>
        {/* 错误提示 - 只在没有验证错误对话框时显示 */}
        {error && !validationError && (
          <Alert className="border-destructive bg-destructive/5 text-destructive mb-4">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>
              {error === "所选文件已在列表中" ? "文件已存在" : "上传失败"}
            </AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {/* 成功提示 */}
        {success && (
          <Alert className="border-green-500 bg-green-500/5 text-green-500 mb-4">
            <CheckCircleIcon className="h-4 w-4" />
            <AlertTitle>导入成功</AlertTitle>
            <AlertDescription>
              成功处理{" "}
              {parseResults.filter((r) => r?.success === true).length}
              /{files.length} 个文件
            </AlertDescription>
          </Alert>
        )}
        
        {/* 验证错误对话框 */}
        {validationError && (
          <ShareholderRegistryImportErrorDialog
            message={validationError.message}
            fileName={validationError.fileName}
            isLastFile={validationError.fileIndex === files.length - 1}
            onOpenHelpDialog={() => setShowHelpDialog(true)}
            onSkip={handleSkipFile}
            onCancel={handleCancel}
          />
        )}
        
        {/* 文件上传区域 */}
        {!success && !validationError && (
          <FileUploadArea
            onFileSelect={handleFileSelect}
            disabled={importPhase !== "idle"}
          />
        )}
        
        {/* 已选文件列表 */}
        {files.length > 0 && !success && !validationError && (
          <FileList
            files={files}
            currentFileIndex={currentFileIndex}
            parseResults={parseResults}
            importPhase={importPhase}
            uploadProgress={uploadProgress}
            onRemoveFile={removeFile}
            onClearFiles={clearFiles}
            onStartImport={handleUpload}
          />
        )}
        
        {/* 总体导入进度 */}
        {importPhase !== "idle" && (
          <ImportProgress
            currentFileIndex={currentFileIndex}
            totalFiles={files.length}
            progress={uploadProgress}
            status={importPhase}
          />
        )}
      </CardContent>
      
      {/* 帮助对话框 */}
      <ShareholderRegistryHelpDialog
        isOpen={showHelpDialog}
        onOpenChange={setShowHelpDialog}
      />
    </Card>
  );
} 