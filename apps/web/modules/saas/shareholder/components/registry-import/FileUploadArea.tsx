"use client";

import React, { useState } from 'react';
import { Label } from "@ui/components/label";
import { Input } from "@ui/components/input";
import { UploadCloudIcon, FileUpIcon } from "lucide-react";

interface FileUploadAreaProps {
  onFileSelect: (files: File[]) => void;
  disabled?: boolean;
}

/**
 * 文件上传区域组件
 * 支持拖拽上传和点击选择文件
 * 开放所有文件类型上传，在点击导入按钮时才进行校验
 * 
 * @param {Object} props - 组件属性
 * @param {Function} props.onFileSelect - 选择文件后的回调函数
 * @param {boolean} [props.disabled=false] - 是否禁用上传
 * 
 * @update 2025-06-12 11:28:57 修改为开放所有文件类型上传，移除文件类型限制，在点击导入按钮时才进行校验
 * @update 2025-06-04 19:50:28 添加对ZIP文件的支持，用户可以上传ZIP压缩包
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中拆分出来
 */
export function FileUploadArea({ onFileSelect, disabled = false }: FileUploadAreaProps) {
  const [isDragging, setIsDragging] = useState(false);

  // 处理拖拽开始
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  // 处理拖拽离开
  const handleDragLeave = () => {
    setIsDragging(false);
  };

  // 处理拖拽放置
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled) {
      return;
    }

    // 获取拖拽的文件
    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      onFileSelect(droppedFiles);
    }
  };

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || !e.target.files?.length) {
      return;
    }
    
    const selectedFiles = Array.from(e.target.files);
    if (selectedFiles.length > 0) {
      onFileSelect(selectedFiles);
    }
  };

  return (
			<div
				className={`rounded-lg border-2 border-dashed text-center transition-colors mb-4 p-6
      border-muted-foreground/20 ${isDragging ? "border-primary bg-primary/5" : "hover:border-primary/50"} 
      ${disabled ? "opacity-60 cursor-not-allowed" : ""}`}
				onDragOver={handleDragOver}
				onDragLeave={handleDragLeave}
				onDrop={handleDrop}
			>
				<UploadCloudIcon className="mx-auto text-muted-foreground mb-4 h-12 w-12" />
				<h3 className="mb-1 font-medium text-lg cursor-default">
					拖拽文件到此处
				</h3>
				<p className="text-muted-foreground mb-4 text-sm cursor-default">
					请导入从中国结算CSDC下载的原始股东名册文件或
				</p>
				<div className="text-center">
					<Label
						htmlFor="file-upload"
						className={`inline-flex cursor-pointer items-center gap-1 rounded-md bg-primary font-medium text-primary-foreground px-4 py-2 text-sm
          ${disabled ? "opacity-60 cursor-not-allowed" : ""}`}
					>
						<FileUpIcon className="h-4 w-4" />
						<span className="inline-flex items-center">添加文件</span>
					</Label>
					<Input
						id="file-upload"
						type="file"
						accept="*/*"
						onChange={handleFileChange}
						className="hidden"
						multiple
						disabled={disabled}
					/>
				</div>
			</div>
		);
} 