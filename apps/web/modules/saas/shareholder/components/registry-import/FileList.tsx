"use client";

import React from 'react';
import { Button } from "@ui/components/button";
import { Loader2Icon } from "lucide-react";
import { FileListItem } from './FileListItem';
import type { ShareholderRegistryParseResult } from "@saas/shareholder/lib/dbf-parser";

interface FileListProps {
  files: File[];
  currentFileIndex: number;
  parseResults: ShareholderRegistryParseResult[];
  importPhase: "idle" | "validating" | "importing";
  uploadProgress: number;
  onRemoveFile: (index: number) => void;
  onClearFiles: () => void;
  onStartImport: () => void;
}

/**
 * 文件列表组件
 * 显示所有已选择的文件及其处理状态
 * 
 * @param {Object} props - 组件属性
 * @param {File[]} props.files - 文件列表
 * @param {number} props.currentFileIndex - 当前处理的文件索引
 * @param {ShareholderRegistryParseResult[]} props.parseResults - 文件解析结果列表
 * @param {string} props.importPhase - 导入阶段
 * @param {number} props.uploadProgress - 上传进度
 * @param {Function} props.onRemoveFile - 移除单个文件的回调函数
 * @param {Function} props.onClearFiles - 清除所有文件的回调函数
 * @param {Function} props.onStartImport - 开始导入的回调函数
 * 
 * @update 2025-06-04 16:15:02 从ShareholderRegistryImport组件中拆分出来
 */
export function FileList({
  files,
  currentFileIndex,
  parseResults,
  importPhase,
  uploadProgress,
  onRemoveFile,
  onClearFiles,
  onStartImport
}: FileListProps) {
  if (files.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        暂无文件，请通过拖拽或点击上方按钮选择文件
      </div>
    );
  }

  return (
			<div className="space-y-2">
				{/* 文件列表头部 */}
				<div className="flex items-center justify-between mb-2">
					<div className="font-medium text-sm">
						已选择 {files.length} 个文件
					</div>
					<div className="flex items-center gap-2">
						{/* 清除按钮 */}
						<Button
							variant="outline"
							size="sm"
							onClick={onClearFiles}
							disabled={importPhase !== "idle"}
						>
							清除全部
						</Button>
						{/* 导入按钮 */}
						<Button
							onClick={onStartImport}
							size="sm"
							disabled={
								files.length === 0 || importPhase !== "idle"
							}
						>
							{importPhase !== "idle" ? (
								<>
									<Loader2Icon className="animate-spin mr-2 h-4 w-4" />
									处理中...
								</>
							) : (
								<>开始导入</>
							)}
						</Button>
					</div>
				</div>

				{/* 文件列表容器 */}
				<div className="rounded-md border-border bg-muted/30">
					<div className="max-h-[240px] overflow-y-auto  space-y-2">
						{files.map((file, index) => (
							<FileListItem
								key={`${file.name}-${index}`}
								file={file}
								index={index}
								currentFileIndex={currentFileIndex}
								parseResult={parseResults[index]}
								importPhase={importPhase}
								uploadProgress={uploadProgress}
								onRemove={onRemoveFile}
							/>
						))}
					</div>
				</div>
			</div>
		);
} 