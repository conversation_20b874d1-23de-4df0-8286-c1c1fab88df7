import * as React from "react";
import { cn } from "@ui/lib";
import { Table, ConfigProvider } from "antd";
import type { TableProps, TableColumnType } from "antd";
import type { TableRowSelection as AntTableRowSelection } from "antd/es/table/interface";
import type { SpinProps } from "antd";
import { useTheme } from "next-themes";
import zhCN from "antd/lib/locale/zh_CN"; // 导入antd的中文语言包

// 禁用 antd 的 React 版本兼容性警告
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
	// 忽略 antd 的 React 版本兼容性警告
	if (
		typeof args[0] === "string" &&
		args[0].includes("antd: compatible") &&
		args[0].includes("antd v5 support React is 16 ~ 18")
	) {
		return;
	}
	originalConsoleError(...args);
};

// 全局禁用 antd 警告
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(ConfigProvider as any).config({
	theme: { hashed: true },
});

/**
 * 股东表格项目接口
 *
 * @interface ShareholderItem
 * @property {string} id - 唯一标识符
 * @property {string} name - 股东名称
 * @property {string} [identifier] - 股东身份标识（可选）
 * @property {number | string} shares - 持股数量
 * @property {number | string} [percentage] - 持股百分比（可选）
 * @property {string} [type] - 股东类型（可选）
 * @property {string} [relationship] - 与公司关系（可选）
 * @property {Date | string} [joinDate] - 入股日期（可选）
 * @property {Record<string, any>} [extra] - 额外自定义字段（可选）
 */
export interface ShareholderItem {
	id: string;
	name: string;
	identifier?: string;
	shares: number | string;
	percentage?: number | string;
	type?: string;
	relationship?: string;
	joinDate?: Date | string;
	extra?: Record<string, any>;
	[key: string]: any; // 允许额外的动态字段
}

/**
 * 筛选条件选项接口
 */
export interface FilterOption {
	value: string;
	label: string;
}

/**
 * 股东表格列配置接口
 *
 * @interface ShareholderColumn
 * @property {string} key - 列对应的数据键名
 * @property {string} title - 列标题
 * @property {string} [className] - 自定义CSS类名（可选）
 * @property {(value: any, record: ShareholderItem) => React.ReactNode} [render] - 自定义渲染函数（可选）
 * @property {boolean} [sortable] - 是否可排序（可选）
 * @property {number} [width] - 列宽度（可选）
 * @property {boolean} [hidden] - 是否隐藏（可选）
 * @property {FilterOption[]} [filters] - 筛选选项（可选）
 * @property {boolean} [filtered] - 是否已筛选（可选）
 */
export interface ShareholderColumn {
	key: string;
	title: string;
	className?: string;
	render?: (value: any, record: ShareholderItem) => React.ReactNode;
	sortable?: boolean;
	width?: number;
	hidden?: boolean;
	filters?: FilterOption[];
	filtered?: boolean;
}

/**
 * 默认股东表格列配置
 */
export const DEFAULT_SHAREHOLDER_COLUMNS: ShareholderColumn[] = [
	{
		key: "name",
		title: "股东名称",
		width: 180,
	},
	{
		key: "identifier",
		title: "证件号码",
		width: 180,
	},
	{
		key: "shares",
		title: "持股数量",
		width: 120,
	},
	{
		key: "percentage",
		title: "持股比例",
		width: 120,
		render: (value) =>
			typeof value === "number" ? `${(value * 100).toFixed(2)}%` : value,
	},
	{
		key: "type",
		title: "股东类型",
		width: 120,
	},
	{
		key: "joinDate",
		title: "入股日期",
		width: 120,
		render: (value) => {
			if (!value) {
				return "-";
			}
			if (value instanceof Date) {
				return value.toLocaleDateString();
			}
			return value;
		},
	},
];

/**
 * 导出行选择配置类型
 * 用于表格组件的行选择功能
 */
export type ShareholderRowSelection = {
	selectedRowKeys: string[];
	onChange: (selectedRowKeys: string[]) => void;
	getCheckboxProps?: (record: ShareholderItem) => { disabled?: boolean };
};

/**
 * 股东表格组件接口
 *
 * @interface AntShareholderTableProps
 * @property {ShareholderItem[]} data - 股东数据数组
 * @property {ShareholderColumn[]} [columns] - 自定义列配置（可选，默认使用DEFAULT_SHAREHOLDER_COLUMNS）
 * @property {string} [className] - 自定义表格CSS类名（可选）
 * @property {string} [headerClassName] - 自定义表头CSS类名（可选）
 * @property {string} [rowClassName] - 自定义行CSS类名（可选）
 * @property {string} [cellClassName] - 自定义单元格CSS类名（可选）
 * @property {(item: ShareholderItem) => void} [onRowClick] - 行点击事件处理函数（可选）
 * @property {React.ReactNode} [emptyContent] - 空数据显示的内容（可选）
 * @property {boolean | SpinProps} [loading] - 加载状态（可选）
 * @property {(sortKey: string, sortOrder: 'asc' | 'desc') => void} [onSort] - 排序事件处理函数（可选）
 * @property {React.ReactNode} [footer] - 表格底部内容（可选）
 * @property {ShareholderRowSelection} [rowSelection] - 行选择配置（可选）
 * @property {(event: React.UIEvent<HTMLDivElement>) => void} [onScroll] - 表格滚动事件处理函数（可选）- 添加于2025年06月13日10:07:52
 * @property {object} [scroll] - 表格滚动配置（可选）- 添加于2025年06月13日10:07:52
 */
export interface AntShareholderTableProps {
  data: ShareholderItem[];
  columns?: ShareholderColumn[];
  className?: string;
  headerClassName?: string;
  rowClassName?: string;
  cellClassName?: string;
  onRowClick?: (item: ShareholderItem) => void;
  emptyContent?: React.ReactNode;
  loading?: boolean | SpinProps;
  onSort?: (sortKey: string, sortOrder: 'asc' | 'desc') => void;
  footer?: React.ReactNode;
  rowSelection?: ShareholderRowSelection;
  onScroll?: (event: React.UIEvent<HTMLDivElement>) => void; // 添加于2025年06月13日10:07:52
  scroll?: object; // 添加于2025年06月13日10:07:52
}

/**
 * 生成表格的自定义样式
 * @param {boolean} isDarkTheme - 是否为暗色主题
 * @returns {React.CSSProperties} 样式对象
 *
 * @modified 2025年06月12日 - 添加了斑马纹行的背景色变量
 */
function getTableStyle(isDarkTheme: boolean): React.CSSProperties {
  return {
    "--ant-table-bg": isDarkTheme ? "var(--card)" : "white",
    "--ant-table-header-bg": isDarkTheme ? "var(--card)" : "white",
    "--ant-table-header-color": isDarkTheme ? "rgba(255, 255, 255, 0.85)" : "var(--foreground)",
    "--ant-table-text-color": isDarkTheme ? "rgba(255, 255, 255, 0.85)" : "var(--foreground)",
    "--ant-table-row-hover-bg": isDarkTheme ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
    "--ant-light-row-hover-bg": "rgba(0, 0, 0, 0.04)", // 明确指定亮色主题的悬停背景
    "--ant-table-text-hover-color": isDarkTheme ? "#fff" : "var(--foreground)",
    "--ant-table-border-color": isDarkTheme ? "rgba(255, 255, 255, 0.12)" : "var(--border)",
    "--ant-scrollbar-thumb-color": isDarkTheme ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.2)",
    "--ant-scrollbar-track-color": isDarkTheme ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)",
    // 添加选中行样式变量
    "--ant-table-row-selected-bg": "rgba(24, 144, 255, 0.15)",
    "--ant-table-row-selected-text": "white",
    // 添加于2025年06月12日: 斑马纹行的背景色变量
    "--ant-table-row-striped-bg": isDarkTheme ? "rgba(255, 255, 255, 0.03)" : "rgba(0, 0, 0, 0.02)",
  } as React.CSSProperties;
}

/**
 * 股东表格组件
 *
 * 用于展示股东信息的可复用表格组件，支持自定义列、样式和交互
 *
 * @param {AntShareholderTableProps} props - 组件属性
 * @returns {JSX.Element} 股东表格组件
 */
export function AntShareholderTable({
	data,
	columns = DEFAULT_SHAREHOLDER_COLUMNS,
	className,
	rowClassName,
	cellClassName,
	onRowClick,
	emptyContent = "暂无数据",
	loading = false,
	onSort,
	footer,
	rowSelection,
	onScroll, // 添加于2025年06月13日10:07:52
	scroll: scrollProp, // 添加于2025年06月13日10:07:52，重命名以避免与内部scroll变量冲突
}: AntShareholderTableProps): JSX.Element {
	// 获取当前主题
	const { resolvedTheme } = useTheme();
	const isDarkTheme = resolvedTheme === "dark";

	// 过滤出可见的列
	const visibleColumns = React.useMemo(() => {
		return columns.filter((col) => !col.hidden);
	}, [columns]);

	// 转换为Ant Design的Table列配置
	const antColumns: TableColumnType<ShareholderItem>[] = React.useMemo(() => {
		return visibleColumns.map((column) => {
			const antColumn: TableColumnType<ShareholderItem> = {
				title: column.title,
				dataIndex: column.key,
				key: column.key,
				width: column.width,
				className: cn(column.className, cellClassName),
			};

			// 添加自定义渲染
			if (column.render) {
				antColumn.render = (value, record) => {
					if (column.render) {
						return column.render(value, record);
					}
					return value;
				};
			}

			// 添加排序功能
			if (column.sortable) {
				antColumn.sorter = true;
				// 禁用排序提示tooltip
				antColumn.showSorterTooltip = false;
			}

			// 添加筛选功能
			if (column.filters && column.filters.length > 0) {
				antColumn.filters = column.filters.map((filter) => ({
					text: filter.label,
					value: filter.value,
				}));
			}

			return antColumn;
		});
	}, [visibleColumns, cellClassName]);

	// 处理排序变化
	const handleChange: TableProps<ShareholderItem>["onChange"] = (
		_nation,
		_filters,
		sorter,
	) => {
		// 处理排序
		if (sorter && !Array.isArray(sorter) && onSort) {
			// 获取排序字段和方向
			const field = sorter.field as string;
			const order = sorter.order;

			// 如果字段或排序顺序未定义，不执行任何操作
			if (!field || !order) {
				return;
			}

			// 特殊处理"rank"列的排序方向，与其他列相反
			// rank列需要反向映射：向上箭头(ascend)对应降序(desc)，向下箭头(descend)对应升序(asc)
			// 修改于2025年06月04日: 调整rank列的排序方向，使排名为1的记录显示向上箭头
			// 修改于2025年06月16日: 根据hayden要求，调整排名列排序箭头显示逻辑，1往上排箭头向下，大数往1排箭头向上
			if (field === "rank") {
				// 反向映射：antd的ascend映射到API的desc，antd的descend映射到API的asc
				// 这样当排名从1往上排（升序）时，箭头显示向下；当排名从大数往1排（降序）时，箭头显示向上
				const sortOrder = order === "ascend" ? "desc" : "asc";
				onSort(field, sortOrder);
				return;
			}

			// 其他列的常规映射：
			// 当箭头向下时(descend)，表示从大到小排序，对应API中的desc
			// 当箭头向上时(ascend)，表示从小到大排序，对应API中的asc
			const sortOrder = order === "ascend" ? "asc" : "desc";
			onSort(field, sortOrder);
		}
	};

	// 生成Ant Design表格的类名
	const tableClassName = cn(
		"ant-shareholder-table",
		{
			"ant-shareholder-table-dark": isDarkTheme,
		},
		className,
	);

	// 行点击处理
	const onRow = onRowClick
		? (record: ShareholderItem) => ({
				onClick: () => {
					onRowClick(record);
				},
			})
		: undefined;

	// 使用提取的函数获取样式
	const tableStyle = React.useMemo(
		() => getTableStyle(isDarkTheme),
		[isDarkTheme],
	);

	// 滚动配置：优先使用传入的scrollProp，否则使用默认的自适应配置 - 修改于2025年06月13日10:07:52
	const scroll = React.useMemo(() => {
		// 如果传入了scrollProp，直接使用
		if (scrollProp) {
			return scrollProp;
		}

		// 否则使用默认的自适应配置：只有当数据超过10行时才启用垂直滚动
		const shouldEnableYScroll = data.length > 10;

		return {
			x: "max-content", // 启用水平滚动
			y: shouldEnableYScroll ? 500 : undefined, // 只有数据超过10行时才启用垂直滚动
			scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
		};
	}, [data.length, scrollProp]);

	// 自定义表格文案
	const customLocale = React.useMemo(() => {
		return {
			...zhCN.Table,
			// 修改于2025年06月04日: 调整排序提示文本，使其与排序箭头方向的修改保持一致
			triggerDesc: "点击降序排列（从大到小）",
			triggerAsc: "点击升序排列（从小到大）",
			cancelSort: "取消排序",
			emptyText: emptyContent,
		};
	}, [emptyContent]);

	// 转换rowSelection为Ant Design的格式
	const antRowSelection = React.useMemo<
		AntTableRowSelection<ShareholderItem> | undefined
	>(() => {
		if (!rowSelection) {
			return undefined;
		}

		return {
			selectedRowKeys: rowSelection.selectedRowKeys,
			onChange: (selectedRowKeys: React.Key[]) => {
				rowSelection.onChange(selectedRowKeys as string[]);
			},
			getCheckboxProps: rowSelection.getCheckboxProps,
			columnWidth: 32, // 进一步减小复选框列宽度
			fixed: true,
		};
	}, [rowSelection]);

	// 表格的附加属性
	const tableProps = React.useMemo(
		() => ({
			size: "middle" as const, // 设置表格为中等大小，行高适中
			bordered: false, // 不显示边框以符合当前设计
		}),
		[],
	);

	return (
		<div className={tableClassName} style={tableStyle}>
			<style jsx global>{`
        /* Ant Design表格自定义样式 */
        .ant-shareholder-table {
          --scrollbar-size: 8px;
        }
        
        /* 基础样式 */
        .ant-shareholder-table .ant-table {
          background-color: var(--ant-table-bg);
          color: var(--ant-table-text-color);
          border: none !important; /* 移除表格外边框 */
        }
        
        .ant-shareholder-table .ant-table-container {
          overflow: hidden;
          border: none !important; /* 移除表格容器边框 */
        }
        
        /* 调整表格单元格内边距和行高 */
        .ant-shareholder-table .ant-table-tbody > tr > td {
          padding: 10px 8px;
          height: 48px; /* 固定行高 */
          line-height: 1.4;
          vertical-align: middle;
          border: none !important; /* 移除单元格边框 */
        }
        
        /* 调整表头内边距和高度 */
        .ant-shareholder-table .ant-table-thead > tr > th {
          padding: 10px 8px;
          height: 46px; /* 固定表头高度 */
          background-color: var(--ant-table-header-bg) !important;
          color: var(--ant-table-header-color);
          font-weight: 500 !important; /* 确保表头字体加粗一致 */
          font-family: inherit !important; /* 确保使用继承的字体系列 */
          text-align: center; /* 确保表头文字居中对齐 */
          line-height: 1.4;
          vertical-align: middle;
          border: none !important; /* 移除表头边框 */
        }
        
        /* 添加于2025年06月12日 13:42:08 - 移除所有边框 */
        .ant-shareholder-table .ant-table,
        .ant-shareholder-table .ant-table-container,
        .ant-shareholder-table .ant-table-content,
        .ant-shareholder-table .ant-table-thead,
        .ant-shareholder-table .ant-table-tbody,
        .ant-shareholder-table .ant-table-thead > tr,
        .ant-shareholder-table .ant-table-tbody > tr,
        .ant-shareholder-table .ant-table-thead > tr > th,
        .ant-shareholder-table .ant-table-tbody > tr > td,
        .ant-shareholder-table .ant-table-summary,
        .ant-shareholder-table .ant-table-footer,
        .ant-shareholder-table .ant-table-title,
        .ant-shareholder-table .ant-table-header,
        .ant-shareholder-table .ant-table-placeholder {
          border: none !important;
          border-bottom: none !important;
          border-right: none !important;
          border-left: none !important;
          border-top: none !important;
          outline: none !important;
          box-shadow: none !important;
        }

        /* 修复表头字体不一致问题 */
        .ant-shareholder-table .ant-table-thead > tr > th .ant-table-column-title {
          font-weight: 500;
          font-family: inherit;
          color: inherit; /* 使用继承的颜色 */
        }
        
        /* 黑色主题下表头颜色特殊处理 */
        .ant-shareholder-table-dark .ant-table-thead > tr > th .ant-table-column-title {
          color: rgba(255, 255, 255, 0.9);
        }
        
        .ant-shareholder-table .ant-table-tbody > tr > td {
          color: var(--ant-table-text-color); /* 确保单元格文字颜色一致 */
        }
        
        /* 添加于2025年06月12日 14:32:23 - 确保表格底部(footer)文本颜色适配主题 */
        .ant-shareholder-table .ant-table-footer {
          background-color: var(--ant-table-bg) !important;
          color: var(--ant-table-text-color) !important; /* 确保footer文字颜色与表格一致 */
          padding: 0 !important; /* 移除内边距，让内部组件控制间距 */
        }
        
        /* 添加于2025年06月12日 14:32:23 - 深色主题下表格底部(footer)特殊处理 */
        .ant-shareholder-table-dark .ant-table-footer {
          color: rgba(255, 255, 255, 0.85) !important; /* 深色主题下使用浅色文字 */
          background-color: var(--card) !important; /* 使用与表格相同的背景色 */
        }
        
        /* 确保footer中的所有文本元素都使用正确的颜色 */
        .ant-shareholder-table .ant-table-footer * {
          color: inherit !important;
        }
        
        /* 深色主题下文字颜色增强 */
        .ant-shareholder-table-dark .ant-shareholder-table .ant-table-tbody > tr > td {
          color: rgba(255, 255, 255, 0.85);
        }
        
        /* 深色主题下链接文字颜色 */
        .ant-shareholder-table-dark .ant-table-tbody > tr > td a {
          color: #4b9eff;
        }
        
        /* 深色主题下表格边框 */
        .ant-shareholder-table-dark .ant-table {
          border-color: rgba(255, 255, 255, 0.12);
        }
        
        /* 悬停效果的全局调整 */
        .ant-table-tbody > tr.ant-table-row,
        .ant-table-tbody > tr.ant-table-row > td {
          transition: background-color 0.2s ease-in-out !important;
        }
        
        /* 修复悬停效果在黑暗模式下变白的问题 */
        .ant-shareholder-table .ant-table-tbody > tr.ant-table-row:hover > td {
          background-color: transparent !important; 
        }
        
        /* 修改于2025年06月12日: 增强斑马纹行的视觉效果 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr.bg-slate-100\/60 > td {
          background-color: var(--ant-table-row-striped-bg) !important;
        }
        
        .ant-shareholder-table-dark .ant-table-tbody > tr.bg-gray-700\/20 > td {
          background-color: var(--ant-table-row-striped-bg) !important;
        }
        
        /* 浅色主题悬停样式 - 更具体的选择器 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-row-hover > td,
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr > td.ant-table-cell-row-hover {
          background-color: var(--ant-light-row-hover-bg) !important;
        }
        
        /* 修改于2025年06月12日: 确保斑马纹行在悬停时有足够的视觉差异 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr.bg-slate-100\/60:hover > td {
          background-color: rgba(0, 0, 0, 0.08) !important;
        }
        
        .ant-shareholder-table-dark .ant-table-tbody > tr.bg-gray-700\/20:hover > td {
          background-color: rgba(255, 255, 255, 0.12) !important;
        }
        
        /* 直接覆盖Ant Design的原生悬停样式 */
        .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-table-wrapper .ant-table-tbody > tr > td.ant-table-cell-row-hover {
          background-color: var(--ant-table-row-hover-bg) !important;
          transition: background-color 0.2s ease-in-out !important;
        }
        
        /* 添加选中行的样式 */
        .ant-shareholder-table .ant-table-tbody > tr.ant-table-row-selected > td {
          background-color: var(--ant-table-row-selected-bg) !important;
          color: var(--ant-table-row-selected-text) !important;
          transition: all 0.2s ease-in-out !important;
        }
        
        /* 亮色主题下选中行样式微调 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr.ant-table-row-selected > td {
          background-color: rgba(24, 144, 255, 0.15) !important; /* 浅蓝色背景 */
          color: rgba(0, 0, 0, 0.85) !important; /* 保持文本对比度 */
        }
        
        /* 暗色主题下选中行样式微调 */
        .ant-shareholder-table-dark .ant-table-tbody > tr.ant-table-row-selected > td {
          background-color: rgba(24, 144, 255, 0.25) !important; /* 深色主题下稍深的蓝色 */
          color: white !important; /* 白色文本 */
        }
        
        /* 确保选中行悬停时保持选中样式 */
        .ant-shareholder-table .ant-table-tbody > tr.ant-table-row-selected:hover > td {
          background-color: var(--ant-table-row-selected-bg) !important;
          color: var(--ant-table-row-selected-text) !important;
        }
        
        /* 亮色主题选中+悬停状态 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr.ant-table-row-selected:hover > td {
          background-color: rgba(24, 144, 255, 0.2) !important; /* 稍深的浅蓝色 */
        }
        
        /* 暗色主题选中+悬停状态 */
        .ant-shareholder-table-dark .ant-table-tbody > tr.ant-table-row-selected:hover > td {
          background-color: rgba(24, 144, 255, 0.3) !important; /* 更深的蓝色 */
        }
        
        /* 空状态样式 */
        .ant-shareholder-table .ant-empty-description {
          color: var(--ant-table-text-color);
        }
        
        /* 排序图标样式 */
        .ant-shareholder-table-dark .ant-table-column-sorter-up.active,
        .ant-shareholder-table-dark .ant-table-column-sorter-down.active {
          color: #4b9eff;
        }
        
        /* 确保排序图标与排序方向一致 */
        .ant-shareholder-table .ant-table-column-sorter-up.active,
        .ant-shareholder-table .ant-table-column-sorter-down.active {
          color: var(--primary, #1890ff); /* 使用主色或默认蓝色 */
        }
        
        .ant-shareholder-table .ant-table-filter-trigger {
          color: var(--ant-table-header-color);
        }
        
        .ant-shareholder-table .ant-table-column-sorter {
          color: var(--ant-table-header-color);
        }
        
        /* 修复排序列背景变黑的问题 */
        .ant-shareholder-table .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: transparent !important;
        }
        
        /* 亮色主题下排序列背景样式 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: rgba(0, 0, 0, 0.02) !important;
        }
        
        /* 暗色主题下排序列背景样式 */
        .ant-shareholder-table-dark .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: rgba(255, 255, 255, 0.04) !important;
        }
        
        /* 修改于2025年06月12日: 确保斑马纹行在排序列中也能正常显示 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr:nth-child(odd) > td.ant-table-column-sort {
          background-color: transparent !important;
        }
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr:nth-child(even) > td.ant-table-column-sort {
          background-color: rgba(0, 0, 0, 0.02) !important;
        }
        .ant-shareholder-table-dark .ant-table-tbody > tr:nth-child(odd) > td.ant-table-column-sort {
          background-color: transparent !important;
        }
        .ant-shareholder-table-dark .ant-table-tbody > tr:nth-child(even) > td.ant-table-column-sort {
          background-color: rgba(255, 255, 255, 0.04) !important;
        }
        
        /* 确保排序列中的行悬停效果正常 */
        .ant-shareholder-table:not(.ant-shareholder-table-dark) .ant-table-tbody > tr:hover > td.ant-table-column-sort {
          background-color: var(--ant-light-row-hover-bg) !important;
        }
        
        .ant-shareholder-table-dark .ant-table-tbody > tr:hover > td.ant-table-column-sort {
          background-color: var(--ant-table-row-hover-bg) !important;
        }
        
        /* 复选框样式 */
        .ant-shareholder-table .ant-checkbox-wrapper {
          color: var(--ant-table-header-color);
        }
        
        .ant-shareholder-table .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #1890ff;
          border-color: #1890ff;
        }
        
        .ant-shareholder-table .ant-checkbox-indeterminate .ant-checkbox-inner::after {
          background-color: #1890ff;
        }
        
        /* 复选框列样式 - 增强版 */
        .ant-shareholder-table .ant-table-selection-column {
          text-align: center !important;
          padding-left: 8px !important;
          padding-right: 0 !important;
          width: 32px !important;
          min-width: 32px !important;
          max-width: 32px !important;
          box-sizing: border-box !important;
        }
        
        /* 确保复选框居中且尺寸合适 */
        .ant-shareholder-table .ant-table-selection-column .ant-checkbox-wrapper {
          margin-right: 0 !important;
          padding: 0 !important;
        }
        
        /* 处理复选框表头 */
        .ant-shareholder-table .ant-table-thead > tr > th.ant-table-selection-column {
          width: 32px !important;
          min-width: 32px !important;
          max-width: 32px !important;
        }
        
        /* 确保表头复选框位置一致 */
        .ant-shareholder-table .ant-table-selection-column .ant-checkbox {
          margin-left: 0 !important;
          margin-right: 0 !important;
        }
        
        /* 黑色主题下的复选框边框颜色 */
        .ant-shareholder-table-dark .ant-checkbox-wrapper .ant-checkbox-inner {
          border-color: rgba(255, 255, 255, 0.5);
        }
        
        /* 自定义滚动条样式 */
        .ant-shareholder-table .ant-table-body::-webkit-scrollbar,
        .ant-shareholder-table .ant-table-header::-webkit-scrollbar {
          width: var(--scrollbar-size);
          height: var(--scrollbar-size);
        }
        
        .ant-shareholder-table .ant-table-body::-webkit-scrollbar-thumb,
        .ant-shareholder-table .ant-table-header::-webkit-scrollbar-thumb {
          background-color: var(--ant-scrollbar-thumb-color);
          border-radius: calc(var(--scrollbar-size) / 2);
        }
        
        .ant-shareholder-table .ant-table-body::-webkit-scrollbar-track,
        .ant-shareholder-table .ant-table-header::-webkit-scrollbar-track {
          background-color: var(--ant-scrollbar-track-color);
        }
        
        /* 适配Firefox滚动条 */
        .ant-shareholder-table .ant-table-body,
        .ant-shareholder-table .ant-table-header {
          scrollbar-width: thin;
          scrollbar-color: var(--ant-scrollbar-thumb-color) var(--ant-scrollbar-track-color);
        }
        
        /* 修复筛选菜单在暗色模式下的颜色问题 */
        .ant-shareholder-table-dark .ant-dropdown-menu {
          background-color: var(--popover);
          color: var(--popover-foreground);
        }
        
        .ant-shareholder-table-dark .ant-dropdown-menu-item {
          color: var(--popover-foreground);
        }
        
        .ant-shareholder-table-dark .ant-dropdown-menu-item:hover {
          background-color: var(--accent);
        }
        
        
        /* 加载状态样式 */
        .ant-shareholder-table .ant-spin {
          color: var(--primary);
        }
        
        /* 自定义Tooltip样式 */
        .ant-tooltip .ant-tooltip-inner {
          background-color: var(--popover);
          color: var(--popover-foreground);
        }
        
        .ant-tooltip .ant-tooltip-arrow-content {
          background-color: var(--popover);
        }
        
        /* 移除最后一列的垂直分隔线 */
        .ant-shareholder-table .ant-table-thead > tr > th:last-child,
        .ant-shareholder-table .ant-table-tbody > tr > td:last-child {
          border-right: none !important;
        }
        
        /* 隐藏表格垂直分隔线 */
        .ant-shareholder-table .ant-table-tbody > tr > td {
          border-right: none !important;
        }
        
        /* 隐藏表格右侧滚动条区域的垂直线 */
        .ant-shareholder-table .ant-table-measure-row,
        .ant-shareholder-table .ant-table-rtl,
        .ant-shareholder-table .ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container::after,
        .ant-shareholder-table .ant-table-cell-scrollbar {
          box-shadow: none !important;
          border-right: none !important;
        }
        
        /* 隐藏右侧滚动指示器 */
        .ant-shareholder-table .ant-table-container::after {
          display: none !important;
        }
        
        /* 确保垂直分隔线不显示 */
        .ant-shareholder-table .ant-table-container table > thead > tr:first-child th:last-child {
          border-right: none !important;
        }
        
        /* 确保表格滚动区域不显示垂直线 */
        .ant-shareholder-table .ant-table-ping-right .ant-table-cell-fix-right-first::after,
        .ant-shareholder-table .ant-table-ping-right .ant-table-cell-fix-right-last::after {
          box-shadow: none !important;
        }
      `}</style>

			{/* 使用ConfigProvider来处理React兼容性警告 */}
			<ConfigProvider
				theme={{
					// 确保使用 hashed 模式减少兼容性检查
					hashed: true,
					// 设置组件token
					components: {
						Table: {
							colorBgContainer: isDarkTheme
								? "var(--card)"
								: "white", // 表格背景色
							borderRadius: 0,
							colorBorderSecondary: "transparent",
							colorBorder: "transparent",
							colorText: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: undefined,
							colorTextHeading: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: undefined,
							colorTextSecondary: isDarkTheme
								? "rgba(255, 255, 255, 0.65)"
								: undefined,
							colorTextDisabled: isDarkTheme
								? "rgba(255, 255, 255, 0.3)"
								: undefined,
							colorBgTextHover: isDarkTheme
								? "rgba(255, 255, 255, 0.08)"
								: "rgba(0, 0, 0, 0.04)",
							colorFillAlter: isDarkTheme
								? "rgba(255, 255, 255, 0.02)"
								: "rgba(0, 0, 0, 0.02)",
							motionDurationMid: "0s",
							motionDurationSlow: "0s",
						},
					},
				}}
			>
				<Table
					dataSource={data}
					columns={antColumns}
					rowKey="id"
					loading={loading}
					onChange={handleChange}
					onRow={onRow}
					pagination={false}
					scroll={scroll}
					locale={customLocale}
					rowClassName={(_, index) =>
						cn(
							// 修改于2025年06月12日: 调整行背景颜色，使单数行高亮显示
							// 原代码: index % 2 === 1 ? (isDarkTheme ? "bg-gray-700/20" : "bg-slate-100/60") : "",
							index % 2 === 0
								? isDarkTheme
									? "bg-gray-700/20"
									: "bg-slate-100/60"
								: "",
							rowClassName,
						)
					}
					footer={footer ? () => footer : undefined}
					rowSelection={antRowSelection}
					onScroll={onScroll} // 添加于2025年06月13日10:07:52，支持滚动事件处理
					{...tableProps}
				/>
			</ConfigProvider>
		</div>
	);
} 