"use client";

// 导入必要的依赖
import { cn } from "@ui/lib"; // 用于合并 className 的工具函数
import Link from "next/link"; // Next.js 的链接组件
import { usePathname } from "next/navigation"; // 用于获取当前路径
import type { ReactNode } from "react"; // React 节点类型
import { useTranslations } from "next-intl"; // 国际化翻译 hook

// 定义股东菜单支持的翻译键类型
export type shareholderMenuTranslationKey =
	| "management"
	| "roster-manage"
	| "register"
	| "analysis";

// 定义单个菜单项的接口
export interface ShareholderMenuItem {
	title?: string; // 可选的标题
	titleKey?: shareholderMenuTranslationKey; // 子菜单项的翻译键
	href: string; // 子菜单项链接
	icon?: ReactNode; // 子菜单项图标
}

// 定义菜单组的接口
export interface ShareholderMenuGroup {
	title?: string; // 可选的组标题
	titleKey?: shareholderMenuTranslationKey; // 可选的组标题翻译键
	avatar?: ReactNode; // 可选的头像
	items: ShareholderMenuItem[]; // 菜单项数组
}

/**
 * 股东菜单组件
 * @param menuItems - 菜单项配置数组
 * @returns 渲染的菜单组件
 */
export function ShareholderMenu({
	menuItems
}: {
	menuItems: ShareholderMenuGroup[];
	layout?: "vertical" | "horizontal"; // 布局参数，支持垂直或水平布局
}) {
	const pathname = usePathname(); // 获取当前路径
	const t = useTranslations("shareholder.menu"); // 获取翻译函数
	
	// 判断是否是根路径（只包含组织slug，不包含具体功能路径）
	const isRootPath = pathname.match(/\/app\/[^\/]+\/shareholder$/);

	// 判断菜单项是否处于激活状态
	const isActiveMenuItem = (href: string, index: number) => {
		// 如果是根路径且当前是第一个菜单项，则高亮
		if (isRootPath && index === 0) {
			return true;
		}
		// 否则检查路径是否包含当前链接
		return pathname.includes(href);
	};

	return (
		<div className="space-y-8">
			{menuItems.map((item, i) => (
				<div key={i}>
					{/* 渲染菜单组标题 */}
					{(item.title || item.titleKey) && (
						<div className="flex items-center justify-start gap-2">
							{item.avatar}
							<h2 className="text-xs font-semibold text-foreground/60">
								{item.titleKey ? t(item.titleKey) : item.title}
							</h2>
						</div>
					)}

					{/* 渲染菜单项列表 - 始终使用水平布局 */}
					<ul className="mt-2 flex list-none flex-row gap-6">
						{item.items.map((subitem, k) => (
							<li
								key={k}
								className="flex justify-center text-center"
							>
								<Link
									href={subitem.href}
									className={cn(
										"inline-flex flex-col items-center pb-1.5 pt-1.5 text-sm relative",
										isActiveMenuItem(subitem.href, k)
											? "font-bold after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary" // 激活状态样式，使用after伪元素作为下划线
											: "", // 非激活状态样式
									)}
									data-active={isActiveMenuItem(
										subitem.href,
										k,
									)}
								>
									{subitem.icon && (
										<span className="shrink-0">
											{subitem.icon}
										</span>
									)}
									<span>
										{subitem.titleKey
											? t(subitem.titleKey)
											: subitem.title}
									</span>
								</Link>
							</li>
						))}
					</ul>
				</div>
			))}
		</div>
	);
} 