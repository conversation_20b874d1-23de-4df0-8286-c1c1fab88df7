"use client";

import { useMemo, useRef, useState, useEffect } from "react";
import type { PaginationInfo, ShareholderItem, SortOrder } from "@saas/shareholder/lib/types";
import { TablePagination } from "@saas/shareholder/components/TablePagination";
import { formatNumber, renderTruncatedWithTooltip } from "@saas/shareholder/lib/ui-utils";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { cn } from "@ui/lib";
import type { ShareholderColumn } from "@saas/shareholder/components/ant-shareholder-table";
import { AntShareholderTable as BaseShareholderTable } from "@saas/shareholder/components/ant-shareholder-table";

/**
 * 空数据状态组件
 * 在表格中没有数据时显示友好的提示信息
 * @version 1.1.0 (2025-05-19) - 优化视觉效果，添加图标和更好的布局
 */
function EmptyState({ 
  searchTerm,
  text
}: { 
  searchTerm?: string;
  text: string;
}) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center px-4" style={{ height: "500px" }}>
      {searchTerm ? (
        // 搜索无结果状态
        <>
          <p className="font-medium text-slate-700 mb-2">
            没有找到匹配的股东
          </p>
        </>
      ) : (
        // 无数据状态
        <>
          <p className="font-medium text-slate-700 mb-2">
            {text}
          </p>
        </>
      )}
    </div>
  );
}

interface ShareholderTableProps {
  shareholders: ShareholderItem[];
  pagination?: PaginationInfo;
  isLoading: boolean;
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: SortOrder;
  onSort: (column: string, order?: 'asc' | 'desc') => void;
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  searchTerm?: string;
  onResetSort?: () => void; // 保留属性但不再在组件内部使用
  visibleColumns?: string[]; // 添加可见列属性
  onScrollLoadMore?: (nextPage: number) => Promise<ShareholderItem[]>; // 添加滚动加载更多数据的回调 - 添加于2025年06月13日10:13:43
}

/**
 * 股东表格组件
 * 显示股东列表数据，支持排序、分页和搜索
 * 优化了表格显示，添加了固定高度、内部滚动、水平滚动和Tooltip显示功能
 * @version 11.0.0 (2025-06-13) - 添加滚动加载分页功能，优化用户体验，支持表格滚动到底部自动加载更多数据
 * @version 10.1.0 (2025-05-26) - 添加汇总账户号码、汇总账户名称和股份性质字段，支持05名册数据展示
 * @version 10.0.0 (2025-05-22) - 添加全量字段显示支持，增加更多API返回字段的展示
 * @version 9.1.0 (2025-05-22) - 添加可见列筛选功能，用户可自定义显示列
 * @version 9.0.4 (2025-05-21) - 优化空数据展示逻辑，移除冗余的条件渲染
 * @version 9.0.3 (2025-05-21) - 优化加载状态显示，确保表格尺寸一致性
 * @version 9.0.2 (2025-05-21) - 更新组件，添加Ant Design风格的筛选功能
 * @version 9.0.1 (2025-05-21) - 调整表格样式，使表头和内容更加紧凑
 * @version 9.0.0 (2025-05-21) - 重构为使用通用的AntShareholderTable组件，保持UI一致性
 * @version 8.0.0 (2025-05-20) - 统一与ShareholderRegistryTable的风格，使用Shadcn UI的Table组件
 * @version 7.0.0 (2025-05-19) - 使用增强版useSystemScale hook进行样式适配，提升代码复用性
 * @version 6.0.0 (2025-05-19) - 添加系统缩放适配，优化不同缩放级别下的显示效果
 * @version 5.0.0 (2025-05-19) - 移除useResponsiveDesign依赖，使用固定样式配置
 * @version 4.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端表格实现
 */
export function ShareholderTable({
		shareholders,
		pagination,
		isLoading,
		page,
		sortBy,
		sortOrder,
		onSort,
		onPageChange,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: <explanation>
		onLimitChange,
		searchTerm,
		visibleColumns,
		// onScrollLoadMore, // 保留但不再使用，避免破坏接口兼容性
	}: ShareholderTableProps) {
		// 使用增强版的系统缩放hook获取样式配置
		const { styles } = useSystemScale();

		// 验证股东数据
		const validShareholders = Array.isArray(shareholders)
			? shareholders
			: [];

		// 滚动相关状态
		const [isScrollLoading, setIsScrollLoading] = useState(false);
		const [resetPaginationFlag, setResetPaginationFlag] = useState(false);
		const [accumulatedData, setAccumulatedData] = useState<
			ShareholderItem[]
		>([]); // 累积的数据 - 添加于2025年06月13日10:13:43
		const scrollRef = useRef({
			left: 0,
			top: 0,
		});
		const tableScrollRef = useRef<HTMLDivElement>(null); // 表格滚动容器引用 - 添加于2025年06月13日10:22:15

		// 当排序或搜索变化时，重置累积数据、分页状态和滚动位置 - 修改于2025年06月13日10:22:15
		useEffect(() => {
			setAccumulatedData(validShareholders); // 重置累积数据为当前数据
			setResetPaginationFlag(true);

			// 重置滚动位置到顶部 - 添加于2025年06月13日10:22:15
			if (tableScrollRef.current) {
				tableScrollRef.current.scrollTo({ top: 0, behavior: 'smooth' });
			}

			// 重置滚动记录
			scrollRef.current = { left: 0, top: 0 };

			// 重置标志在短时间后恢复，以便下次排序时能再次触发
			const timer = setTimeout(() => {
				setResetPaginationFlag(false);
			}, 300);

			return () => clearTimeout(timer);
		}, [sortBy, sortOrder, searchTerm]); // 依赖于具体的排序和搜索参数，而不是函数引用

		// 当新数据到达时，根据页码决定是替换还是累积 - 添加于2025年06月13日10:13:43
		useEffect(() => {
			if (page === 1) {
				// 第一页数据，直接替换
				setAccumulatedData(validShareholders);
			} else {
				// 后续页数据，进行累积（去重）
				setAccumulatedData((prevData) => {
					const existingIds = new Set(
						prevData.map((item) => item.id),
					);
					const newItems = validShareholders.filter(
						(item) => !existingIds.has(item.id),
					);
					return [...prevData, ...newItems];
				});
			}
		}, [validShareholders, page]);

		// 将累积的股东数据转换为AntShareholderTable需要的格式 - 修改于2025年06月13日10:13:43
		const shareHolderData = useMemo(() => {
			return accumulatedData.map((shareholder) => {
				// 确保所有字段都有值，防止undefined导致渲染错误
				return {
					id: shareholder.id || "",
					name: shareholder.securitiesAccountName || "",
					account: shareholder.unifiedAccountNumber || "",
					identifier: shareholder.shareholderId || "",
					shares: shareholder.numberOfShares || "0",
					percentage: shareholder.shareholdingRatio || "0",
					type: shareholder.shareholderCategory || "",
					tags: shareholder.shareholderTags || [],
					rank: shareholder.rankInPeriod || 0,
					contactNumber: shareholder.contactNumber || "",
					change: shareholder.change || { type: "unchanged" },
					// 添加更多字段映射，确保每个字段都有默认值
					lockedShares: shareholder.lockedUpShares || "0",
					frozenShares: shareholder.frozenShares || "0",
					contactAddress: shareholder.contactAddress || "",
					zipCode: shareholder.zipCode || "",
					cashAccount: shareholder.cashAccount || "",
					sharesInCashAccount: shareholder.sharesInCashAccount || "0",
					marginAccount: shareholder.marginAccount || "",
					sharesInMarginAccount:
						shareholder.sharesInMarginAccount || "0",
					relatedParty: shareholder.relatedPartyIndicator || "",
					clientCategory: shareholder.clientCategory || "",
					remarks: shareholder.remarks || "",
					// 添加新增字段
					marginCollateralAccountNumber:
						shareholder.marginCollateralAccountNumber || "",
					marginCollateralAccountName:
						shareholder.marginCollateralAccountName || "",
					natureOfShares: shareholder.natureOfShares || "",
				};
			});
		}, [accumulatedData]);

		// 定义列配置
		const columns: ShareholderColumn[] = useMemo(() => {
			const allColumns = [
				{
					key: "rank",
					title: "持股排名",
					width: 100,
					sortable: true,
					className: "text-center",
					render: (value: any) => value || "-",
				},
				{
					key: "name",
					title: "账户名称",
					width: 230,
					sortable: false,
					className: "text-center font-medium",
					render: (value: any) =>
						renderTruncatedWithTooltip(String(value), 25),
				},
				{
					key: "identifier",
					title: "证件号码",
					width: 180,
					sortable: false,
					className: "text-center",
				},
				{
					key: "account",
					title: "一码通号码",
					width: 150,
					sortable: false,
					className: "text-center font-mono",
				},
				{
					key: "percentage",
					title: "持股比例",
					width: 100,
					sortable: true,
					className: "text-center font-mono",
					render: (value: any) =>
						formatNumber(value, {
							decimals: 4,
							percentage: true,
						}),
				},
				{
					key: "shares",
					title: "持股数量",
					width: 100,
					sortable: true,
					className: "text-center font-mono",
					render: (value: any) => formatNumber(value),
				},
				{
					key: "contactNumber",
					title: "手机号码",
					width: 100,
					sortable: false,
					className: "text-center font-mono",
				},
				// 添加更多列定义
				{
					key: "type",
					title: "持有人类别",
					width: 100,
					sortable: false,
					className: "text-center",
				},
				{
					key: "lockedShares",
					title: "限售股数量",
					width: 100,
					sortable: false,
					className: "text-center font-mono",
					render: (value: any) => formatNumber(value),
				},
				{
					key: "frozenShares",
					title: "冻结股数",
					width: 100,
					sortable: false,
					className: "text-center font-mono",
					render: (value: any) => formatNumber(value),
				},
				{
					key: "contactAddress",
					title: "通讯地址",
					width: 200,
					sortable: false,
					className: "text-center",
					render: (value: any) =>
						renderTruncatedWithTooltip(String(value || "-"), 20),
				},
				{
					key: "zipCode",
					title: "邮政编码",
					width: 100,
					sortable: false,
					className: "text-center font-mono",
				},
				{
					key: "cashAccount",
					title: "普通证券账户",
					width: 150,
					sortable: false,
					className: "text-center font-mono",
				},
				{
					key: "sharesInCashAccount",
					title: "普通账户持股数量",
					width: 120,
					sortable: false,
					className: "text-center font-mono",
					render: (value: any) => formatNumber(value),
				},
				{
					key: "marginAccount",
					title: "信用证券账户",
					width: 150,
					sortable: false,
					className: "text-center font-mono",
				},
				{
					key: "sharesInMarginAccount",
					title: "信用账户持股数量",
					width: 120,
					sortable: false,
					className: "text-center font-mono",
					render: (value: any) => formatNumber(value),
				},
				{
					key: "relatedParty",
					title: "关系标识",
					width: 100,
					sortable: false,
					className: "text-center",
				},
				{
					key: "clientCategory",
					title: "客户类别",
					width: 100,
					sortable: false,
					className: "text-center",
				},
				{
					key: "remarks",
					title: "备注",
					width: 200,
					sortable: false,
					className: "text-center",
					render: (value: any) =>
						renderTruncatedWithTooltip(String(value || "-"), 20),
				},
				// 添加新增字段列定义
				{
					key: "marginCollateralAccountNumber",
					title: "汇总账户号码",
					width: 150,
					sortable: false,
					className: "text-center font-mono",
				},
				{
					key: "marginCollateralAccountName",
					title: "汇总账户名称",
					width: 200,
					sortable: false,
					className: "text-center",
					render: (value: any) =>
						renderTruncatedWithTooltip(String(value || "-"), 20),
				},
				{
					key: "natureOfShares",
					title: "股份性质",
					width: 100,
					sortable: false,
					className: "text-center",
				},
			];

			// 如果指定了可见列，则筛选列
			if (visibleColumns && visibleColumns.length > 0) {
				return allColumns.filter((column) =>
					visibleColumns.includes(column.key),
				);
			}

			return allColumns;
		}, [visibleColumns]);

		// 处理排序转换 - 修改于2025年06月13日10:22:15，添加页码重置和滚动位置重置逻辑
		const handleSort = (column: string, order: "asc" | "desc") => {
			// 将列名转换为API排序字段
			let apiSortField = column;
			switch (column) {
				// 已移除以下三个字段的排序功能，但保留注释以便未来参考
				/*
      case "name":
        apiSortField = "securitiesAccountName";
        break;
      case "account":
        apiSortField = "unifiedAccountNumber";
        break;
      case "identifier":
        apiSortField = "shareholderId";
        break;
      */
				case "shares":
					apiSortField = "numberOfShares";
					break;
				case "percentage":
					apiSortField = "shareholdingRatio";
					break;
				case "rank":
					apiSortField = "rank";
					break;
			}

			// 排序时重置页码到第一页
			onPageChange(1);

			// 立即重置滚动位置到顶部 - 添加于2025年06月13日10:22:15
			if (tableScrollRef.current) {
				tableScrollRef.current.scrollTo({ top: 0, behavior: 'smooth' });
			}

			// 传递排序字段和排序方向到父组件
			// 注意：rank列的特殊排序方向处理已经在ant-shareholder-table组件中完成
			// 修改于2025年06月04日: rank列现在使用正向映射，与其他列一致，向上箭头(ascend)对应升序(asc)
			onSort(apiSortField, order);
		};

		// 定义加载状态的配置
		const loadingConfig = useMemo(() => {
			return {
				spinning: isLoading,
				size: "large" as const,
			};
		}, [isLoading]);

		// 处理表格滚动事件 - 修改于2025年06月13日10:22:15，添加滚动容器引用记录
		const handleTableScroll = (event: React.UIEvent<HTMLDivElement>) => {
			const { scrollTop, clientHeight, scrollHeight, scrollLeft } =
				event.target as HTMLDivElement;

			// 记录滚动容器引用 - 添加于2025年06月13日10:22:15
			if (!tableScrollRef.current && event.target) {
				tableScrollRef.current = event.target as HTMLDivElement;
			}

			// 计算是否还有更多数据可以加载
			const totalPages = pagination?.totalPages || 1;
			const hasMoreData = page < totalPages;

			// 垂直滚动 & 到底了 & 有更多数据 & 不在加载中
			if (
				Math.abs(scrollTop - scrollRef.current.top) > 0 &&
				scrollTop + clientHeight >= scrollHeight - 50 && // 提前50px触发加载
				hasMoreData &&
				!isScrollLoading &&
				!isLoading
			) {
				// 设置滚动加载状态
				setIsScrollLoading(true);

				// 调用页面变更函数，加载下一页
				onPageChange(page + 1);

				// 延迟重置加载状态，避免频繁触发
				setTimeout(() => {
					setIsScrollLoading(false);
				}, 500);
			}

			// 记录当前滚动信息
			scrollRef.current = {
				left: scrollLeft,
				top: scrollTop,
			};
		};

		// 设置滚动配置 - 修改于2025年06月17日15:25:33，添加智能垂直滚动逻辑
		// 原代码保留作为注释：
		// const scrollConfig = useMemo(() => {
		//   return {
		//     x: "max-content", // 启用水平滚动
		//     y: "calc(98vh - 300px)", // 固定高度，与示例代码保持一致
		//     scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
		//   };
		// }, []);
		const scrollConfig = useMemo(() => {
			// 智能垂直滚动：只有当数据超过10行时才启用垂直滚动，避免少量数据时出现滚动条
			const shouldEnableYScroll = shareHolderData.length > 10;

			return {
				x: "max-content", // 启用水平滚动
				y: shouldEnableYScroll ? "calc(98vh - 300px)" : undefined, // 根据数据量决定是否启用垂直滚动
				scrollToFirstRowOnChange: false, // 设置为false以减少滚动指示器的显示
			};
		}, [shareHolderData.length]);

		// 移除冗余的条件渲染，直接使用 AntShareholderTable 进行渲染
		// AntShareholderTable 组件会根据数据是否为空自动显示 emptyContent
		return (
			<>
				<div
					className={cn(
						"rounded-md",
						"h-[calc(96vh-200px)]",
						"flex",
						"flex-col",
					)}
				>
					<div className="flex-1 overflow-hidden">
						<BaseShareholderTable
							data={shareHolderData}
							columns={columns}
							loading={loadingConfig}
							className="w-full h-full"
							headerClassName="sticky top-0 z-10 font-medium"
							rowClassName="border-b border-slate-200/70 last:border-0"
							cellClassName={styles.fontSize.content}
							onSort={handleSort}
							emptyContent={EmptyState({
								searchTerm,
								text: `${loadingConfig.spinning ? "" : "请上传股东名册"}`,
							})}
							// 修改于2025年06月13日10:07:52，使用Ant Design Table的onScroll属性
							onScroll={handleTableScroll}
							scroll={scrollConfig}
						/>
					</div>
					<div className="flex-shrink-0">
						<TablePagination
							pagination={
								pagination || {
									total: 0,
									page: 1,
									limit: 10,
									totalPages: 1,
								}
							}
							page={page}
						/>
					</div>
				</div>
			</>
		);
	} 