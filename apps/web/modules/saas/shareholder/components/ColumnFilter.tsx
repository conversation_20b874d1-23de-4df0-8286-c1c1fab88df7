"use client";

import { But<PERSON> } from "@ui/components/button";
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger,
  PopoverHeader,
  PopoverBody
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { Columns as ColumnsIcon } from "lucide-react";

export interface ColumnOption {
  key: string;
  title: string;
  locked?: boolean; // 是否锁定显示，不可隐藏
}

// 预设配置类型定义
export interface PresetOption {
  key: string;
  title: string;
  columns: string[];
}

interface ColumnFilterProps {
  columns: ColumnOption[];
  defaultColumns?: string[]; // 默认显示的列
  visibleColumns: string[]; // 当前显示的列
  onVisibleColumnsChange: (columns: string[]) => void;
  size?: "sm" | "md" | "lg"; // 适配不同尺寸
  className?: string; // 允许外部传入自定义类名
  // 预设配置相关属性
  presetOptions?: PresetOption[];
}

/**
 * 列筛选器组件
 * 提供预设视图快速切换功能
 * 
 * @version 2.0.0 (2025-05-22) - 使用更新的Popover组件，改进界面与交互
 * @version 1.0.0 (2025-05-22) - 初始版本，提供基础的列选择功能
 */
export function ColumnFilter({
  columns,
  defaultColumns,
  visibleColumns,
  onVisibleColumnsChange,
  size = "md",
  className,
}: ColumnFilterProps) {
  // 确定按钮大小类
  const buttonSizeClass = size === "sm" 
    ? "h-8 px-2 text-xs"
    : size === "lg"
      ? "h-10 px-4 text-base"
      : "h-9 px-3 text-sm";
  
  // 判断当前是否为全量视图（所有列都显示）
  const isFullView = visibleColumns.length === columns.length;
  
  // 判断当前是否为默认视图
  const isDefaultView = !isFullView && (
    // 如果有提供defaultColumns，检查是否与当前visibleColumns匹配
    (defaultColumns && 
      defaultColumns.length === visibleColumns.length && 
      defaultColumns.every(col => visibleColumns.includes(col))) ||
    // 如果没有提供defaultColumns，且当前不是全量视图，则默认当作是默认视图
    (!defaultColumns && visibleColumns.length < columns.length)
  );
  
  // 切换到全量视图
  const handleFullView = () => {
    const allColumns = columns.map(col => col.key);
    onVisibleColumnsChange(allColumns);
  };
  
  // 切换到默认视图
  const handleDefaultView = () => {
    const defaultCols = defaultColumns || columns
      .filter(col => !col.locked) // 假设没有提供 defaultColumns，则只显示非锁定列
      .map(col => col.key);
    onVisibleColumnsChange(defaultCols);
  };
  
  // 应用预设配置
  const applyPreset = (preset: PresetOption) => {
    // 确保锁定列始终包含在内
    const lockedColumnKeys = columns
      .filter(col => col.locked)
      .map(col => col.key);
    
    const presetColumns = Array.from(new Set([...preset.columns, ...lockedColumnKeys]));
    onVisibleColumnsChange(presetColumns);
  };
  
  // 创建流式响应式的按钮类
  const getResponsiveButtonClass = (size: string) => {
    // 根据大小调整不同的响应式行为
    if (size === "sm") {
      return "max-w-[40px] sm:max-w-none overflow-hidden";
    }
    
    if (size === "lg") {
      return "max-w-[48px] sm:max-w-none overflow-hidden";
    }
    
    return "max-w-[44px] sm:max-w-none overflow-hidden";
  };
  
  return (
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"shrink-0 rounded-md",
							"h-9 w-9", // 确保高宽一致为9（36px）
							className,
						)}
						aria-label="显示属性"
					>
						<ColumnsIcon className="size-4 m-0" />
					</Button>
				</PopoverTrigger>

				<PopoverContent
					align="end"
					sideOffset={10}
					className="w-[280px] max-w-[95vw] p-0 shadow-lg border border-gray-200 rounded-lg"
				>
					<PopoverHeader title="字段" className="font-medium" />

					<PopoverBody className="p-4">
						<div className="flex flex-col gap-3">
							<div className="grid grid-cols-2 gap-3">
								<Button
									variant={
										isDefaultView ? "primary" : "outline"
									}
									size="sm"
									onClick={handleDefaultView}
									className={cn(
										"w-full justify-center transition-all duration-200 rounded-md",
										isDefaultView
											? "shadow-sm"
											: "hover:bg-gray-50",
									)}
								>
									<span className="flex items-center">
										默认
									</span>
								</Button>
								<Button
									variant={isFullView ? "primary" : "outline"}
									size="sm"
									onClick={handleFullView}
									className={cn(
										"w-full justify-center transition-all duration-200 rounded-md",
										isFullView
											? "shadow-sm"
											: "hover:bg-gray-50",
									)}
								>
									<span className="flex items-center">
										全量
									</span>
								</Button>
							</div>
						</div>
					</PopoverBody>
				</PopoverContent>
			</Popover>
		);
} 