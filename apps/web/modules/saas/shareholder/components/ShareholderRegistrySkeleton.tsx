"use client";

/**
 * 股东名册骨架屏组件
 * 在数据加载过程中显示的占位UI
 * 
 * @version 1.1.0 (2025-05-22) - 移除缩放适应逻辑，使用固定尺寸
 * @version 1.0.0 (2025-05-22) - 初始版本，与当前项目UI结构保持一致
 */
import { Skeleton } from "@ui/components/skeleton";

/**
 * 股东名册列表骨架屏组件
 * 在股东名册数据加载过程中显示的占位UI，包括筛选器和表格
 */
export function ShareholderRegistrySkeleton() {
  return (
    <div className="space-y-4">
      {/* 筛选器骨架屏 */}
      <div className="space-y-3">
        {/* 按钮组和筛选器 */}
        <div className="flex flex-wrap items-start justify-between gap-y-4">
          <div className="flex flex-wrap items-center max-w-full gap-2 md:gap-3">
            {/* 按钮组 */}
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-8 w-16 rounded-full" />
              <Skeleton className="h-8 w-16 rounded-full" />
              <Skeleton className="h-8 w-16 rounded-full" />
              <Skeleton className="h-8 w-[72px] rounded-full" />
            </div>
            
            {/* 期数选择 */}
            <Skeleton className="h-8 w-[120px]" />
            
            {/* 搜索框 */}
            <Skeleton className="h-8 w-[200px] sm:w-[160px] md:w-[180px] lg:w-[200px]" />
          </div>
          
          {/* 右侧按钮 */}
          <div className="flex flex-wrap items-center gap-3 shrink-0">
            <Skeleton className="h-8 w-24" />
          </div>
        </div>
      </div>
      
      {/* 表格骨架屏 */}
      <div className="rounded-md border border-slate-200 overflow-hidden shadow-sm">
        {/* 表头骨架屏 */}
        <div className="bg-gray-50 border-b border-slate-200 py-3">
          <div className="grid grid-cols-9 gap-1">
            <Skeleton className="h-4 w-20 mx-auto" />
            <Skeleton className="h-4 w-20 mx-auto" />
            <Skeleton className="h-4 w-20 mx-auto" />
            <Skeleton className="h-4 w-20 mx-auto" />
            <Skeleton className="h-4 w-20 mx-auto" />
            <Skeleton className="h-4 w-12 mx-auto" />
            <Skeleton className="h-4 w-16 mx-auto" />
            <Skeleton className="h-4 w-16 mx-auto" />
            <Skeleton className="h-4 w-12 mx-auto" />
          </div>
        </div>
        
        {/* 表格内容骨架屏 - 显示多行 */}
        <div className="divide-y divide-slate-100">
          {Array.from({ length: 10 }).map((_, i) => (
            <div key={i} className="grid grid-cols-9 gap-1 items-center py-3 px-2">
              <Skeleton className="h-4 w-24 mx-auto" />
              <Skeleton className="h-4 w-20 mx-auto" />
              <Skeleton className="h-4 w-28 mx-auto" />
              <Skeleton className="h-4 w-16 mx-auto" />
              <Skeleton className="h-4 w-16 mx-auto" />
              <Skeleton className="h-4 w-8 mx-auto" />
              <Skeleton className="h-4 w-16 mx-auto" />
              <Skeleton className="h-4 w-14 mx-auto" />
              <Skeleton className="h-4 w-10 mx-auto" />
            </div>
          ))}
        </div>
      </div>
      
      {/* 分页骨架屏 */}
      <div className="flex items-center justify-between border-gray-200 py-3 px-2 mt-4">
        <div className="flex items-center">
          <Skeleton className="h-7 w-7 rounded-sm" />
          <Skeleton className="h-7 w-7 rounded-sm ml-2" />
        </div>
        <div className="flex items-center gap-1 text-gray-500">
          <Skeleton className="h-4 w-48" />
        </div>
      </div>
    </div>
  );
}
