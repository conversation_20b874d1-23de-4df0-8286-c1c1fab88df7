"use client";

import { useCallback, useEffect, useRef } from "react";
import { Skeleton } from "@ui/components/skeleton";
import { ShareholderChangeToolbar } from "@saas/shareholder/components/change";
import { ShareholderChangeTableView } from "@saas/shareholder/components/change";
import { useShareholderChanges } from "@saas/shareholder/hooks/useShareholderChanges";
import { useOrganization } from "@saas/organizations/hooks/useOrganization";
/**
 * 股东持股变化分析组件属性接口
 *
 * @interface ShareholderChangeAnalysisProps
 * @property {string} organizationSlug - 组织名
 * @time 2025-06-11 15:42:59.427
 */
interface ShareholderListProps {
	organizationSlug?: string;
}

function ShareholderChangeSkeleton() {
	return (
		<>
						{/* 工具栏骨架屏 */}
						<div className="mb-2 p-2">
							<div className="flex flex-wrap items-center gap-4">
								{/* 开始日期选择骨架 */}
								<Skeleton className="h-9 w-[140px] rounded-md" />

								{/* 结束日期选择骨架 */}
								<Skeleton className="h-9 w-[140px] rounded-md" />

								{/* 股东类型选择骨架 */}
								<Skeleton className="h-9 w-[140px] rounded-md" />

								{/* 搜索框骨架 */}
								<Skeleton className="h-9 w-[300px] flex-1 min-w-[200px] max-w-[400px] rounded-md" />

								{/* 刷新按钮骨架 */}
								<Skeleton className="h-9 w-9 rounded-md" />
							</div>
						</div>

						{/* 表格骨架屏 */}
						<div className="rounded-md border">
							{/* 表头骨架 */}
							<div className="flex border-b p-2 gap-4">
								{Array.from({ length: 8 }).map((_, index) => (
									<Skeleton
										key={`header-${index}`}
										className="h-6 flex-1 rounded-md"
									/>
								))}
							</div>

							{/* 表格行骨架 - 生成5行 */}
							{Array.from({ length: 5 }).map((_, rowIndex) => (
								<div
									key={`row-${rowIndex}`}
									className="flex border-b p-2 gap-4"
								>
									{Array.from({ length: 8 }).map(
										(_, colIndex) => (
											<Skeleton
												key={`cell-${rowIndex}-${colIndex}`}
												className="h-6 flex-1 rounded-md"
											/>
										),
									)}
								</div>
							))}
						</div>
					</>
	);
}

/**
 * 股东持股变化分析组件
 * 用于展示股东持股变化的分析数据和图表
 * 
 * <AUTHOR>
 * @created 2024-06-28 16:23:45.621
 * @modified 2025年06月11日 18:03:39
 * @param {ShareholderChangeAnalysisProps} props - 组件属性
 * @returns {JSX.Element} 股东持股变化分析组件
 * @time 2025年06月11日 18:03:39
 */
export function ShareholderChangeComposite({
		organizationSlug = "",
	}: ShareholderListProps) {
		// 获取半年内的日期范围作为默认值（仅作为初始值，实际会根据期数日期列表更新）
		const today = new Date();
		const sixMonthsAgo = new Date(today);
		sixMonthsAgo.setMonth(today.getMonth() - 6);

		const defaultEndDate = today.toISOString().split("T")[0];
		const defaultStartDate = sixMonthsAgo.toISOString().split("T")[0];

		// 添加用户选择日期的标记，用于防止自动设置覆盖用户选择
		const userSelectedDatesRef = useRef({
			startDate: false,
			endDate: false
		});

		// 获取组织信息
		const { data: organization, isLoading: isLoadingOrg } =
			useOrganization(organizationSlug);
		// 使用股东持股变化钩子 - 修改于 2025-06-17 16:47:20.601，添加sortType相关状态
		const {
			shareholderChanges,
			availableDates,
			registerDates,
			shareholderTypes,
			startDate,
			endDate,
			shareholderType,
			searchTerm,
			sortType, // 新增：排序类型状态 - 添加于 2025-06-17 16:47:20.601
			sortOrder,
			page, // 添加页码状态 - 添加于 2025-06-13 14:58:45.257
			pagination, // 添加分页信息 - 添加于 2025-06-13 14:58:45.257
			isLoading,

			setStartDate,
			setEndDate,
			setShareholderType,
			setSearchTerm,
			setSortType, // 新增：排序类型设置函数 - 添加于 2025-06-17 16:47:20.601
			setSortOrder,
			setPage, // 添加页码设置函数 - 添加于 2025-06-13 14:58:45.257
			resetAllFilters,
			refetch,
		} = useShareholderChanges(
			organization?.id || "",
			defaultStartDate,
			defaultEndDate,
		);

		// 自定义日期设置处理函数，追踪用户手动选择
		const handleStartDateChange = useCallback((date: string) => {
			userSelectedDatesRef.current.startDate = true;
			setStartDate(date);
		}, [setStartDate]);

		const handleEndDateChange = useCallback((date: string) => {
			userSelectedDatesRef.current.endDate = true;
			setEndDate(date);
		}, [setEndDate]);

		// 当期数日期列表加载完成后，更新开始和结束日期
		// 修改：仅在用户未手动选择日期的情况下自动设置
		useEffect(() => {
			if (registerDates.length > 0) {
				// 按照日期从新到旧排序
				const sortedDates = [...registerDates].sort((a, b) => b.localeCompare(a));
				
				// 检查是否由用户设置了日期
				const isStartDateUserSet = userSelectedDatesRef.current.startDate;
				const isEndDateUserSet = userSelectedDatesRef.current.endDate;
				
				// 设置最新的日期为结束日期（仅当用户未手动选择结束日期时）
				if (sortedDates[0] && sortedDates[0] !== endDate && !isEndDateUserSet) {
					setEndDate(sortedDates[0]);
				}
				
				// 如果有多个日期，设置最早的日期为开始日期（仅当用户未手动选择开始日期时）
				if (sortedDates.length > 1 && sortedDates[sortedDates.length - 1] !== startDate && !isStartDateUserSet) {
					setStartDate(sortedDates[sortedDates.length - 1]);
				}
			}
		}, [registerDates, setStartDate, setEndDate, startDate, endDate]);

		// 处理刷新按钮点击
		const handleRefresh = useCallback(() => {
			// 重置用户选择状态，允许系统在数据刷新后重新设置日期范围
			userSelectedDatesRef.current.startDate = false;
			userSelectedDatesRef.current.endDate = false;
			
			// 使用全面的重置函数，重置所有状态到初始状态
			resetAllFilters(); // 替代resetFilters，更全面地重置所有状态
			
			// 刷新所有数据
			refetch(); // 刷新股东变化数据
		}, [refetch, resetAllFilters]);

		// 处理排序 - 修改于 2025-06-17 17:09:31.041，更新sortType参数支持具体日期格式
		const handleSort = useCallback(
			(_sortKey: string, sortOrder: "asc" | "desc", sortType?: "rank" | "date" | string) => {
				// 如果传入了sortType，则更新排序类型（支持具体日期格式）
				if (sortType) {
					setSortType(sortType);
				}
				setSortOrder(sortOrder);
			},
			[setSortType, setSortOrder],
		);


		return (
			<div className="flex flex-col gap-2">
				{organization?.id ? (
					<>
						<ShareholderChangeToolbar
							startDate={startDate}
							endDate={endDate}
							shareholderType={shareholderType || "all"}
							searchTerm={searchTerm || ""}
							availableDates={availableDates}
							shareholderTypes={shareholderTypes}
							onStartDateChange={handleStartDateChange}
							onEndDateChange={handleEndDateChange}
							onShareholderTypeChange={setShareholderType}
							onSearchChange={setSearchTerm}
							onRefresh={handleRefresh}
							isLoading={isLoading}
						/>
						<ShareholderChangeTableView
							data={shareholderChanges}
							loading={isLoading}
							availableDates={availableDates}
							onSort={handleSort}
							page={page} // 添加页码属性 - 添加于 2025-06-13 14:58:45.257
							onPageChange={setPage} // 添加页码变更回调 - 添加于 2025-06-13 14:58:45.257
							pagination={pagination} // 修复：添加缺失的分页信息属性 - 修复于 2025-06-13 15:06:00.611
						/>
					</>
				) : (
					<ShareholderChangeSkeleton />
				)}
			</div>
		);
	}

