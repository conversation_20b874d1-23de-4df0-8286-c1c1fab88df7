"use client";

import { ShareholderRegistryHeader } from "@saas/shareholder/components/ShareholderRegistryHeader";
import { ShareholderRegistryTable } from "@saas/shareholder/components/ShareholderRegistryTable";
import { InfoIcon } from "lucide-react";
import { useOrganization } from "@saas/organizations/hooks/useOrganization";
import { useShareholderRegistryManage } from "@saas/shareholder/hooks/useShareholderRegistryManage";
import {
  ImportShareholderRegistryDialog
} from "@saas/shareholder/components/dialogs";
import { useSystemScale } from "@saas/shareholder/hooks/useSystemScale";
import { cn } from "@ui/lib";
import { ShareholderRegistrySkeleton } from "@saas/shareholder/components/ShareholderRegistrySkeleton";
import { useEffect, useRef } from "react";

interface ShareholderRegistryManageProps {
  organizationSlug: string;
}

/**
 * 股东名册管理主组件
 * 整合所有子组件并处理业务逻辑
 * 
 * @version 3.2.0 (2025-05-28) - 添加组织信息刷新机制，确保删除名册后公司代码和简称更新
 * @version 3.1.0 (2025-05-20) - 添加批量删除API支持，优化批量操作体验
 * @version 3.0.0 (2025-05-19) - 增加系统缩放适配，优化在高DPI显示器上的视觉体验
 * @version 2.0.0 (2025-05-19) - 移除移动端适配逻辑，只保留桌面端布局
 */
export function ShareholderRegistryManage({
  organizationSlug,
}: ShareholderRegistryManageProps) {  
  // 使用系统缩放hook获取缩放比例和样式配置
  const { scale } = useSystemScale();
  
  // 使用useOrganization钩子获取组织信息
  const { data: organization, isLoading: isLoadingOrganization, refetch: refetchOrganization } = useOrganization(organizationSlug);
  const organizationId = organization?.id || "";
  
  // 使用自定义钩子获取股东名册数据和管理方法
		const {
			// 状态
			isImportOpen,
			isBatchDeleting,
			isLoadingRegistries,

			// 数据
			registries,
			registriesPagination,
			currentCompanyCode,
			currentCompanyName,
			page,
			limit,
			selectedItems,

			// 操作方法
			setPage,
			setLimit,

			// 业务方法
			handleImport,
			handleImportClose,
			directDeleteRegistry,
			handleSelectItem,
			handleSelectAll,
			batchDeleteRegistries,
			refetchRegistries,
		} = useShareholderRegistryManage(organizationId, organization);
	// 在组件内部
	const isRefreshingRef = useRef(false);

	// 修改useEffect - 增强防抖逻辑
	useEffect(() => {
		if (registries && !isRefreshingRef.current) {
			if (registries.length === 0 || registries.length === 1) {
				isRefreshingRef.current = true;
				refetchOrganization().finally(() => {
					// 设置一个延迟，防止短时间内重复刷新
					setTimeout(() => {
						isRefreshingRef.current = false;
					}, 2000); // 增加延迟时间到2秒
				});
			}
		}
	}, [registries?.length, refetchOrganization]);

  // 显示加载状态或空状态
  if (isLoadingOrganization) {
    return <ShareholderRegistrySkeleton />;
  }

  if (!organizationId) {
    return (
      <div className={cn(
        "flex w-full flex-col items-center justify-center",
        scale > 1.25 ? "h-32" : "h-36"
      )}>
        <InfoIcon className={cn(
          "text-muted-foreground",
          scale > 1.25 ? "h-4 w-4" : "h-5 w-5"
        )} />
        <h3 className={cn(
          "mt-2 font-medium",
          scale > 1.25 ? "text-sm" : "text-base"
        )}>无法加载组织信息</h3>
        <p className={cn(
          "text-muted-foreground",
          scale > 1.25 ? "text-[10px]" : "text-xs"
        )}>
          请确认组织是否存在，或者刷新页面重试
        </p>
      </div>
    );
  }

  // 处理刷新期数和组织信息 - 添加防抖
  const handleRefreshAll = () => {
    refetchRegistries();
    
    // 使用同样的防抖机制
    if (!isRefreshingRef.current) {
      isRefreshingRef.current = true;
      refetchOrganization().finally(() => {
        setTimeout(() => {
          isRefreshingRef.current = false;
        }, 2000);
      });
    }
  };

  return (
			<div className={cn("space-y-4", scale > 1.25 && "space-y-3")}>
				{/* 头部 */}
				<ShareholderRegistryHeader
					onImport={handleImport}
					onBatchDelete={batchDeleteRegistries}
					companyCode={currentCompanyCode}
					companyName={currentCompanyName}
					isLoading={isBatchDeleting}
					selectedCount={selectedItems.length}
					onPeriodRefresh={handleRefreshAll}
				/>

				{/* 名册列表 */}
				<ShareholderRegistryTable
					registries={registries}
					pagination={registriesPagination}
					isLoading={isLoadingRegistries}
					page={page}
					limit={limit}
					onPageChange={setPage}
					onLimitChange={setLimit}
					onDeleteRegistry={directDeleteRegistry}
					selectedItems={selectedItems}
					onSelectItem={handleSelectItem}
					onSelectAll={handleSelectAll}
					disableSelection={isBatchDeleting}
				/>

				{/* 导入对话框 */}
				<ImportShareholderRegistryDialog
					isOpen={isImportOpen}
					organizationId={organizationId}
					onOpenChange={handleImportClose}
				/>
			</div>
		);
} 