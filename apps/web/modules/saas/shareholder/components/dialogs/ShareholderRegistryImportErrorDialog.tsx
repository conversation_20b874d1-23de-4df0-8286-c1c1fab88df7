import { AlertCircle, HelpCircle } from "lucide-react";
import { Button } from "@ui/components/button";

interface ShareholderRegistryImportErrorDialogProps {
  message: string;
  isLastFile: boolean;
  fileName?: string;
  onOpenHelpDialog: () => void;
  onSkip: () => void;
  onCancel: () => void;
}

/**
 * 股东名册导入错误对话框组件
 * 用于显示导入时的错误信息，并提供跳过和返回选项
 * 
 * @param message - 错误信息内容
 * @param isLastFile - 是否为最后一个文件
 * @param fileName - 文件名称
 * @param onOpenHelpDialog - 打开帮助对话框的回调函数
 * @param onSkip - 跳过当前文件的回调函数
 * @param onCancel - 取消导入的回调函数
 * 
 * @version 2.1.0 (2025-05-27) - 添加文件名显示
 * @version 2.0.0 (2025-05-19) - 重构错误处理流程
 * <AUTHOR> 2025-05-19
 */
export function ShareholderRegistryImportErrorDialog({
  message,
  isLastFile,
  fileName,
  onOpenHelpDialog,
  onSkip,
  onCancel,
}: ShareholderRegistryImportErrorDialogProps) {
  return (
    // 容器
    <div className="w-full">
      {/* 错误提示框 */}
      <div 
        className="rounded-md border border-red-200 bg-red-50 text-red-800 p-4"
      >
        <div className="flex items-start">
          {/* 错误图标 */}
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5" />
          </div>
          
          {/* 错误内容区域 */}
          <div className="ml-3 flex-1">
            {/* 错误标题 */}
            <h3 className="font-medium text-sm">导入失败</h3>
            {/* 文件名称(如果存在) */}
            {fileName && (
              <div className="mt-1 text-xs font-medium text-red-700">
                文件：{fileName}
              </div>
            )}
            {/* 错误信息 */}
            <div className="mt-2 text-sm">
              {message}
            </div>
            
            {/* 操作按钮区域 */}
            <div className="flex justify-between items-center border-t border-red-200 mt-3 pt-3">
              {/* 帮助按钮 */}
              <Button
                variant="ghost"
                size="sm"
                className="text-red-700 hover:text-red-800 hover:bg-red-100 h-8"
                onClick={onOpenHelpDialog}
              >
                <HelpCircle className="mr-1 h-3.5 w-3.5" />
                查看导入说明
              </Button>
              
              {/* 操作按钮组 */}
              <div className="flex space-x-2">
                {/* 返回按钮 */}
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8"
                  onClick={onCancel}
                >
                  返回
                </Button>
                
                {/* 跳过按钮 - 在最后一个文件时禁用 */}
                <Button
                  variant="error"
                  size="sm"
                  className="h-8"
                  onClick={onSkip}
                  disabled={isLastFile}
                >
                  跳过
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 