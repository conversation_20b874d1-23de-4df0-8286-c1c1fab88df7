"use client";

import { Badge } from "@ui/components/badge";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger,
  TooltipPortal
} from "@ui/components/tooltip";
import { PlusIcon } from "lucide-react";

interface ShareholderTagsProps {
  tags?: string[];
  maxDisplay?: number;
}

/**
 * 股东标签组件
 * 显示股东的标签列表，超过指定数量使用Tooltip显示
 */
export function ShareholderTags({ tags, maxDisplay = 2 }: ShareholderTagsProps) {
  if (!tags || tags.length === 0) {
    return null;
  }
  
  // 如果标签数量小于等于最大显示数量，直接显示所有标签
  if (tags.length <= maxDisplay) {
    return (
      <div className="flex flex-wrap gap-1">
        {tags.map((tag, i) => (
          <Badge key={i} status="info" className="text-xs">{tag}</Badge>
        ))}
      </div>
    );
  }
  
  // 如果标签数量超过最大显示数量，显示部分标签并使用Tooltip显示全部
  return (
    <div className="flex flex-wrap gap-1 items-center">
      {/* 显示前几个标签 */}
      {tags.slice(0, maxDisplay).map((tag, i) => (
        <Badge key={i} status="info" className="text-xs">{tag}</Badge>
      ))}
      
      {/* 使用Tooltip显示剩余标签 */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="cursor-pointer inline-flex items-center rounded-full px-3 py-1 text-xs bg-gray-100 text-gray-700">
              <PlusIcon className="h-3 w-3 mr-1" />
              {tags.length - maxDisplay}
            </span>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent side="top" align="start" className="p-2">
              <div className="flex flex-wrap gap-1 max-w-[250px]">
                {tags.slice(maxDisplay).map((tag, i) => (
                  <Badge key={i} status="info" className="text-xs">{tag}</Badge>
                ))}
              </div>
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
} 