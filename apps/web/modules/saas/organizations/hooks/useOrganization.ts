import { useQuery } from "@tanstack/react-query";
import { authClient } from "@repo/auth/client";

/**
 * 通过组织slug获取组织信息
 * @param slug 组织slug
 * @returns 组织信息和查询状态
 */
export function useOrganization(slug: string) {
  // 使用 React Query 的 useQuery hook 来获取和缓存组织数据
  return useQuery({
    // 使用组织slug作为缓存key
    queryKey: ["organization", slug],
    
    // 定义获取数据的异步函数
    queryFn: async () => {
      // 调用auth客户端API获取完整的组织信息
      const { data, error } = await authClient.organization.getFullOrganization({
        query: {
          organizationSlug: slug,
        },
      });

      // 如果返回错误则抛出异常
      if (error) {
        throw new Error(
          error.message || "获取组织信息失败"
        );
      }

      // 返回获取的组织数据
      return data;
    },
    
    // 只有当slug存在时才启用查询
    enabled: !!slug
  });
} 