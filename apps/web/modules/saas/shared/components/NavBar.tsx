"use client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { UserMenu } from "@saas/shared/components/UserMenu";
import { Logo } from "@shared/components/Logo";
import { cn } from "@ui/lib";
import {
	ChevronRightIcon,
	HomeIcon,
	RocketIcon,
	SettingsIcon,
	UserCog2Icon,
	UserCogIcon,
	LineChartIcon,
	CalendarDaysIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { OrganzationSelect } from "../../organizations/components/OrganizationSelect";

export function NavBar() {
	const t = useTranslations();
	const pathname = usePathname();
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();

	const { useSidebarLayout } = config.ui.saas;

	const basePath = activeOrganization
		? `/app/${activeOrganization.slug}`
		: "/app";

	const menuItems = [
		// 如果有活跃组织，显示市值管理菜单，否则显示首页菜单
		...(activeOrganization
			? [
					{
						label: t("app.menu.start"), // 市值管理菜单
						href: `/app/${activeOrganization.slug}`,
						icon: LineChartIcon,
						isActive:
							pathname === basePath ||
							pathname.includes("/market"),
					},
				]
			: [
					{
						label: t("app.menu.home"), // 首页菜单
						href: "/app",
						icon: HomeIcon,
						isActive: pathname === "/app",
					},
				]),

		// 如果有活跃组织，显示股东、会议、AI聊天机器人和组织设置菜单
		...(activeOrganization
			? [
					{
						label: t("app.menu.shareholder"), // 股东菜单
						href: `/app/${activeOrganization.slug}/shareholder/analysis`,
						icon: RocketIcon,
						isActive: pathname.includes("/shareholder"),
					},
					{
						label: t("app.menu.meeting"), // 会议菜单
						href: `/app/${activeOrganization.slug}/meeting/list`,
						icon: CalendarDaysIcon,
						isActive: pathname.includes("/meeting"),
					},
					// 2025-05-07 17:09:40 先取消ai聊天机器人菜单，暂时不用
					// {
					// 	label: t("app.menu.aiChatbot"), // AI聊天机器人菜单
					// 	href: activeOrganization
					// 		? `/app/${activeOrganization.slug}/chatbot`
					// 		: "/app/chatbot",
					// 	icon: BotMessageSquareIcon,
					// 	isActive: pathname.includes("/chatbot"),
					// },
					/* 原代码:
					 * {
					 * 	label: t("app.menu.organizationSettings"), // 组织设置菜单
					 * 	href: `${basePath}/settings`,
					 * 	icon: SettingsIcon,
					 * 	isActive: pathname.startsWith(`${basePath}/settings/`),
					 * },
					 * 修改原因: 将组织设置移至用户菜单，减少主导航的设置按钮显著性
					 * 修改时间: 2025-06-28
					 * 修改人: Alson
					 * 关联需求: 极简界面优化，将组织设置移至用户菜单
					 * 恢复方法: 删除此注释，取消上方原代码的注释
					 */
				]
			: [
					{
						label: t("app.menu.accountSettings"), // 账户设置菜单
						href: "/app/settings",
						icon: UserCog2Icon,
						isActive: pathname.startsWith("/app/settings/"),
					},
				]),

		// 如果用户是管理员且在个人页面，显示管理员菜单
		...(!activeOrganization && user?.role === "admin"
			? [
					{
						label: t("app.menu.admin"), // 管理员菜单
						href: "/app/admin",
						icon: UserCogIcon,
						isActive: pathname.startsWith("/app/admin/"),
					},
				]
			: []),
	];

	return (
		<nav
			className={cn("w-full", {
				"w-full md:fixed md:top-0 md:left-0 md:h-full md:w-[280px]":
					useSidebarLayout,
			})}
		>
			<div
				className={cn("container max-w-6xl py-4", {
					"container max-w-6xl py-4 md:flex md:h-full md:flex-col md:px-6 md:pt-6 md:pb-0":
						useSidebarLayout,
				})}
			>
				<div className="flex flex-wrap items-center justify-between gap-4">
					<div
						className={cn("flex items-center gap-4 md:gap-2", {
							"md:flex md:w-full md:flex-col md:items-stretch md:align-stretch":
								useSidebarLayout,
						})}
					>
						<Link href="/app" className="block">
							<Logo />
						</Link>

						{config.organizations.enable &&
							!config.organizations.hideOrganization && (
								<>
									<span
										className={cn(
											"hidden opacity-30 md:block",
											{
												"md:hidden": useSidebarLayout,
											},
										)}
									>
										<ChevronRightIcon className="size-4" />
									</span>

									<OrganzationSelect
										className={cn({
											"md:-mx-2 md:mt-2":
												useSidebarLayout,
										})}
									/>
								</>
							)}
					</div>

					<div
						className={cn(
							"mr-0 ml-auto flex items-center justify-end gap-4",
							{
								"md:hidden": useSidebarLayout,
							},
						)}
					>
						<UserMenu />
					</div>
				</div>

				<ul
					className={cn(
						"no-scrollbar -mx-4 -mb-4 mt-6 flex list-none items-center justify-start gap-4 overflow-x-auto px-4 text-sm",
						{
							"md:mx-0 md:my-4 md:flex md:flex-col md:items-stretch md:gap-1 md:px-0":
								useSidebarLayout,
						},
					)}
				>
					{menuItems.map((menuItem) => (
						<li key={menuItem.href}>
							<Link
								href={menuItem.href}
								className={cn(
									"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3",
									[
										menuItem.isActive
											? "border-primary font-bold"
											: "border-transparent",
									],
									{
										"md:-mx-6 md:border-b-0 md:border-l-2 md:px-6 md:py-2":
											useSidebarLayout,
									},
								)}
							>
								<menuItem.icon
									className={`size-4 shrink-0 ${
										menuItem.isActive
											? "text-primary"
											: "opacity-50"
									}`}
								/>
								<span>{menuItem.label}</span>
							</Link>
						</li>
					))}
				</ul>

				<div
					className={cn(
						"-mx-4 md:-mx-6 mt-auto mb-0 hidden p-4 md:p-4",
						{
							"md:block": useSidebarLayout,
						},
					)}
				>
					<UserMenu showUserName />
				</div>
			</div>
		</nav>
	);
}