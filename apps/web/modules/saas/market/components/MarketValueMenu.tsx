"use client";

import { cn } from "@ui/lib";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type { ReactNode } from "react";
import { useTranslations } from "next-intl";

// 定义市场价值菜单的翻译键类型
export type marketValueMenuTranslationKey =
	| "monitoring"
	| "industry-industry" 
	| "analysis-report"
	| "title";

// 定义单个菜单项的接口
export interface MarketValueMenuItem {
	title?: string; // 可选的标题
	titleKey: marketValueMenuTranslationKey; // 翻译键
	href: string; // 链接地址
	icon?: ReactNode; // 可选的图标
}

// 定义菜单组的接口
export interface MarketValueMenuGroup {
	title?: string; // 可选的组标题
	titleKey?: marketValueMenuTranslationKey; // 可选的组标题翻译键
	avatar?: ReactNode; // 可选的头像
	items: MarketValueMenuItem[]; // 菜单项数组
}

// 定义组件Props接口
interface MarketValueMenuProps {
	menuItems: MarketValueMenuGroup[]; // 菜单组数组
	layout?: "vertical" | "horizontal"; // 新增布局参数，支持垂直或水平布局
}

/**
 * 市场价值菜单组件
 * 支持垂直和水平两种布局方式
 * 
 * @param menuItems 菜单项配置
 * @param layout 布局方式，默认为垂直布局
 * @returns 渲染的菜单组件
 */
export function MarketValueMenu({ menuItems, layout = "vertical" }: MarketValueMenuProps) {
	const pathname = usePathname(); // 获取当前路径
	const t = useTranslations("market-value.menu"); // 获取翻译函数
	
	// 判断是否是根路径（只包含组织slug，不包含具体功能路径）
	const isRootPath = pathname.match(/\/app\/[^\/]+\/market$/);

	// 判断菜单项是否激活
	const isActiveMenuItem = (href: string, index: number) => {
		// 如果是根路径且当前是第一个菜单项，则高亮
		if (isRootPath && index === 0) {
			return true;
		}
		// 否则检查路径是否包含当前链接
		return pathname.includes(href);
	};

	return (
		<div className="space-y-8">
			{menuItems.map((item, i) => (
				<div key={i}>
					{/* 渲染菜单组标题 */}
					{(item.title || item.titleKey) && (
						<div className="flex items-center justify-start gap-2">
							{item.avatar}
							<h2 className="text-xs font-semibold text-foreground/60">
								{item.titleKey ? t(item.titleKey) : item.title}
							</h2>
						</div>
					)}

					{/* 渲染菜单项列表 - 根据layout参数决定布局方式 */}
					<ul className={cn(
						"mt-2 flex list-none", 
						layout === "horizontal" 
							? "flex-row gap-6" // 水平布局样式
							: "flex-row gap-6 lg:mt-4 lg:flex-col lg:gap-2" // 垂直布局样式（响应式）
					)}>
						{item.items.map((subitem, k) => (
							<li key={k}>
								<Link
									href={subitem.href}
									className={cn(
										"flex items-center gap-2 py-1.5 text-sm",
										// 水平布局只使用底部边框，垂直布局在大屏幕时使用左侧边框
										layout === "horizontal"
											? "border-b-2" // 水平布局样式
											: "lg:-ml-0.5 border-b-2 lg:border-b-0 lg:border-l-2 lg:pl-2", // 垂直布局样式
										isActiveMenuItem(subitem.href, k)
											? "border-primary font-bold" // 激活状态样式
											: "border-transparent", // 非激活状态样式
									)}
									data-active={isActiveMenuItem(subitem.href, k)}
								>
									<span className="shrink-0">
										{subitem.icon}
									</span>
									<span>{subitem.titleKey ? t(subitem.titleKey) : subitem.title}</span>
								</Link>
							</li>
						))}
					</ul>
				</div>
			))}
		</div>
	);
}