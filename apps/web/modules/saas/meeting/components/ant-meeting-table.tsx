import * as React from "react";
import { cn } from "@ui/lib";
import { Table, Config<PERSON><PERSON><PERSON>, But<PERSON>} from "antd";
import type { TableProps, TableColumnType } from "antd";
import type { SpinProps } from "antd";
import { useTheme } from "next-themes";
import zhCN from "antd/lib/locale/zh_CN";
import { ChevronDown, ChevronUp } from "lucide-react";
import type { Meeting } from "@saas/meeting/components/types";

import Link from "next/link";

// 禁用 antd 的 React 版本兼容性警告
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
	if (
		typeof args[0] === "string" &&
		args[0].includes("antd: compatible") &&
		args[0].includes("antd v5 support React is 16 ~ 18")
	) {
		return;
	}
	originalConsoleError(...args);
};

/**
 * 会议表格项目接口
 */


/**
 * 会议表格列配置接口
 */
export interface MeetingColumn {
	key: string;
	title: string;
	className?: string;
	render?: (value: any, record: Meeting) => React.ReactNode;
	sortable?: boolean;
	width?: number;
	hidden?: boolean;
}

/**
 * 会议表格组件接口
 */
export interface AntMeetingTableProps {
	columns: TableColumnType<Meeting>[];
	data: Meeting[];
	className?: string;
	headerClassName?: string;
	rowClassName?: string;
	cellClassName?: string;
	onRowClick?: (item: Meeting) => void;
	emptyContent?: React.ReactNode;
	loading?: boolean | SpinProps;
	onSort?: (sortKey: string, sortOrder: "asc" | "desc") => void;
	footer?: React.ReactNode;

	// 会议相关的回调函数
	handleCancelMeeting?: (meetingId: string) => void;
	handleExportParticipants?: (meetingId: string, title: string) => void;
	handleGetSignInRecord?: (meetingId: string, title: string) => void;
	handleGetMeetingDocs?: (meetingId: string, title: string) => void;
	handleGetMeetingRecord?: (meetingId: string, title: string) => void;
	handleShowTranscript?: (meetingId: string, title: string) => void;
	handleShowAISummary?: (meetingId: string, title: string) => void;

	// 配置选项
	showCancelOption?: boolean;
	showDocAndRecordButtons?: boolean;
	cancelingMeetingId?: string | null;
	organizationSlug?: string;
	timeColumnLabel?: string;
	sortDirection?: "asc" | "desc";
	toggleSortDirection?: () => void;
}

/**
 * 生成表格的自定义样式
 */
function getTableStyle(isDarkTheme: boolean): React.CSSProperties {
	return {
		"--ant-table-bg": isDarkTheme ? "var(--card)" : "white",
		"--ant-table-header-bg": isDarkTheme ? "var(--card)" : "white",
		"--ant-table-header-color": isDarkTheme
			? "rgba(255, 255, 255, 0.85)"
			: "var(--foreground)",
		"--ant-table-text-color": isDarkTheme
			? "rgba(255, 255, 255, 0.85)"
			: "var(--foreground)",
		"--ant-table-row-hover-bg": isDarkTheme
			? "rgba(255, 255, 255, 0.08)"
			: "rgba(0, 0, 0, 0.04)",
		"--ant-light-row-hover-bg": "rgba(0, 0, 0, 0.04)",
		"--ant-table-text-hover-color": isDarkTheme
			? "#fff"
			: "var(--foreground)",
		"--ant-table-border-color": isDarkTheme
			? "rgba(255, 255, 255, 0.12)"
			: "var(--border)",
		"--ant-scrollbar-thumb-color": isDarkTheme
			? "rgba(255, 255, 255, 0.3)"
			: "rgba(0, 0, 0, 0.2)",
		"--ant-scrollbar-track-color": isDarkTheme
			? "rgba(255, 255, 255, 0.05)"
			: "rgba(0, 0, 0, 0.05)",
	} as React.CSSProperties;
}

/**
 * 会议表格组件
 */
export function AntMeetingTable({
	columns,
	data,
	className,
	rowClassName,
	onRowClick,
	emptyContent = <div className="h-full" />,
	loading = false,
	onSort,
	footer,

	// 会议相关的回调函数
	handleCancelMeeting,
	handleExportParticipants,
	handleGetSignInRecord,
	handleGetMeetingDocs,
	handleGetMeetingRecord,
	handleShowTranscript,
	handleShowAISummary,

	// 配置选项
	showCancelOption = false,
	showDocAndRecordButtons = false,
	cancelingMeetingId,
	organizationSlug = "",
	timeColumnLabel = "时间",
	sortDirection = "desc",
	toggleSortDirection,
}: AntMeetingTableProps): JSX.Element {
	// 获取当前主题
	const { resolvedTheme } = useTheme();
	const isDarkTheme = resolvedTheme === "dark";

	// 定义表格列配置
	const antColumns: TableColumnType<Meeting>[] = React.useMemo(() => {
		const columns: TableColumnType<Meeting>[] = [
			{
				title: (
					<button
						type="button"
						className="flex items-center cursor-pointer text-left hover:text-gray-700 text-sm font-medium"
						onClick={toggleSortDirection}
						onKeyDown={(e) =>
							e.key === "Enter" && toggleSortDirection?.()
						}
					>
						{timeColumnLabel}
						<span
							className="ml-1 focus:outline-none"
							title={
								sortDirection === "asc"
									? "点击降序排列"
									: "点击升序排列"
							}
						>
							{sortDirection === "asc" ? (
								<ChevronUp className="size-4" />
							) : (
								<ChevronDown className="size-4" />
							)}
						</span>
					</button>
				),
				dataIndex: "startTime",
				key: "startTime",
				width: 200,
				render: (startTime) => (
					<div className="flex items-center text-black-700 text-sm">
						{startTime}
					</div>
				),
			},
			{
				title: "会议主题",
				dataIndex: "title",
				key: "title",
				width: 300,
				render: (title, record) => (
					<div className="text-black-700 text-sm">
						{title}
						{/* <a
						href={`/app/${organizationSlug}/meeting/list/${record.id}-${encodeURIComponent(title.toLowerCase().replace(/\s+/g, "-"))}`}
						className="text-blue-600 hover:underline truncate text-sm"
					>
						{title}
					</a> */}
					</div>
				),
			},
			{
				title: "会议号",
				dataIndex: "meetingId",
				key: "meetingId",
				width: 150,
				render: (meetingId) => (
					<div className="text-black-700 text-sm">{meetingId}</div>
				),
			},
			{
				title: "操作",
				key: "actions",
				width: 200,
				render: (_, record) => (
					<div className="flex items-center justify-center gap-2">
						{record.status !== "MEETING_STATE_ENDED" && (
							<Button
								type="link"
								className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm"
								href={record.joinURL || "#"}
								target="_blank"
								rel="noopener noreferrer"
							>
								进入
							</Button>
						)}
						{record.status !== "MEETING_STATE_ENDED" && (
							<Link
								className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm"
								href={`/app/${organizationSlug}/meeting/list/change_meeting/${record.id}`}
							>
								修改
							</Link>
						)}
						{showCancelOption &&
							handleCancelMeeting &&
							record.status !== "MEETING_STATE_ENDED" && (
								<Button
									type="link"
									danger
									className="h-7 px-2 flex items-center gap-1 text-black-700 text-sm hover:text-black-900 text-sm"
									onClick={() =>
										handleCancelMeeting(record.id)
									}
									disabled={cancelingMeetingId === record.id}
								>
									{cancelingMeetingId === record.id ? (
										<div className="flex items-center">
											<div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1" />
											取消中...
										</div>
									) : (
										"取消"
									)}
								</Button>
							)}
					</div>
				),
			},
		];

		// 如果显示文档和录制按钮，添加会议记录列
		if (showDocAndRecordButtons) {
            columns.pop();
			columns.push({
				title: "会议记录",
				key: "meetingRecord",
				width: 300,
				render: (_, record) => (
					<div className="flex items-center justify-center gap-2">
						<button
							type="button"
							onClick={() =>
								handleGetSignInRecord?.(record.id, record.title)
							}
							className="text-blue-500 hover:underline truncate"
						>
							签到记录
						</button>

						<button
							type="button"
							onClick={() =>
								handleExportParticipants?.(record.id, record.title)
							}
							className="text-blue-500 hover:underline truncate"
						>
							参会人员
						</button>

						<button
							type="button"
							onClick={() =>
								handleGetMeetingDocs?.(record.id, record.title)
							}
							className="text-blue-500 hover:underline truncate"
						>
							会议文档
						</button>

						<button
							type="button"
							onClick={() =>
								handleGetMeetingRecord?.(record.id, record.title)
							}
							className="text-blue-500 hover:underline truncate"
						>
							会议录制
						</button>

						<button
							type="button"
							onClick={() =>
								handleShowTranscript?.(record.id, record.title)
							}
							className="text-blue-500 hover:underline truncate"
						>
							会议记录
						</button>

						<button
							type="button"
							onClick={() =>
								handleShowAISummary?.(record.id, record.title)
							}
							className="text-blue-500 hover:underline truncate"
						>
							AI会议纪要
						</button>
					</div>
				),
			});
		}

		return columns;
	}, [
		timeColumnLabel,
		sortDirection,
		toggleSortDirection,
		organizationSlug,
		showCancelOption,
		showDocAndRecordButtons,
		cancelingMeetingId,
		handleCancelMeeting,
		handleExportParticipants,
		handleGetSignInRecord,
		handleGetMeetingDocs,
		handleGetMeetingRecord,
		handleShowTranscript,
		handleShowAISummary,
	]);

	// 处理排序变化
	const handleChange: TableProps<Meeting>["onChange"] = (
		_pagination,
		_filters,
		sorter,
	) => {
		if (sorter && !Array.isArray(sorter) && onSort) {
			const field = sorter.field as string;
			const sortOrder = sorter.order === "ascend" ? "asc" : "desc";
			onSort(field, sortOrder);
		}
	};

	// 生成Ant Design表格的类名
	const tableClassName = cn(
		"ant-meeting-table",
		{
			"ant-meeting-table-dark": isDarkTheme,
		},
		className,
	);

	// 行点击处理
	const onRow = onRowClick
		? (record: Meeting) => ({
				onClick: () => {
					onRowClick(record);
				},
			})
		: undefined;

	// 使用提取的函数获取样式
	const tableStyle = React.useMemo(
		() => getTableStyle(isDarkTheme),
		[isDarkTheme],
	);

	// 滚动配置
	const scroll = React.useMemo(() => {
		const shouldEnableYScroll = data.length > 10;

		return {
			x: "max-content",
			y: shouldEnableYScroll ? 500 : undefined,
			scrollToFirstRowOnChange: false,
		};
	}, [data.length]);

	// 自定义表格文案
	const customLocale = React.useMemo(() => {
		return {
			...zhCN.Table,
			triggerDesc: "点击降序排列",
			triggerAsc: "点击升序排列",
			cancelSort: "取消排序",
			emptyText: emptyContent,
		};
	}, [emptyContent]);

	return (
		<div className={tableClassName} style={tableStyle}>
			<style jsx global>{`
        /* Ant Design表格自定义样式 */
        .ant-meeting-table {
          --scrollbar-size: 8px;
        }
        
        /* 基础样式 */
        .ant-shareholder-table .ant-table {
          background-color: var(--ant-table-bg);
          color: var(--ant-table-text-color);
        }
        
        .ant-meeting-table .ant-table-container {
          border-radius: var(--radius-sm);
          overflow: hidden;
        }
        
        /* 调整表格单元格内边距和行高 */
        .ant-shareholder-table .ant-table-tbody > tr > td {
          padding: 10px 8px;
          height: 48px; /* 固定行高 */
          line-height: 1.4;
          vertical-align: middle;
        }
        
        /* 调整表头内边距和高度 */
        .ant-meeting-table .ant-table-thead > tr > th {
          padding: 12px 8px;
          height: 46px;
          background-color: var(--ant-table-header-bg) !important;
          color: var(--ant-table-header-color);
          border-bottom: none !important;
          font-weight: 500 !important;
          font-family: inherit !important;
          text-align: center;
          line-height: 1.5715;
          vertical-align: middle;
          font-size: 14px;
        }
        
        /* 修复表头字体不一致问题 */
        .ant-meeting-table .ant-table-thead > tr > th .ant-table-column-title {
          font-weight: 500;
          font-family: inherit;
          color: inherit;
          font-size: 14px;
        }
        
        /* 黑色主题下表头颜色特殊处理 */
        .ant-meeting-table-dark .ant-table-thead > tr > th .ant-table-column-title {
          color: rgba(255, 255, 255, 0.9);
        }
        
        .ant-meeting-table .ant-table-tbody > tr > td {
          border-bottom: none !important;
          color: var(--ant-table-text-color);
          font-size: 14px;
          text-align: center;
        }
        
        /* 深色主题下文字颜色增强 */
        .ant-meeting-table-dark .ant-meeting-table .ant-table-tbody > tr > td {
          color: rgba(255, 255, 255, 0.85);
        }
        
        /* 修改于2025-06-12: 强制覆盖暗色主题下的按钮和链接文字颜色，解决text-black-700类优先级问题 */
        /* 原问题: 操作列中的按钮使用text-black-700类，在暗色主题下无法适配 */
        /* 修改范围: 覆盖.ant-meeting-table-dark下所有按钮和链接的文字颜色 */
        /* 恢复方法: 删除下面的CSS规则块，恢复原有样式 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn,
        .ant-meeting-table-dark .ant-table-tbody > tr > td a,
        .ant-meeting-table-dark .ant-table-tbody > tr > td button {
          color: rgba(255, 255, 255, 0.85) !important;
        }
        
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn:hover,
        .ant-meeting-table-dark .ant-table-tbody > tr > td a:hover,
        .ant-meeting-table-dark .ant-table-tbody > tr > td button:hover {
          color: rgba(255, 255, 255, 1) !important;
        }
        
        /* 特殊处理危险按钮(取消按钮)的颜色 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn.ant-btn-dangerous {
          color: #ff4d4f !important;
        }
        
        .ant-meeting-table-dark .ant-table-tbody > tr > td .ant-btn.ant-btn-dangerous:hover {
          color: #ff7875 !important;
        }
        
        /* 深色主题下链接文字颜色 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td a {
          color: #4b9eff;
        }
        
        /* 深色主题下表格边框 */
        .ant-meeting-table-dark .ant-table {
          border-color: rgba(255, 255, 255, 0.12);
        }
        
        /* 悬停效果 */
        .ant-table-tbody > tr.ant-table-row,
        .ant-table-tbody > tr.ant-table-row > td {
          transition: background-color 0.2s ease-in-out !important;
        }
        
        /* 修复悬停效果在黑暗模式下变白的问题 */
        .ant-meeting-table .ant-table-tbody > tr.ant-table-row:hover > td {
          background-color: transparent !important; 
        }
        
        /* 浅色主题悬停样式 */
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-row-hover > td,
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr > td.ant-table-cell-row-hover {
          background-color: var(--ant-light-row-hover-bg) !important;
        }
        
        /* 直接覆盖Ant Design的原生悬停样式 */
        .ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td,
        .ant-table-wrapper .ant-table-tbody > tr > td.ant-table-cell-row-hover {
          background-color: var(--ant-table-row-hover-bg) !important;
          transition: background-color 0.2s ease-in-out !important;
        }
        
        /* 空状态样式 */
        .ant-meeting-table .ant-empty-description {
          color: var(--ant-table-text-color);
          font-size: 14px;
        }
        
        /* 排序图标样式 */
        .ant-meeting-table-dark .ant-table-column-sorter-up.active,
        .ant-meeting-table-dark .ant-table-column-sorter-down.active {
          color: #4b9eff;
        }
        
        .ant-meeting-table .ant-table-column-sorter-up.active,
        .ant-meeting-table .ant-table-column-sorter-down.active {
          color: var(--primary, #1890ff);
        }
        
        .ant-meeting-table .ant-table-filter-trigger {
          color: var(--ant-table-header-color);
        }
        
        .ant-meeting-table .ant-table-column-sorter {
          color: var(--ant-table-header-color);
        }
        
        /* 修复排序列背景变黑的问题 */
        .ant-meeting-table .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: transparent !important;
        }
        
        /* 亮色主题下排序列背景样式 */
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: rgba(0, 0, 0, 0.02) !important;
        }
        
        /* 暗色主题下排序列背景样式 */
        .ant-meeting-table-dark .ant-table-tbody > tr > td.ant-table-column-sort {
          background-color: rgba(255, 255, 255, 0.04) !important;
        }
        
        /* 确保排序列中的行悬停效果正常 */
        .ant-meeting-table:not(.ant-meeting-table-dark) .ant-table-tbody > tr:hover > td.ant-table-column-sort {
          background-color: var(--ant-light-row-hover-bg) !important;
        }
        
        .ant-meeting-table-dark .ant-table-tbody > tr:hover > td.ant-table-column-sort {
          background-color: var(--ant-table-row-hover-bg) !important;
        }
        
        /* 自定义滚动条样式 */
        .ant-meeting-table .ant-table-body::-webkit-scrollbar,
        .ant-meeting-table .ant-table-header::-webkit-scrollbar {
          width: var(--scrollbar-size);
          height: var(--scrollbar-size);
        }
        
        .ant-meeting-table .ant-table-body::-webkit-scrollbar-thumb,
        .ant-meeting-table .ant-table-header::-webkit-scrollbar-thumb {
          background-color: var(--ant-scrollbar-thumb-color);
          border-radius: calc(var(--scrollbar-size) / 2);
        }
        
        .ant-meeting-table .ant-table-body::-webkit-scrollbar-track,
        .ant-meeting-table .ant-table-header::-webkit-scrollbar-track {
          background-color: var(--ant-scrollbar-track-color);
        }
        
        /* 适配Firefox滚动条 */
        .ant-meeting-table .ant-table-body,
        .ant-meeting-table .ant-table-header {
          scrollbar-width: thin;
          scrollbar-color: var(--ant-scrollbar-thumb-color) var(--ant-scrollbar-track-color);
        }
        
        /* 修复筛选菜单在暗色模式下的颜色问题 */
        .ant-meeting-table-dark .ant-dropdown-menu {
          background-color: var(--popover);
          color: var(--popover-foreground);
        }
        
        .ant-meeting-table-dark .ant-dropdown-menu-item {
          color: var(--popover-foreground);
          font-size: 14px;
        }
        
        .ant-meeting-table-dark .ant-dropdown-menu-item:hover {
          background-color: var(--accent);
        }
        
        /* 加载状态样式 */
        .ant-meeting-table .ant-spin {
          color: var(--primary);
        }
        
        /* 移除最后一列的垂直分隔线 */
        .ant-meeting-table .ant-table-thead > tr > th:last-child,
        .ant-meeting-table .ant-table-tbody > tr > td:last-child {
          border-right: none !important;
        }
        
        /* 隐藏表格垂直分隔线 */
        .ant-meeting-table .ant-table-tbody > tr > td {
          border-right: none !important;
        }
        
        /* 隐藏右侧滚动指示器 */
        .ant-meeting-table .ant-table-container::after {
          display: none !important;
        }

        /* 按钮样式统一 */
        .ant-meeting-table .ant-btn {
          font-size: 14px;
          height: 32px;
          padding: 4px 15px;
          border-radius: 2px;
        }

        /* 链接样式统一 */
        .ant-meeting-table .ant-table-tbody > tr > td a {
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
        }

        .ant-meeting-table .ant-table-tbody > tr > td a:hover {
          color: #1890ff;
        }

        /* 下拉菜单样式统一 */
        .ant-meeting-table .ant-dropdown-menu {
          padding: 4px 0;
          font-size: 14px;
        }

        .ant-meeting-table .ant-dropdown-menu-item {
          padding: 5px 12px;
          line-height: 22px;
        }

        /* 固定列阴影效果 */
        .ant-meeting-table .ant-table-cell-fix-left-last::after,
        .ant-meeting-table .ant-table-cell-fix-right-first::after {
          box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.1) !important;
        }

        /* 自定义Tooltip样式 */
        .ant-tooltip .ant-tooltip-inner {
          background-color: var(--popover);
          color: var(--popover-foreground);
        }

        .ant-tooltip .ant-tooltip-arrow-content {
          background-color: var(--popover);
        }

        /* 确保垂直分隔线不显示 */
        .ant-meeting-table .ant-table-container table > thead > tr:first-child th:last-child {
          border-right: none !important;
        }

        /* 确保表格滚动区域不显示垂直线 */
        .ant-meeting-table .ant-table-ping-right .ant-table-cell-fix-right-first::after,
        .ant-meeting-table .ant-table-ping-right .ant-table-cell-fix-right-last::after {
          box-shadow: none !important;
        }
      `}</style>

			<ConfigProvider
				theme={{
					hashed: true,
					components: {
						Table: {
							colorBgContainer: isDarkTheme
								? "var(--card)"
								: "white",
							borderRadius: 0,
							colorBorderSecondary: isDarkTheme
								? "rgba(255, 255, 255, 0.12)"
								: "rgba(0, 0, 0, 0.06)",
							colorText: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: "rgba(0, 0, 0, 0.85)",
							colorTextHeading: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: "rgba(0, 0, 0, 0.85)",
							colorTextSecondary: isDarkTheme
								? "rgba(255, 255, 255, 0.65)"
								: "rgba(0, 0, 0, 0.65)",
							colorTextDisabled: isDarkTheme
								? "rgba(255, 255, 255, 0.3)"
								: "rgba(0, 0, 0, 0.3)",
							colorBgTextHover: isDarkTheme
								? "rgba(255, 255, 255, 0.08)"
								: "rgba(0, 0, 0, 0.04)",
							colorFillAlter: isDarkTheme
								? "rgba(255, 255, 255, 0.02)"
								: "rgba(0, 0, 0, 0.02)",
							motionDurationMid: "0s",
							motionDurationSlow: "0s",
							fontSize: 14,
							lineHeight: 1.5715,
						},
						Button: {
							colorPrimary: "#1890ff",
							colorPrimaryHover: "#40a9ff",
							colorPrimaryActive: "#096dd9",
							borderRadius: 2,
							fontSize: 14,
							lineHeight: 1.5715,
						},
						Dropdown: {
							colorBgElevated: isDarkTheme
								? "var(--popover)"
								: "white",
							colorText: isDarkTheme
								? "rgba(255, 255, 255, 0.85)"
								: "rgba(0, 0, 0, 0.85)",
							borderRadius: 2,
						},
					},
				}}
			>
				<Table
					dataSource={data}
					columns={columns}
					rowKey="id"
					loading={loading}
					onChange={handleChange}
					onRow={onRow}
					pagination={false}
					scroll={scroll}
					locale={customLocale}
					rowClassName={(_, index) =>
						cn(
							// 修改于2025年06月12日: 调整行背景颜色，使单数行高亮显示
							// 原代码: index % 2 === 1 ? (isDarkTheme ? "bg-gray-700/20" : "bg-slate-100/60") : "",
							index % 2 === 0
								? isDarkTheme
									? "bg-gray-700/20"
									: "bg-slate-100/60"
								: "",
							rowClassName,
						)
					}
					footer={footer ? () => footer : undefined}
					size="middle"
					bordered={false}
				/>
			</ConfigProvider>
		</div>
	);
}