// 定义会议数据类型
export interface Meeting {
	id: string;
	title: string;
	startTime: string;
	meetingId: string;
	status: string;
	duration?: number;
	host?: string;
	participants?: string[];
	description?: string;
	joinURL: string;
}

// 定义会议文档数据类型
export interface MeetingDoc {
	doc_id: string;
	doc_title: string;
	doc_creator_userid: string;
	doc_creator_user_name: string;
	doc_edit_time: number;
	doc_editor_userid: string;
	doc_editor_user_name: string;
	doc_modify_permission: number;
}

export interface MeetingDocsResponse {
	total_count: number;
	doc_info_list?: MeetingDoc[]; // API返回的字段名称
}

// 添加录制文件信息接口
export interface MeetingRecordDetail {
	record_file_id?: string;
	meeting_id?: string;
	meeting_code?: string;
	record_name?: string;
	start_time?: string;
	end_time?: string;
	meeting_record_name?: string;
	view_address?: string;
	download_address?: string;
	download_address_file_type?: string;
	// 摘要文件
	meeting_summary?: Array<{
		download_address: string;
		file_type: string;
	}>;
	// AI会议记录
	ai_meeting_transcripts?: Array<{
		download_address: string;
		file_type: string;
	}>;
	// AI会议纪要
	ai_minutes?: Array<{
		download_address: string;
		file_type: string;
	}>;
}


// 定义用户文档响应类型以匹配后端返回的数据结构
export interface UserDocsResponse {
	total_count: number;
	meeting_info_list: Array<{
		meeting_id: string;
		meeting_code: string;
		subject: string;
		start_time: string;
		doc_info_list: MeetingDoc[];
	}>;
	current_size: number;
	current_offset: number;
	total_page: number;
	page_size: number;
}

export interface MeetingDocsProps {
	organizationSlug: string;
}

export interface UncompletedMeetingTableProps {
		meetings: Meeting[];
		organizationSlug: string;
		sortDirection: "asc" | "desc";
		toggleSortDirection: () => void;
		isLoading: boolean;
		searchQuery: string;
		clearSearch: () => void;
		cancelingMeetingId: string | null;
		handleCancelMeeting: (meetingId: string) => void;
		handleGetMeetingDocs: (meetingId: string, meetingTitle: string) => void;
		handleGetMeetingRecord: (
			meetingId: string,
			meetingTitle: string,
		) => void;
		hasCheckedUserStatus?: boolean;
		isUserActive?: boolean;
		isActivating?: boolean;
		handleActivation?: () => void;
		showActivationDialog?: boolean;
		clearActivationCheck?: () => void;
}

export interface CompletedMeetingTableProps {
	meetings: Meeting[];
	organizationSlug: string;
	sortDirection: "asc" | "desc";
	toggleSortDirection: () => void;
	isLoading: boolean;
	searchQuery: string;
	clearSearch: () => void;
	cancelingMeetingId: string | null;
	handleGetMeetingDocs: (meetingId: string, meetingTitle: string) => void;
	handleGetMeetingRecord: (meetingId: string, meetingTitle: string) => void;
	startDate: string;
	endDate: string;
	handleStartDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	handleEndDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	clearDateFilter: () => void;
	hasCheckedUserStatus?: boolean;
	isUserActive?: boolean;
	isActivating?: boolean;
	handleActivation?: () => void;
	showActivationDialog?: boolean;
	clearActivationCheck?: () => void;
}

export interface MeetingTableProps {
		meetings: Meeting[];
		organizationSlug: string;
		sortDirection: "asc" | "desc";
		toggleSortDirection: () => void;
		isLoading: boolean;
		handleCancelMeeting?: (meetingId: string) => void;
		showCancelOption?: boolean;
		timeColumnLabel?: string;
		showDocAndRecordButtons?: boolean;
		hasCheckedUserStatus?: boolean;
		isUserActive?: boolean;
		isActivating?: boolean;
		handleActivation?: () => void;
		showActivationDialog?: boolean;
		clearActivationCheck?: () => void;
}

export interface DateFilterProps {
		startDate: string;
		endDate: string;
		handleStartDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
		handleEndDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
		clearDateFilter: () => void;
}