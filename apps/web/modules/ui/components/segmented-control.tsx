"use client";

import * as React from "react";
import * as ToggleGroupPrimitive from "@radix-ui/react-toggle-group";
import { cn } from "@ui/lib";
import { cva, type VariantProps } from "class-variance-authority";

/**
 * 分段式控制组件样式变体
 */
const segmentedControlVariants = cva(
  "inline-flex items-center justify-center rounded-lg border p-0.5 bg-background",
  {
    variants: {
      variant: {
        surface: "border-border",
        classic: "border-primary/20 bg-background",
      },
      size: {
        "1": "h-7 text-xs",
        "2": "h-8 text-sm",
        "3": "h-10 text-base",
      },
      radius: {
        none: "rounded-none",
        small: "rounded-sm",
        medium: "rounded-md",
        large: "rounded-lg",
        full: "rounded-full",
      },
      fullWidth: {
        true: "w-full",
      },
    },
    defaultVariants: {
      variant: "surface",
      size: "2",
      radius: "medium",
    },
  }
);

/**
 * 分段式控制项目样式变体
 */
const segmentedControlItemVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        surface: "data-[state=on]:bg-foreground data-[state=on]:text-background hover:bg-accent/50 data-[state=off]:text-foreground",
        classic: "data-[state=on]:bg-primary data-[state=on]:text-primary-foreground hover:bg-accent/50 data-[state=off]:text-foreground",
      },
      size: {
        "1": "px-2 py-0.5 text-xs",
        "2": "px-3 py-1 text-sm",
        "3": "px-4 py-1.5 text-base",
      },
      radius: {
        none: "rounded-none",
        small: "rounded-sm",
        medium: "rounded-md",
        large: "rounded-lg",
        full: "rounded-full",
      },
      fullWidth: {
        true: "flex-1",
      },
    },
    defaultVariants: {
      variant: "surface",
      size: "2",
      radius: "medium",
    },
  }
);

/**
 * 分段式控制根组件基础属性
 */
interface SegmentedControlRootBaseProps extends VariantProps<typeof segmentedControlVariants> {
  className?: string;
  disabled?: boolean;
  orientation?: "horizontal" | "vertical";
  dir?: "ltr" | "rtl";
  loop?: boolean;
  rovingFocus?: boolean;
  children?: React.ReactNode;
}

/**
 * 单选分段控制组件属性
 */
export interface SegmentedControlSingleProps extends SegmentedControlRootBaseProps {
  type?: "single";
  value?: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
}

/**
 * 多选分段控制组件属性
 */
export interface SegmentedControlMultipleProps extends SegmentedControlRootBaseProps {
  type: "multiple";
  value?: string[];
  defaultValue?: string[];
  onValueChange?: (value: string[]) => void;
}

/**
 * 分段式控制根组件属性（联合类型）
 */
export type SegmentedControlRootProps = SegmentedControlSingleProps | SegmentedControlMultipleProps;

/**
 * 单选分段控制根组件
 */
const SingleSegmentedControlRoot = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Root>,
  SegmentedControlSingleProps
>((props, ref) => {
  const { 
    className, 
    variant, 
    size, 
    radius, 
    fullWidth, 
    ...otherProps 
  } = props;

  return (
    <ToggleGroupPrimitive.Root
      type="single"
      className={cn(
        segmentedControlVariants({ 
          variant, 
          size, 
          radius,
          fullWidth
        }), 
        className
      )}
      {...otherProps}
      ref={ref}
    />
  );
});
SingleSegmentedControlRoot.displayName = "SingleSegmentedControlRoot";

/**
 * 多选分段控制根组件
 */
const MultipleSegmentedControlRoot = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Root>,
  SegmentedControlMultipleProps
>((props, ref) => {
  const { 
    className, 
    variant, 
    size, 
    radius, 
    fullWidth,
    type,
    ...otherProps 
  } = props;

  return (
    <ToggleGroupPrimitive.Root
      type="multiple"
      className={cn(
        segmentedControlVariants({ 
          variant, 
          size, 
          radius,
          fullWidth
        }), 
        className
      )}
      {...otherProps}
      ref={ref}
    />
  );
});
MultipleSegmentedControlRoot.displayName = "MultipleSegmentedControlRoot";

/**
 * 分段式控制根组件
 * 
 * @version 2.1.0 (2025-05-22) - 修复 MultipleSegmentedControlRoot 中的重复 type 属性问题
 * @version 2.0.0 (2025-05-22) - 完全重构组件，使用 Radix UI 的原始类型定义并创建两个独立的内部组件处理不同类型
 * @version 1.4.0 (2025-05-22) - 重构组件属性类型，分别处理单选和多选模式
 * @version 1.3.0 (2025-05-22) - 修复 ToggleGroupPrimitive.Root 的类型问题，使用条件渲染处理不同类型
 * @version 1.2.0 (2025-05-22) - 将已弃用的 ElementRef 替换为 ComponentRef，修复类型问题
 * @version 1.1.0 (2025-05-22) - 修复类型错误，移除type属性的硬编码值
 * @version 1.0.0 (2025-06-18) - 初始版本，基于Radix UI的Toggle Group原语
 */
const SegmentedControlRoot = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Root>,
  SegmentedControlRootProps
>((props, ref) => {
  if (props.type === "multiple") {
    return <MultipleSegmentedControlRoot {...props} ref={ref} />;
  }
  
  return <SingleSegmentedControlRoot {...props} ref={ref} />;
});
SegmentedControlRoot.displayName = "SegmentedControlRoot";

/**
 * 分段式控制项目属性
 */
export interface SegmentedControlItemProps
  extends React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item>,
    VariantProps<typeof segmentedControlItemVariants> {}

/**
 * 分段式控制项目组件
 * 
 * @version 1.1.0 (2023-06-24) - 将已弃用的 ElementRef 替换为 ComponentRef
 * @version 1.0.0 (2025-06-18) - 初始版本，基于Radix UI的Toggle Group Item原语
 */
const SegmentedControlItem = React.forwardRef<
  React.ComponentRef<typeof ToggleGroupPrimitive.Item>,
  SegmentedControlItemProps
>(
  (
    { 
      className, 
      variant, 
      size, 
      radius,
      fullWidth,
      children,
      ...props 
    }, 
    ref
  ) => {
    return (
      <ToggleGroupPrimitive.Item
        className={cn(
          segmentedControlItemVariants({ 
            variant, 
            size, 
            radius,
            fullWidth
          }), 
          className
        )}
        {...props}
        ref={ref}
      >
        {children}
      </ToggleGroupPrimitive.Item>
    );
  }
);
SegmentedControlItem.displayName = "SegmentedControlItem";

/**
 * 分段式控制组件导出
 */
export const SegmentedControl = {
  Root: SegmentedControlRoot,
  Item: SegmentedControlItem,
}; 