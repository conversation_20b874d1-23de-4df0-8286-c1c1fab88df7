"use client";

import * as PopoverPrimitive from "@radix-ui/react-popover";
import * as React from "react";

import { cn } from "@ui/lib";

const Popover = PopoverPrimitive.Root;

const PopoverTrigger = PopoverPrimitive.Trigger;

// 新增表头组件用于显示标题和操作按钮
interface PopoverHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
	title?: string;
	children?: React.ReactNode;
}

const PopoverHeader = React.forwardRef<
	HTMLDivElement,
	PopoverHeaderProps
>(({ className, title, children, ...props }, ref) => (
	<div 
		ref={ref}
		className={cn(
			"flex items-center justify-between p-2 border-b border-border",
			className
		)}
		{...props}
	>
		{title && <h4 className="font-medium text-sm">{title}</h4>}
		{children}
	</div>
));
PopoverHeader.displayName = "PopoverHeader";

// 列表容器组件，用于展示选项列表
const PopoverBody = React.forwardRef<
	HTMLDivElement,
	React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
	<div 
		ref={ref}
		className={cn(
			"p-2 max-h-[280px] overflow-y-auto",
			className
		)}
		{...props}
	/>
));
PopoverBody.displayName = "PopoverBody";

// 修改PopoverContent组件的样式以匹配表格筛选风格
const PopoverContent = React.forwardRef<
	React.ComponentRef<typeof PopoverPrimitive.Content>,
	React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>
>(({ className, align = "center", sideOffset = 4, ...props }, ref) => (
	<PopoverPrimitive.Portal>
		<PopoverPrimitive.Content
			ref={ref}
			align={align}
			sideOffset={sideOffset}
			className={cn(
				"z-50 w-56 rounded-md border bg-popover shadow-md outline-none p-0 text-popover-foreground",
				"data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
				"data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
				"data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
				"data-[state=closed]:animate-out data-[state=open]:animate-in",
				className
			)}
			{...props}
		/>
	</PopoverPrimitive.Portal>
));
PopoverContent.displayName = PopoverPrimitive.Content.displayName;

// 添加列表项组件，用于筛选选项
const PopoverItem = React.forwardRef<
	HTMLDivElement,
	React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
	<div 
		ref={ref}
		className={cn(
			"flex items-center space-x-2 py-1 hover:bg-muted/40 rounded px-1 cursor-pointer",
			className
		)}
		{...props}
	/>
));
PopoverItem.displayName = "PopoverItem";

export { 
	Popover, 
	PopoverTrigger, 
	PopoverContent,
	PopoverHeader,
	PopoverBody,
	PopoverItem 
}; 