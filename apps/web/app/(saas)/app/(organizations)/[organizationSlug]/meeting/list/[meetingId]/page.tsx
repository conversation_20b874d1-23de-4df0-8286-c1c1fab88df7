import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { MeetingDetail } from "@saas/meeting/components/MeetingDetail";
/**
 * 会议详情页面组件
 * @param params - 包含组织 slug、会议 ID 和会议名称的路由参数
 * @returns 渲染的页面组件
 */
export default async function MeetingDetailPage({
  params,
}: { params: Promise<{ organizationSlug: string; meetingId: string }> }) {
  // 获取路由参数
  const { organizationSlug, meetingId } = await params;
  
  // 从 meetingId 中提取会议 ID 和名称
  // 格式示例: "123-product-meeting" 其中 123 是 ID，"product-meeting" 是会议名称
  const meetingIdParts = meetingId.split('-');
  const actualMeetingId = meetingIdParts[0];
  const meetingName = meetingIdParts.length > 1 ? meetingIdParts.slice(1).join('-') : '';

  // 获取当前激活的组织信息
  const activeOrganization = await getActiveOrganization(
    organizationSlug as string,
  );

  // 如果未找到组织则返回 404
  if (!activeOrganization) {
    return notFound();
  }

  return (
    // 展现对应前端MeetingDetail组件
    <div className="border rounded-lg p-6 shadow-sm">
      <MeetingDetail organizationSlug={organizationSlug} meetingId={actualMeetingId} />
    </div>
  );
} 