import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { ChangeMeetingForm } from "@saas/meeting/components/ChangeMeetingForm";     

/**
 * 预定会议页面组件
 * @param params - 包含组织 slug 的路由参数
 * @returns 渲染的页面组件
 */
export default async function MeetingSchedulePage({
	params,
}: { params: Promise<{ organizationSlug: string, meetingId: string }> }) {
	// 获取路由参数中的组织 slug
	const { organizationSlug, meetingId } = await params;

	// 获取当前激活的组织信息
	const activeOrganization = await getActiveOrganization(
		organizationSlug as string,
	);

	// 如果未找到组织则返回 404
	if (!activeOrganization) {
		return notFound();
	}

	return (
		// 展现对应前端MeetingForm组件
		<div className="border rounded-lg p-6 shadow-sm">
			<ChangeMeetingForm organizationSlug={organizationSlug} meetingId={meetingId} />
		</div>
	);
} 