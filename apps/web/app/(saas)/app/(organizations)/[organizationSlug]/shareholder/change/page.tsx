// 导入必要的依赖
import { getSession } from "@saas/auth/lib/server"; // 用于获取用户会话信息
import { SettingsList } from "@saas/shared/components/SettingsList"; // 设置列表组件，用于布局
import { redirect } from "next/navigation"; // 页面重定向功能
import { ShareholderChangeComposite } from "@saas/shareholder/components"; // 导入股东持股变化组合组件

// 定义参数类型
type ShareholderParams = {
	organizationSlug: string;
};

/**
 * 股东持股变化分析页面组件
 * 这是一个服务器组件，用于显示股东持股变化分析功能的页面
 * 
 * <AUTHOR>
 * @created 2025-06-10 19:50:19.948
 */
export default async function ShareholderChangePage({ params }: { params: ShareholderParams }) {
	// 获取用户会话信息，用于验证用户是否已登录
	const session = await getSession();

	if (!session) {
		// 如果用户未登录，重定向到登录页面
		return redirect("/auth/login");
	}

	// 由于 Next.js 路由系统要求 params 需要被 await，我们使用 Promise.resolve
	const { organizationSlug } = await Promise.resolve(params);
	// 渲染页面内容
	return (
		<SettingsList>
			<ShareholderChangeComposite organizationSlug={organizationSlug} />
		</SettingsList>
	);
} 