{"dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/auth": "workspace:*", "@repo/utils": "workspace:*"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@sindresorhus/slugify": "^2.2.1", "@types/node": "^22.13.13", "nanoid": "^5.1.2", "tsx": "^4.19.3"}, "name": "@repo/scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "create:organization": "dotenv -c -e ../../.env -- tsx ./src/create-organization.ts", "add:member": "dotenv -c -e ../../.env -- tsx ./src/add-member.ts", "delete:user": "dotenv -c -e ../../.env -- tsx ./src/delete-user.ts", "delete:organization": "dotenv -c -e ../../.env -- tsx ./src/delete-organization.ts", "reset:password": "dotenv -c -e ../../.env -- tsx ./src/reset-password.ts", "update:member-role": "dotenv -c -e ../../.env -- tsx ./src/update-member-role.ts", "remove:member": "dotenv -c -e ../../.env -- tsx ./src/remove-member.ts", "type-check": "tsc --noEmit"}, "version": "0.0.0"}