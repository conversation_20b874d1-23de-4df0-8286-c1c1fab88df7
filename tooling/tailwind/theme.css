@theme {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-success: var(--success);
	--color-success-foreground: var(--success-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-highlight: var(--highlight);
	--color-highlight-foreground: var(--highlight-foreground);
	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);
 
	--font-sans: var(--font-geist-sans);

	--shadow-xs: 0 2px 8px 0 rgb(0, 0, 0, 0.025), 0 0 1px rgba(0, 0, 0, 0.1);
	--shadow-sm: 0 4px 16px 0 rgb(0, 0, 0, 0.05), 0 0 1px rgba(0, 0, 0, 0.1);
	--shadow-md: 0 6px 24px 0 rgb(0, 0, 0, 0.075), 0 0 1px rgba(0, 0, 0, 0.1);
	--shadow-lg: 0 8px 32px 0 rgb(0, 0, 0, 0.1), 0 0 1px rgba(0, 0, 0, 0.1);
	--shadow-xl: 0 12px 48px 0 rgb(0, 0, 0, 0.125), 0 0 1px rgba(0, 0, 0, 0.1);
	--shadow-2xl: 0 16px 64px 0 rgb(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.1);

	--animation-accordion-down: accordion-down 0.2s ease-out;
	--animation-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@layer base {
	:root {
		--border: #e7e7e8;
		--input: #d3d4d5;
		--ring: #3399ff;
		--background: #ffffff;
		--foreground: #171a1d;
		--primary: #3399ff;
		--primary-foreground: #ffffff;
		--secondary: #171a1d;
		--secondary-foreground: #ffffff;
		--destructive: #dc4a41;
		--destructive-foreground: #ffffff;
		--success: #0ea371;
		--success-foreground: #ffffff;
		--muted: #f9fafa;
		--muted-foreground: #7d7f83;
		--accent: #f1f1f2;
		--accent-foreground: #1d2025;
		--popover: #ffffff;
		--popover-foreground: #171a1d;
		--card: #ffffff;
		--card-foreground: #171a1d;
		--highlight: #fbc434;
		--highlight-foreground: #ffffff;
		--radius: 0.75rem;

		/* fumadocs */
		--fd-banner-height: 4.5rem;
	}

	.dark {
		--border: rgba(255, 255, 255, 0.08);
		--input: #52555a;
		--ring: #3399ff;
		--background: #171a1d;
		--foreground: #f8fafc;
		--primary: #3399ff;
		--primary-foreground: #1d2025;
		--secondary: #f8fafc;
		--secondary-foreground: #1d2025;
		--destructive: #dc4a41;
		--destructive-foreground: #ffffff;
		--success: #0ea371;
		--success-foreground: #ffffff;
		--muted: #33373d;
		--muted-foreground: #abadaf;
		--accent: #1d2025;
		--accent-foreground: #f8fafc;
		--popover: #171a1d;
		--popover-foreground: #f8fafc;
		--card: #171a1d;
		--card-foreground: #f8fafc;
		--highlight: #fbc434;
		--highlight-foreground: #ffffff;
	}

	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		@apply border-border;
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 1.5rem;
	width: 100%;
	max-width: var(--container-7xl);
}

@utility no-scrollbar {
	&::-webkit-scrollbar {
		display: none;
	}
	-ms-overflow-style: none;
	scrollbar-width: none;
}
