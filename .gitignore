# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build
.swc/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# ui
dist/

# typescript
*.tsbuildinfo

# other 
.react-email/
.content-collections/

<<<<<<< HEAD

股东管理功能-后端开发最佳实践文档.md
股东管理功能实施方案.md
API_DOCUMENTATION.md
database_schema.sql
=======
# sql
database_schema.sql

# md
股东管理功能-后端开发最佳实践文档.md
股东管理功能实施方案.md
API_DOCUMENTATION.md
>>>>>>> origin/feature/appUi
DATABASE.md

# mcp
mcp.json
