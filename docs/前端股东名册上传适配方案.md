# 前端股东名册上传适配方案

**文档日期:** 2025-05-29
**版本:** 1.1

## 1. 需求概述

### 1.1 背景

当前系统已支持深市01和05名册的上传和合并，现需扩展支持沪市t1、t2和t3名册的上传和合并。后端API将进行重构，改变数据结构，按照01、05、t1、t2、t3名册字段进行区分上传，不再共用一个数据结构。前端需要适配这些变更，确保文件解析和数据上传功能正常工作。

### 1.2 目标

1. 扩展前端解析功能，支持沪市t1/t2/t3名册的解析和预处理
2. 适配后端新的上传数据结构
3. 实现t1/t2/t3名册的预处理和字段映射
4. 保持对01/05名册的支持
5. 优化用户体验，提供更清晰的错误提示和导入流程

## 2. 文件结构及责任划分

### 2.1 当前模块结构

```
apps/web/modules/saas/shareholder/
├── components/                     # UI组件
│   ├── ShareholderRegistryImport.tsx  # 股东名册导入主组件
│   ├── ShareholderRegistryImportProgress.tsx # 导入进度展示组件
│   └── dialogs/                    # 对话框组件
├── hooks/                          # 自定义Hook
│   └── useShareholders.ts          # 股东相关操作Hook
├── lib/                            # 工具函数和逻辑处理
│   ├── dbf-parser.ts               # DBF解析器入口
│   ├── dbf-parser-common.ts        # 共享解析工具
│   ├── dbf-parser-01.ts            # 01名册解析器
│   ├── dbf-parser-05.ts            # 05名册解析器
│   ├── file-parser.ts              # 通用文件解析
│   └── registry-api.ts             # API调用
```

### 2.2 需要新增的文件结构

```
apps/web/modules/saas/shareholder/
├── lib/
│   ├── dbf-parser/                 # DBF解析器目录
│   │   ├── index.ts                # 入口文件
│   │   ├── common.ts               # 共享工具
│   │   ├── type-01.ts              # 01名册解析器
│   │   └── type-05.ts              # 05名册解析器
│   ├── excel-parser/               # Excel解析器目录
│   │   ├── index.ts                # 入口文件
│   │   ├── common.ts               # 共享工具
│   │   ├── type-t1.ts              # t1名册解析器
│   │   ├── type-t2.ts              # t2名册解析器
│   │   └── type-t3.ts              # t3名册解析器
│   ├── config.ts                   # 配置文件（包含MAX_RECORDS_COUNT等常量）
│   └── registry-merger.ts          # 名册合并预处理工具
```

## 3. 实施策略

### 3.1 解析器扩展

1. **新增t1/t2/t3名册解析器**
   - 参照01/05解析器模式，创建对应的t1/t2/t3解析器
   - 针对每种名册类型，定义字段映射规则
   - 实现基于Excel格式的解析逻辑(t1/t2/t3为后缀名.xls的Excel文件)
   - 使用iconv-lite库处理中文编码问题，防止出现乱码
   - 实现字符串处理函数，去除多余空格和特殊字符

2. **通用解析器升级**
   - 在`file-parser.ts`中添加对t1/t2/t3名册类型的检测逻辑
   - 扩展`detectRegistryType`函数，支持识别沪市名册类型
   - 升级文件名解析函数，支持沪市名册文件命名规则

3. **Excel解析增强**
   - 增强Excel文件解析能力，支持结构化提取t1/t2/t3数据
   - 添加对特定格式和表头的识别
   - 实现对Excel中特殊行(表头、汇总行等)的提取
   - 处理不同Excel文件中的编码问题，统一使用iconv-lite进行转码
   - 对导出的Excel数据进行格式化处理，包括去除空格、特殊字符等

4. **数据筛选处理**
   - 使用环境变量-->全局配置文件中定义`MAX_RECORDS_COUNT = 200`变量
   - 根据sharesInCashAccount(持股数量)最大，解析出的股东记录进行排序
   - 仅保留排序后的前`MAX_RECORDS_COUNT`条记录
   - 提供配置选项，允许调整此限制

### 3.2 预处理实现

为t1/t2/t3名册添加预处理逻辑，主要在`registry-merger.ts`中实现：

1. **数据叠加规则**
   - 基于ZJHM(证件号码)和YMTZHHM(一码通账户号码)作为组合键进行记录匹配
   - 对匹配记录实现以下字段的叠加处理：
     - 普通持股数量 (t1CYSL, t2:CYSL; t3: PTZQZHCYSL；数据库:sharesInCashAccount)
     - 信用持股数量 (t3: XYCYSL; 数据库:sharesInMarginAccount)
     - 总持股数量 (t3: ZCYSL; 数据库:numberOfShares)
   - 对非叠加字段采用"先到先得"原则，即保留首次出现的字段值


### 3.3 数据适配

创建名册类型与API数据结构的映射逻辑：

1. **深市01/05名册**
   - 保持现有处理逻辑不变
   - 确保与新的API接口兼容

2. **沪市t1/t2/t3名册**
   - 创建新的数据结构映射
   - 添加t1特有字段：shareTradingCategory(流通类型)、rightsCategory(权益类别)
   - 实现字段名称的标准化转换

3. **自动类型检测**
   - 根据文件命名和内容特征，自动识别名册类型
   - 针对不同类型的名册应用对应的解析和映射规则

## 4. 用户界面调整

### 4.1 上传组件增强

1. **文件类型识别**
   - 扩展现有的文件类型识别，增加对沪市名册格式的支持
   - 提供更详细的文件类型提示，区分深市/沪市名册

2. **上传流程调整**
   - 针对t1/t2/t3名册，增加预处理步骤
   - 在上传前合并后，进行点击导入按钮上传入库

3. **进度展示优化**
   - 细化进度展示，包括解析、预处理、上传等阶段
   - 提供更详细的状态反馈

### 4.2 错误处理增强

1. **错误提示增强**
   - 针对t1/t2/t3名册添加特定的错误检测和提示
   - 提供更详细的字段验证和数据格式检查

2. **导入失败处理**
   - 针对不同类型的失败原因提供不同的处理建议
   - 支持部分文件跳过继续上传的功能

## 5. 字段映射关系

### 5.1 名册字段映射综合对照表

| 数据库字段 | 01名册 | 05名册 | t1名册 | t2名册 | t3名册 | 说明 |
|-----------|-------|-------|-------|-------|-------|------|
| unifiedAccountNumber | YMTH | YMTH | YMTZHHM | YMTZHHM | YMTZHHM | 一码通账户号码(所有名册共有) |
| securitiesAccountName | ZQZHMC | XYZHMC | CYRMC | CYRMC | CYRMC | 证券账户名称(所有名册共有) |
| shareholderId | ZJDM | XYZHZJDM | ZJHM | ZJHM | ZJHM | 证件号码(所有名册共有) |
| shareholderCategory | CYRLBMS | CYRLBMS | GDLB | GDLB | GDLB | 持有人类别/股东类别(所有名册共有) |
| numberOfShares | CGSL | - | - | - | ZCYSL | 总持股数量(01名册和t3名册有) |
| sharesInCashAccount | PTZHCGSL | - | CYSL | CYSL | PTZQZHCYSL | 普通持股数量(除05名册外都有) |
| sharesInMarginAccount | XYZHCGSL | CGSL | - | - | XYCYSL | 信用持股数量(05名册和t3名册有) |
| lockedUpShares | XSGSL | - | - | - | - | 限售股数量(仅01名册有) |
| shareholdingRatio | CGBL | - | - | - | - | 持股比例(仅01名册有) |
| frozenShares | DJGS | DJGS | - | - | - | 冻结股数(01和05名册有) |
| contactAddress | TXDZ | TXDZ | TXDZ | TXDZ | TXDZ | 通讯地址(所有名册共有) |
| contactNumber | DHHM | - | LXDH | LXDH | LXDH | 联系电话(除05名册外都有) |
| zipCode | YZBM | - | YZBM | YZBM | YZBM | 邮政编码(除05名册外都有) |
| marginCollateralAccountNumber | - | HZZQZH | - | - | - | 汇总账户号码(仅05名册有) |
| marginCollateralAccountName | - | HZZHMC | - | - | - | 汇总账户名称(仅05名册有) |
| natureOfShares | - | GFXZ | - | - | - | 股份性质(仅05名册有) |
| relatedPartyIndicator | GLGXBS | - | - | - | - | 关联关系确认标识(仅01名册有) |
| clientCategory | KHLB | - | - | - | - | 客户类别(仅01名册有) |
| cashAccount | PTZQZH | - | - | - | PTZQZH | 普通证券账户(01和t3名册有) |
| marginAccount | XYZQZH | XYZQZH | - | - | XYZQZH | 信用证券账户(01/05/t3名册有) |
| remarks | BZ | - | BZ | BZ | BZ | 备注(除05名册外都有) |
| shareTradingCategory | - | - | LTLX | - | - | 流通类型(仅t1名册有) |
| rightsCategory | - | - | YQLB | - | - | 权益类别(仅t1名册有) |

### 5.2 01名册字段映射详情

| 原始字段  | 数据库字段             | 说明               |
|----------|------------------------|-------------------|
| YMTH     | unifiedAccountNumber   | 一码通账户号码      |
| ZQZHMC   | securitiesAccountName  | 证券账户名称        |
| ZJDM     | shareholderId          | 证件号码           |
| CYRLBMS  | shareholderCategory    | 持有人类别          |
| CGSL     | numberOfShares         | 总持股数量          |
| XSGSL    | lockedUpShares         | 限售股数量          |
| CGBL     | shareholdingRatio      | 持股比例            |
| DJGS     | frozenShares           | 冻结股数           |
| PTZQZH   | cashAccount            | 普通证券账户        |
| PTZHCGSL | sharesInCashAccount    | 普通账户持股数量     |
| XYZQZH   | marginAccount          | 信用证券账户        |
| XYZHCGSL | sharesInMarginAccount  | 信用账户持股数量     |
| TXDZ     | contactAddress         | 通讯地址           |
| DHHM     | contactNumber          | 电话号码           |
| YZBM     | zipCode                | 邮政编码           |
| GLGXBS   | relatedPartyIndicator  | 关联关系确认标识    |
| KHLB     | clientCategory         | 客户类别           |
| BZ       | remarks                | 备注               |

### 5.3 05名册字段映射详情

| 原始字段    | 数据库字段                  | 说明               |
|------------|----------------------------|-------------------|
| YMTH       | unifiedAccountNumber       | 一码通账户号码      |
| XYZHMC     | securitiesAccountName      | 信用证券账户名称    |
| XYZHZJDM   | shareholderId              | 信用证券账户证件号码 |
| CYRLBMS    | shareholderCategory        | 持有人类别          |
| CGSL       | sharesInMarginAccount      | 信用账户持股数量     |
| HZZQZH     | marginCollateralAccountNumber | 汇总账户号码     |
| HZZHMC     | marginCollateralAccountName   | 汇总账户名称     |
| GFXZ       | natureOfShares             | 股份性质           |
| TXDZ       | contactAddress             | 通讯地址           |
| XYZQZH     | marginAccount              | 信用证券账户        |
| DJGS       | frozenShares               | 冻结股数           |

### 5.4 t1名册字段映射详情

| 原始字段        | 数据库字段                | 说明               |
|----------------|--------------------------|-------------------|
| YMTZHHM        | unifiedAccountNumber     | 一码通账户号码      |
| ZQZHMC         | securitiesAccountName    | 证券账户名称        |
| ZJHM           | shareholderId            | 证件号码           |
| GDLB           | shareholderCategory      | 股东类别           |
| CYSL           | sharesInCashAccount      | 持有数量(普通持股)   |
| LTLX           | shareTradingCategory     | 流通类型(t1特有)    |
| YQLB           | rightsCategory           | 权益类别(t1特有)    |
| TXDZ           | contactAddress           | 通讯地址           |
| LXDH           | contactNumber            | 联系电话           |
| YZBM           | zipCode                  | 邮政编码           |
| BZ             | remarks                  | 备注               |


### 5.5 t2名册字段映射详情

| 原始字段        | 数据库字段                | 说明               |
|----------------|--------------------------|-------------------|
| YMTZHHM        | unifiedAccountNumber     | 一码通账户号码      |
| ZQZHMC         | securitiesAccountName    | 证券账户名称        |
| ZJHM           | shareholderId            | 证件号码           |
| GDLB           | shareholderCategory      | 股东类别           |
| CYSL           | sharesInCashAccount      | 持有数量(普通持股)   |
| TXDZ           | contactAddress           | 通讯地址           |
| LXDH           | contactNumber            | 联系电话           |
| YZBM           | zipCode                  | 邮政编码           |
| BZ             | remarks                  | 备注               |


### 5.6 t3名册字段映射详情

| 原始字段        | 数据库字段                | 说明               |
|----------------|--------------------------|-------------------|
| YMTZHHM        | unifiedAccountNumber     | 一码通账户号码      |
| ZQZHMC         | securitiesAccountName    | 证券账户名称        |
| ZJHM           | shareholderId            | 证件号码           |
| GDLB           | shareholderCategory      | 股东类别           |
| PTZQZHCYSL     | sharesInCashAccount      | 普通持股数量        |
| XYCYSL         | sharesInMarginAccount    | 信用持股数量        |
| ZCYSL          | numberOfShares           | 总持股数量          |
| TXDZ           | contactAddress           | 通讯地址           |
| LXDH           | contactNumber            | 联系电话           |
| YZBM           | zipCode                  | 邮政编码           |
| BZ             | remarks                  | 备注               |
| PTZQZH         | cashAccount              | 普通证券账户        |
| XYZQZH         | marginAccount            | 信用证券账户        |



### 6. 预处理流程

1. **初始解析**
   - 解析上传的t1/t2/t3名册文件，提取原始数据
   - 使用iconv-lite库处理中文编码，防止出现乱码
   - 对字段值进行规范化处理，包括去除空格、特殊字符等

2. **数据排序与筛选**
   - 按持股数量对解析出的记录进行降序排序
   - 仅保留前`MAX_RECORDS_COUNT`条记录(默认为200)
   - 对筛选后的数据进行后续处理

3. **记录重复的人分组合并一条**
  - 基于组合键对所有记录进行分组，把重复股东的数据集合到一组，然后进行合并到一条（规则如预处理），注意多个重复组情况（多个股东，多条重复）
  - 识别需要合并的记录组

4. **数据合并**
   - 对每组记录应用叠加规则
   - 对非叠加字段应用"先到先得"原则
   - 生成合并后的记录集

6. **提交上传**
   - 将预处理后的数据适配为API所需格式
   - 调用上传API提交数据

## 7. 数据校验逻辑

为确保上传的名册数据符合系统要求，需要对t1/t2/t3名册进行严格的数据校验，避免无效或错误的数据进入系统。校验逻辑将在Excel解析器中实现，主要分为文件名格式校验、公司代码校验和数据内容校验三个层面。

### 7.1 T1/T2/T3名册数据校验规则

## 1. T1名册校验规则

### 1.1 文件名格式检查
- 格式要求: `t1XXXXXXxxyyyymmddall.mdd.xls`
  - XXXXXX: 证券代码
  - xx: 股份性质
  - yyyymmdd: 权益登记日
  - all: 表示发送对象为全体证券持有人
  - mdd: 文件发送日期
    - m: 月份(1-9,a=10,b=11,c=12)
    - dd: 日期(01-31)
- 错误提示: "文件命名错误"

### 1.2 公司代码检查
- 无上传历史时:
  - 使用第一份名册中的公司代码(ZQDM字段)作为默认值
- 有上传历史时:
  - ZQDM必须与已有公司代码一致
- 错误提示: "文件中公司代码与当前公司代码不一致"

### 1.3 数据内容检查
1. 权益登记日校验
   - 检查QYDJR字段是否为空
   - 错误提示: "名册中未检测到期数"

2. 必填字段检查
   - 必须包含以下字段:
     - CYRMC (持有人名称)
     - CYRLB (持有人类别)
     - YMTZHHM (一码通账户号码)
     - ZJHM (证件号码)
   - 错误提示: "名册中缺少字段'XX'"

3. 字段内容检查
   - 以上字段均不可为空
   - 错误提示: "名册中字段'XX'缺少内容"

## 2. T2名册校验规则

### 2.1 文件名格式检查
- 格式要求: `t2XXXXXXxxyyyymmddzz.mdd`
  - XXXXXX: 证券代码
  - xx: 股份性质
  - yyyymmdd: 权益登记日
  - zz: 表示前N名(如t100表示前100名)
  - mdd: 文件发送日期
    - m: 月份(1-9,a=10,b=11,c=12)
    - dd: 日期(01-31)
- 错误提示: "文件命名错误"

### 2.2 公司代码检查
- 规则同T1名册
- 错误提示: "文件中公司代码与当前公司代码不一致"

### 2.3 数据内容检查
- 字段校验规则同T1名册

## 3. T3名册校验规则

### 3.1 文件名格式检查
- 格式要求: `t3XXXXXXxxyyyymmddzz.mdd`
  - XXXXXX: 证券代码
  - xx: 股份性质
  - yyyymmdd: 权益登记日
  - zz: 
    - "all": 全体股东名册
    - "tN": 前N名股东名册(如t100)
  - mdd: 文件发送日期
    - m: 月份(1-9,a=10,b=11,c=12)
    - dd: 日期(01-31)
- 错误提示: "文件命名错误"

### 3.2 公司代码检查
- 规则同T1名册
- 错误提示: "文件中公司代码与当前公司代码不一致"

### 3.3 数据内容检查
- 字段校验规则同T1名册

