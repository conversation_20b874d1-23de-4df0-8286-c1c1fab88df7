# 股东名册API设计文档

## 1. 概述

本文档描述了股东名册系统的API设计，遵循项目现有的API结构和规范。所有接口均不使用GET请求方式，以便于后续实现请求参数加密和返回数据加密，主要采用POST请求。

API路由基础路径：`/api/shareholder-registry`

### 1.1 主要功能

股东名册系统提供以下主要功能：

1. **股东名册上传**：支持上传解析后的股东名册数据，包含公司基本信息和股东详细信息。
2. **股东名册列表查询**：获取已上传的股东名册列表，支持分页和公司代码筛选。
3. **控股股东分析**：自动识别和展示持股比例最大的股东信息，帮助快速掌握公司控股结构。
4. **前十大股东汇总**：自动汇总前十大股东的持股总量和比例总和，便于分析股权集中度。
5. **股东名册期数日期查询**：获取可用的名册期数日期，用于历史数据对比分析。
6. **股东列表查询**：查询特定名册下的股东信息，支持高级搜索和排序功能。
7. **名册删除**：支持删除不需要的股东名册记录。
8. **名册批量删除**：支持一次性删除多个股东名册记录，提高操作效率。

### 1.2 数据处理原则

在API设计中，我们遵循以下数据处理原则：

1. 严格的数据验证：所有请求参数都经过严格的格式和业务规则验证。
2. 一致的错误处理：提供统一的错误码和描述格式。
3. 高效的数据查询：优化数据库查询性能，特别是对大量股东数据的统计分析。
4. 数据安全加密：支持请求和响应数据的加密处理。
5. 批量操作优化：对批量操作进行特殊优化，确保高效处理大量数据。

## 2. 统一状态码规范

| 状态码 | 描述 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求成功处理并返回 |
| 400 | 请求错误 | 参数验证失败或请求格式错误 |
| 401 | 未授权 | 用户未登录或会话已过期 |
| 403 | 禁止访问 | 用户无权访问请求的资源 |
| 404 | 资源不存在 | 请求的资源未找到 |
| 409 | 资源冲突 | 重复导入相同的股东名册 |
| 422 | 处理错误 | 服务器理解请求但无法处理 |
| 500 | 服务器错误 | 服务器内部错误 |

股东名册：请求头需统一携带
暂时无法在飞书文档外展示此内容

## 3. 接口定义

### 3.1 股东名册上传接口

前端解析DBF文件后，将解析结果提交到后端进行入库。该接口会检查组织与公司代码的绑定关系，确保一个组织只能绑定一个公司的股东数据。

```
POST /api/shareholder-registry/upload
```


#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| fileName | string | 是 | 原始DBF文件名 |
| recordCount | number | 是 | 记录数量 |
| registerDate | string | 是 | 报告日期（YYYY-MM-DD格式） |
| companyCode | string | 是 | 公司代码 |
| companyInfo | object | 是 | 公司基本信息 |
| shareholders | array | 是 | 股东信息数组 |

##### companyInfo 结构

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| companyName | string | 是 | 公司名称 |
| totalShares | string | 是 | 总股数 |
| totalShareholders | number | 是 | 总户数 |
| totalInstitutions | number | 是 | 机构总数 |
| largeSharesCount | string | 是 | 持有万份以上总份数 |
| largeShareholdersCount | number | 是 | 持有万份以上总户数 |

##### shareholders 数组元素结构

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| shareholderId | string | 是 | 证件号码 |
| unifiedAccountNumber | string | 是 | 一码通账户号码 |
| securitiesAccountName | string | 是 | 证券账户名称 |
| shareholderCategory | string | 是 | 持有人类别 |
| numberOfShares | string | 是 | 持股数量 |
| lockedUpShares | string | 是 | 限售股数量 |
| shareholdingRatio | string | 是 | 持股比例 |
| frozenShares | string | 是 | 冻结股数 |
| cashAccount | string | 否 | 普通证券账户 |
| sharesInCashAccount | string | 否 | 普通账户持股数量 |
| marginAccount | string | 否 | 信用证券账户 |
| sharesInMarginAccount | string | 否 | 信用账户持股数量 |
| contactAddress | string | 否 | 通讯地址 |
| contactNumber | string | 否 | 电话号码 |
| zipCode | string | 否 | 邮政编码 |
| relatedPartyIndicator | string | 否 | 关联关系确认标识 |
| clientCategory | string | 否 | 客户类别 |
| remarks | string | 否 | 备注 |

#### 业务逻辑说明

1. 首次上传股东名册时，系统会将组织与该公司代码绑定，记录在组织的metadata字段中
2. 后续上传只允许上传绑定的公司代码对应的股东名册
3. 一个组织只能绑定一个公司代码，不能上传其他公司的股东数据

#### 响应

##### 成功响应 - 200

```json
{
  "code": 200,
  "message": "股东名册上传成功",
  "data": {
    "id": "cuid123456",
    "fileName": "example.dbf",
    "recordCount": 1000,
    "registerDate": "2023-12-31",
    "uploadedAt": "2024-05-15T08:30:00.000Z"
  }
}
```

##### 错误响应 - 400 (公司代码不匹配)

```json
{
  "code": 400,
  "error": {
    "code": "COMPANY_CODE_MISMATCH",
    "message": "该组织已绑定公司代码 601288，不能上传其他公司的股东数据"
  },
  "data": null
}
```

##### 错误响应 - 409 (资源冲突)

```json
{
  "code": 409,
  "error": {
    "code": "DUPLICATE_REGISTRY",
    "message": "该组织下已存在相同报告日期的同一公司股东名册"
  },
  "data": null
}
```

### 3.2 股东名册列表接口

获取组织下的股东名册列表，支持分页和过滤。

```
POST /api/shareholder-registry/list
```


#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| page | number | 否 | 页码，默认为1 |
| limit | number | 否 | 每页条数，默认为10 |
| companyCode | string | 否 | 按公司代码筛选 |


#### 响应

##### 成功响应 - 200

```json
{
  "code": 200,
  "message": "获取股东名册列表成功",
  "data": {
    "registries": [
      {
        "id": "cuid123456",
        "fileName": "example.dbf",
        "recordCount": 1000,
        "registerDate": "2023-12-31",
        "companyCode": "601288",
        "companyName": "农业银行",
        "uploadedAt": "2024-05-15T08:30:00.000Z",
        "userName": "张三",
        "companyDetail": {
          "companyName": "农业银行",
          "totalShares": "********.00",
          "totalShareholders": 1000,
          "totalInstitutions": 50,
          "largeSharesCount": "5000000.00",
          "institutionShares": "3000000.00",
          "largeShareholdersCount": 20,
          "controllingShareholderInfo": {
            "securitiesAccountName": "中央汇金投资有限责任公司",
            "shareholdingRatio": "40.03",
            "numberOfShares": "**********.00"
          },
          "topTenShareholdersInfo": {
            "totalRatio": "85.27",
            "totalShares": "**********.00"
          }
        }
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "totalPages": 5
    }
  }
}
```

##### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | string | 股东名册ID |
| fileName | string | 文件名称 |
| recordCount | number | 记录数量 |
| registerDate | string | 名册日期(YYYY-MM-DD格式) |
| companyCode | string | 公司代码 |
| companyName | string | 公司名称 |
| uploadedAt | string | 上传时间(ISO格式) |
| userName | string | 上传用户名称 |
| companyDetail | object | 公司详细信息(可选) |

##### companyDetail 字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| companyName | string | 公司名称 |
| totalShares | string | 总股数 |
| totalShareholders | number | 总户数 |
| totalInstitutions | number | 机构总数 |
| largeSharesCount | string | 持有万份以上总份数 |
| institutionShares | string | 总机构股数 |
| largeShareholdersCount | number | 持有万份以上总户数 |
| controllingShareholderInfo | object | 控股股东信息(可选) |
| topTenShareholdersInfo | object | 前十大股东持股汇总信息(可选) |

##### controllingShareholderInfo 字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| securitiesAccountName | string | 证券账户名称 |
| shareholdingRatio | string | 持股比例 |
| numberOfShares | string | 持股数量 |

##### topTenShareholdersInfo 字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| totalRatio | string | 前十大股东持股比例总和 |
| totalShares | string | 前十大股东持股数量总和 |

### 3.3 查询期数日期接口

获取组织下所有可用的股东名册期数日期（registerDate），用于前端选择特定期数的数据。

```
POST /api/shareholder-registry/report-dates
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| organizationId | string | 是 | 组织ID |
| companyCode | string | 否 | 按公司代码筛选 |

#### 响应

##### 成功响应 - 200

```json
{
  "code": 200,
  "message": "获取期数日期列表成功",
  "data": {
    "registerDates": [
      {
        "registerDate": "2023-12-31",
        "companyCode": "601288"
      },
      {
        "registerDate": "2023-09-30",
        "companyCode": "601288"
      }
    ]
  }
}
```

### 3.4 股东列表接口

获取特定股东名册下的股东信息，支持分页和过滤。

```
POST /api/shareholder-registry/shareholders
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| registerDate | string | 是 | 报告期日期（YYYY-MM-DD格式，需配合organizationId） |
| organizationId | string | 是 | 组织ID（当使用registerDate时必填） |
| page | number | 否 | 页码，默认为1 |
| limit | number | 否 | 每页条数，默认为10 |
| searchTerm | string | 否 | 搜索关键词（默认检索所有字段：证券账户名称、证件号码、一码通账户等） |
| sortBy | string | 否 | 排序字段，默认为numberOfShares |
| sortOrder | string | 否 | 排序方向，asc或desc，默认为desc |

#### 响应

##### 成功响应 - 200

```json
{
  "code": 200,
  "message": "获取股东列表成功",
  "data": {
    "shareholders": [
      {
        "id": "cuid789012",
        "shareholderId": "110101199001011234",
        "unifiedAccountNumber": "A123456789",
        "securitiesAccountName": "张三",
        "shareholderCategory": "个人",
        "numberOfShares": "10000.00",
        "lockedUpShares": "0.00",
        "shareholdingRatio": "0.01",
        "frozenShares": "0.00",
        "contactAddress": "北京市朝阳区",
        "contactNumber": "***********",
        "rank": 1
      }
    ],
    "pagination": {
      "total": 1000,
      "page": 1,
      "limit": 10,
      "totalPages": 100
    }
  }
}
```

### 3.5 股东名册删除接口

删除特定的股东名册记录。

```
POST /api/shareholder-registry/delete
```

#### 请求头

| 名称 | 必填 | 描述 |
|------|------|------|
| Authorization | 是 | Bearer {token} |

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| registryId | string | 是 | 股东名册ID |

#### 响应

##### 成功响应 - 200

```json
{
  "code": 200,
  "message": "股东名册已成功删除",
  "data": null
}
```

##### 错误响应 - 404 (资源不存在)

```json
{
  "code": 404,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "未找到指定的股东名册记录"
  },
  "data": null
}
```

### 3.6 股东名册批量删除接口

批量删除多个股东名册记录，提高操作效率。

```
POST /api/shareholder-registry/batch-delete
```

#### 请求头

| 名称 | 必填 | 描述 |
|------|------|------|
| Authorization | 是 | Bearer {token} |

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| registryIds | array | 是 | 股东名册ID数组，至少包含一个ID |

#### 请求示例

```json
{
  "registryIds": ["id1", "id2", "id3"]
}
```

#### 响应

##### 成功响应 - 200

```json
{
  "code": 200,
  "message": "已成功删除 2 个股东名册",
  "data": {
    "success": {
      "count": 2,
      "ids": ["id1", "id2"]
    },
    "failed": {
      "count": 1,
      "ids": ["id3"]
    },
    "total": 3
  }
}
```

##### 错误响应 - 404 (全部资源不存在)

```json
{
  "code": 404,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "未找到任何指定的股东名册记录"
  },
  "data": null
}
```

##### 错误响应 - 400 (参数验证错误)

```json
{
  "code": 400,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败：至少需要提供一个名册ID"
  },
  "data": null
}
```

## 4. 错误码详细说明

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| UNAUTHORIZED | 用户未授权或会话过期 | 401 |
| FORBIDDEN | 用户无权执行此操作 | 403 |
| RESOURCE_NOT_FOUND | 请求的资源不存在 | 404 |
| VALIDATION_ERROR | 请求参数验证失败 | 400 |
| RECORD_COUNT_MISMATCH | 申报数量不一致 | 400 |
| DUPLICATE_REGISTRY | 重复导入相同的股东名册 | 409 |
| COMPANY_CODE_MISMATCH | 组织已绑定其他公司代码 | 400 |
| DATABASE_ERROR | 数据库操作错误 | 500 |
| INTERNAL_SERVER_ERROR | 服务器内部错误 | 500 |

## 5. 实现注意事项

1. 所有API路由实现应遵循现有项目的目录结构，创建`packages/api/src/routes/shareholder-registry`目录
2. 在路由实现中使用Hono框架和hono-openapi提供的工具进行路由定义和参数验证
3. 所有接口都应使用authMiddleware确保用户已登录
4. 使用Zod进行请求参数验证和类型定义
5. 确保与prisma模型定义一致的请求和响应结构
6. 针对批量数据操作（如上传大量股东数据），考虑使用事务处理确保数据一致性
7. 实现详细的错误处理逻辑，返回统一格式的错误响应
8. 在app.ts中注册新的路由模块
9. 响应结构必须包含code、message和data/error字段

## 6. 路由文件结构方案

根据项目现有架构，股东名册API应采用以下文件结构组织：

```
packages/api/src/routes/shareholder-registry/
├── router.ts                # 主路由文件，聚合所有子路由
├── types.ts                 # 共享类型定义
├── lib/                     # 功能辅助库
│   ├── validators.ts        # 请求验证模式定义
│   └── utils.ts             # 辅助工具函数
├── upload.ts                # 上传股东名册处理
├── list.ts                  # 获取名册列表处理
├── report-dates.ts          # 获取期数日期处理
├── shareholders.ts          # 获取股东列表处理  
└── delete.ts                # 删除名册处理（包含单个删除和批量删除）
```

在主应用文件中注册股东名册路由：

```typescript
// packages/api/src/app.ts
import { shareholderRegistryRouter } from "./routes/shareholder-registry/router";

const appRouter = app
  // ... 现有路由 ...
  .route("/", shareholderRegistryRouter)
  // ... 其他路由 ...
```

主路由文件实现方式：

```typescript
// packages/api/src/routes/shareholder-registry/router.ts
import { Hono } from "hono";
import { uploadRouter } from "./upload";
import { listRouter } from "./list";
import { registerDatesRouter } from "./report-dates";
import { shareholdersRouter } from "./shareholders";
import { deleteRouter } from "./delete";

// 创建股东名册路由器并设置基础路径
export const shareholderRegistryRouter = new Hono()
  .basePath("/shareholder-registry")
  // 挂载子路由
  .route("/", uploadRouter)
  .route("/", listRouter)
  .route("/", registerDatesRouter)
  .route("/", shareholdersRouter)
  .route("/", deleteRouter);
```

每个子路由文件应遵循项目的路由模式，使用Hono框架和hono-openapi提供的工具进行路由定义，如下所示：

```typescript
// packages/api/src/routes/shareholder-registry/upload.ts (示例)
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { companyInfoSchema, shareholderSchema } from "./lib/validators";

export const uploadRouter = new Hono().post(
  "/upload",
  authMiddleware,
  validator("json", z.object({
    organizationId: z.string(),
    fileName: z.string(),
    recordCount: z.number(),
    registerDate: z.string(),
    companyCode: z.string(),
    companyInfo: companyInfoSchema,
    shareholders: z.array(shareholderSchema)
  })),
  describeRoute({
    tags: ["Shareholder Registry"],
    summary: "Upload shareholder registry",
    // ... OpenAPI描述
  }),
  async (c) => {
    // 实际处理逻辑
  }
);
```

此架构方案确保了代码的模块化和可维护性，遵循了项目现有的设计模式和最佳实践。

## 7. 控股股东和前十大股东的实现细节

### 7.1 概述

为了提供更丰富的股权结构分析功能，我们在股东名册列表查询接口中增加了对控股股东和前十大股东数据的自动分析功能。这些功能可以帮助用户快速了解公司的股权集中度和控制权分布。

### 7.2 数据结构

在 `packages/api/src/routes/shareholder-registry/types.ts` 中，我们对 `CompanyDetailInfo` 类型扩展了以下字段：

```typescript
export type CompanyDetailInfo = {
  // ...现有字段...
  
  // 控股股东信息
  controllingShareholderInfo?: {
    securitiesAccountName: string;  // 证券账户名称
    shareholdingRatio: string;      // 持股比例
    numberOfShares: string;         // 持股数量
  };
  
  // 前十股东持股信息
  topTenShareholdersInfo?: {
    totalRatio: string;            // 前十大股东持股比例总和
    totalShares: string;           // 前十大股东持股数量总和
  };
};
```

### 7.3 实现方法

我们在 `packages/api/src/routes/shareholder-registry/list.ts` 中采用了高效的数据库查询方案，利用 SQL 的窗口函数和聚合函数，一次性查询所有需要的数据：

#### 7.3.1 控股股东查询

控股股东定义为持股比例最大的股东，我们使用 PostgreSQL 的窗口函数 `ROW_NUMBER()` 结合 `PARTITION BY` 和 `ORDER BY` 在一次查询中获取每个名册的控股股东：

```sql
WITH RankedShareholders AS (
  SELECT 
    "registryId",
    "securitiesAccountName",
    "shareholdingRatio",
    "numberOfShares",
    ROW_NUMBER() OVER (PARTITION BY "registryId" ORDER BY "shareholdingRatio" DESC) as rn
  FROM "shareholder"
  WHERE "registryId" IN (...)
)
SELECT 
  "registryId",
  "securitiesAccountName",
  "shareholdingRatio",
  "numberOfShares"
FROM RankedShareholders
WHERE rn = 1
```

#### 7.3.2 前十大股东汇总

前十大股东的持股汇总使用类似的窗口函数获取前十排名，然后使用聚合函数计算总和：

```sql
WITH RankedShareholders AS (
  SELECT 
    "registryId",
    "shareholdingRatio",
    "numberOfShares",
    ROW_NUMBER() OVER (PARTITION BY "registryId" ORDER BY "shareholdingRatio" DESC) as rn
  FROM "shareholder"
  WHERE "registryId" IN (...)
),
TopTenShareholders AS (
  SELECT 
    "registryId",
    "shareholdingRatio",
    "numberOfShares"
  FROM RankedShareholders
  WHERE rn <= 10
)
SELECT 
  "registryId",
  SUM("shareholdingRatio") as "totalRatio",
  SUM("numberOfShares") as "totalShares"
FROM TopTenShareholders
GROUP BY "registryId"
```

### 7.4 性能优化

我们采用了以下几种优化策略：

1. **批量查询**：对于多个名册的数据，使用 `IN` 操作符和窗口函数一次性获取所有需要的信息，避免了循环查询带来的性能开销。

2. **SQL 层面计算**：将计算工作下放到数据库层面，利用 PostgreSQL 的高效聚合功能，减少应用层的数据处理量。

3. **映射表优化**：使用 JavaScript 的 Map 数据结构快速映射和查找结果，提高响应数据组装的效率。

```typescript
// 创建查找映射，方便快速访问
const controllingShareholderMap = new Map(
  controllingShareholders.map(item => [item.registryId, item])
);

const topTenShareholdersMap = new Map(
  topTenShareholders.map(item => [
    item.registryId, 
    { totalRatio: item.totalRatio, totalShares: item.totalShares }
  ])
);
```

### 7.5 数据处理注意事项

1. **Decimal 类型转换**：PostgreSQL 的 Decimal 类型通过 Prisma 查询后需要转换为字符串，才能正确序列化为 JSON：

```typescript
companyDetail.controllingShareholderInfo = {
  securitiesAccountName: controllingShareholder.securitiesAccountName,
  shareholdingRatio: controllingShareholder.shareholdingRatio.toString(),
  numberOfShares: controllingShareholder.numberOfShares.toString()
};
```

2. **空值处理**：对于没有足够股东数据的名册，我们进行了空值检查，确保响应数据的完整性：

```typescript
// 添加控股股东信息（如果存在）
const controllingShareholder = controllingShareholderMap.get(item.id);
if (controllingShareholder) {
  companyDetail.controllingShareholderInfo = {
    // ...
  };
}
```

通过这些实现技术，我们能够在保持高性能的同时，为用户提供更有价值的股权结构分析数据。 