# 股东名册上传API实施方案

**文档日期:** 2025-05-29
**版本:** 1.0

## 1. 需求概述

### 1.1 背景

当前系统已支持深市01和05名册的上传和合并，现需扩展支持沪市t1、t2和t3名册的上传和合并。沪市名册与深市名册在字段和数据结构上存在差异，需要进行映射和适配。此外，需要对`Shareholder`模型添加两个新字段以支持沪市特有数据：
- `shareTradingCategory`：流通类型（来自t1名册）
- `rightsCategory`：权益类别（来自t1名册）

### 1.2 目标

1. 重构上传API，将不同类型名册的处理逻辑分离，降低代码复杂度
2. 支持深市01/05名册和沪市t1/t2/t3名册的上传和合并
3. 维持现有的"先到先得"合并原则，确保数据一致性
4. 提高代码可维护性和可扩展性

## 2. 数据库模型调整

### 2.1 Shareholder模型新增字段

需要在`Shareholder`模型中添加两个新字段：

```prisma
model Shareholder {
  // 现有字段
  ...
  
  // 新增字段
  shareTradingCategory  String?             // 流通类型（沪市t1名册）
  rightsCategory        String?             // 权益类别（沪市t1名册）
  
  // 已有字段
  marginCollateralAccountNumber  String?    // 汇总账户号码（05名册）
  marginCollateralAccountName    String?    // 汇总账户名称（05名册）
  natureOfShares                 String?    // 股份性质（05名册）
  remarks               String?             // 备注
  registerDate          DateTime            // 名册日期
  uploadedAt            DateTime            @default(now()) // 上传时间
}
```

## 3. API设计

### 3.1 上传API结构改进

为了解决代码臃肿问题，我们将上传API按名册类型拆分为多个处理器，**注意这些处理器不影响路由地址，只是内部实现的代码组织方式**：

```
packages/api/src/routes/shareholder-registry/
├── upload.ts                      # 主入口路由（URL端点不变）
├── handlers/                      # 处理器目录（内部实现，不影响API地址）
│   ├── upload-01.ts               # 处理深市01名册上传
│   ├── upload-05.ts               # 处理深市05名册上传
│   ├── upload-t1.ts               # 处理沪市t1名册上传
│   ├── upload-t2.ts               # 处理沪市t2名册上传
│   ├── upload-t3.ts               # 处理沪市t3名册上传
│   ├── merge-01-05.ts             # 处理深市01/05名册合并
│   └── merge-t1-t2-t3.ts          # 处理沪市t1/t2/t3名册合并
└── lib/
    ├── validators.ts              # 请求验证器
    └── utils.ts                   # 工具函数（包含名册类型检测等实用函数）
```

### 3.2 请求验证器更新

扩展现有验证器以支持沪市名册特有字段：

```typescript
// packages/api/src/routes/shareholder-registry/lib/validators.ts

export const UploadRegistrySchema = z.object({
  // 现有字段
  organizationId: z.string().min(1, "组织ID不能为空"),
  fileName: z.string().min(1, "文件名不能为空"),
  recordCount: z.number().int().positive("记录数量必须为正整数"),
  registerDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "报告日期格式必须为YYYY-MM-DD"),
  companyCode: z.string().min(1, "公司代码不能为空"),
  
  // 添加沪市特有字段验证
  shareholders: z.array(
    z.object({
      // 现有字段
      shareholderId: z.string().min(1, "证件号码不能为空"),
      unifiedAccountNumber: z.string().min(1, "一码通账户号码不能为空"),
      securitiesAccountName: z.string().min(1, "证券账户名称不能为空"),
      // ... 其他字段
      
      // 新增沪市特有字段（由前端直接提供）
      shareTradingCategory: z.string().optional(),  // 流通类型
      rightsCategory: z.string().optional(),        // 权益类别
      
      // 已有05名册特有字段
      marginCollateralAccountNumber: z.string().optional(),
      marginCollateralAccountName: z.string().optional(),
      natureOfShares: z.string().optional(),
    })
  )
});
```

### 3.3 API端点设计

保持现有的上传端点不变，但改进名册类型的判断逻辑，采用双重验证机制：

```
POST /shareholder-registry/upload
```

请求主体结构：

```typescript
{
  organizationId: string;
  fileName: string;  // 作为辅助判断依据
  recordCount: number;
  registerDate: string; // YYYY-MM-DD
  companyCode: string;
  companyInfo: {
    // 公司信息字段
    ...
  };
  shareholders: Array<{
    // 股东信息字段
    ...
    // 可能包含特定名册类型的特有字段
    shareTradingCategory?: string;  // 沪市流通类型（前端直接提供）
    rightsCategory?: string;        // 沪市权益类别（前端直接提供）
    
    // 05名册特有字段
    marginCollateralAccountNumber?: string; // 汇总账户号码
    marginCollateralAccountName?: string;   // 汇总账户名称
    natureOfShares?: string;                // 股份性质
    
    // 01名册特有字段
    contactNumber?: string;                 // 电话号码
    zipCode?: string;                       // 邮政编码
    relatedPartyIndicator?: string;         // 关联关系确认标识
    cashAccount?: string;                   // 普通证券账户
    sharesInCashAccount?: number;           // 普通账户持股数量
    numberOfShares?: number;                // 总持股数量
    lockedUpShares?: number;                // 限售股数量
    shareholdingRatio?: number;             // 持股比例
    remarks?: string;                       // 备注
    clientCategory?: string;                // 客户类别
      //其他0101名册特有字段...
  }>;
}
```

### 3.4 API测试计划

实现API后，需要进行以下测试以验证功能是否正常：

1. **首次上传测试**
   - 测试上传沪市t1名册
   - 测试上传沪市t2名册
   - 测试上传沪市t3名册
   - 验证数据是否正确保存到数据库

2. **合并逻辑测试**
   - 测试t1名册上传后再上传t2名册
   - 测试t2名册上传后再上传t1名册
   - 测试t1+t2名册上传后再上传t3名册
   - 验证数据合并是否正确
   - 验证重复上传相同类型名册是否被拒绝
   - 验证在t1+t2+t3都上传后，再次上传任何类型是否被拒绝

3. **错误处理测试**
   - 测试上传无效文件名的请求
   - 测试recordCount与实际股东数量不匹配的情况
   - 测试组织与公司代码绑定冲突的情况

可以使用浏览器开发者工具或Postman等工具发送请求进行测试，观察响应状态码和消息，并通过数据库查询验证数据是否正确处理。

## 4. 实现策略

### 4.1 主入口路由

主入口路由负责请求验证和判断名册类型并分发到对应的处理器，使用双重验证机制：

```
/**
 * 股东名册上传路由伪代码
 */
函数 上传名册路由():
  // 引入工具函数
  从 "./lib/utils" 导入 { 通过字段特征检测名册类型, 通过文件名检测名册类型 }
  
  // 注册POST路由，添加认证中间件和数据加密中间件
  路由.POST("/upload", 认证中间件, 股东数据加密中间件, 处理请求)

/**
 * 处理请求的主函数
 */
异步函数 处理请求(上下文):
  尝试:
    // 1. 获取并验证请求数据
    请求数据 = 上下文.获取("requestData")
    用户ID = 上下文.获取("user").id
    
    验证结果 = 上传名册模式.安全解析(请求数据)
    如果 验证结果.失败:
      抛出 HTTP异常(400, { 消息: "请求参数无效", 原因: 验证结果.错误 })
    
    数据 = 验证结果.数据
    
    // 2. 双重验证机制确定名册类型
    名册类型 = 通过字段特征检测名册类型(数据.股东列表)
    文件名类型 = 通过文件名检测名册类型(数据.文件名)
    
    // 3. 处理两种检测结果不一致的情况
    如果 名册类型 !== 文件名类型:
      控制台.警告(`名册类型检测不一致: 字段检测结果为${名册类型}，文件名检测结果为${文件名类型}，将使用字段检测结果`)
    
    // 4. 字段检测失败时使用文件名检测结果
    如果 名册类型 === "未知":
      名册类型 = 文件名类型
    
    // 5. 两种检测方式都失败时抛出错误
    如果 名册类型 === "未知":
      抛出 HTTP异常(400, { 消息: "无法确定名册类型，请检查上传数据" })
    
    // 6. 根据名册类型分发到不同的处理器
    处理结果
    根据 名册类型 分情况处理:
      情况 "01":
        处理结果 = 等待 上传01名册处理器(上下文, 数据, 用户ID)
        跳出
      情况 "05":
        处理结果 = 等待 上传05名册处理器(上下文, 数据, 用户ID)
        跳出
      情况 "t1":
        处理结果 = 等待 上传T1名册处理器(上下文, 数据, 用户ID)
        跳出
      情况 "t2":
        处理结果 = 等待 上传T2名册处理器(上下文, 数据, 用户ID)
        跳出
      情况 "t3":
        处理结果 = 等待 上传T3名册处理器(上下文, 数据, 用户ID)
        跳出
      默认:
        抛出 HTTP异常(400, { 消息: "不支持的名册类型" })
    
    // 7. 检查处理结果
    如果 !处理结果:
      抛出 HTTP异常(500, { 消息: "处理股东名册数据失败" })
    
    // 8. 构建响应数据
    响应数据 = {
      id: 处理结果.id,
      fileName: 处理结果.fileName,
      recordCount: 处理结果.recordCount,
      registerDate: 处理结果.registerDate.转ISO字符串().分割('T')[0],
      uploadedAt: 处理结果.uploadedAt.转ISO字符串()
    }
    
    // 9. 返回成功响应
    成功响应(上下文, "股东名册上传成功", 响应数据)
    返回
    
  捕获 错误:
    // 10. 处理异常
    处理HTTP异常(上下文, 错误)
    返回
```

### 4.2 工具函数实现

将名册类型检测相关的工具函数放在 `lib/utils.ts` 中：

```
/**
 * lib/utils.ts
 * 
 * 名册类型检测工具函数集合
 */

/**
 * 通过股东数据字段特征检测名册类型
 * @param 股东列表 股东数据数组
 * @return 名册类型: "01" | "05" | "t1" | "t2" | "t3" | "未知"
 */
export 函数 通过字段特征检测名册类型(股东列表):
  // 1. 检查输入有效性
  如果 !股东列表 或 股东列表.长度 === 0:
    返回 "未知"
  
  // 2. 仅检查第一条记录
  第一条记录 = 股东列表[0]
  
  // 3. 检查各类型名册的特征字段
  
  // 检查t1名册特有字段（优先级最高）
  如果 第一条记录.shareTradingCategory 或 第一条记录.rightsCategory:
    返回 "t1"
  
  // 检查05名册特有字段（第二优先级）
  如果 第一条记录.marginCollateralAccountNumber 或 
      第一条记录.marginCollateralAccountName 或 
      第一条记录.natureOfShares:
    返回 "05"
  
  // 检查01名册特有字段（第三优先级）
  如果 第一条记录.contactNumber 或 
      第一条记录.zipCode 或 
      第一条记录.relatedPartyIndicator 或 
      第一条记录.cashAccount 或 
      第一条记录.sharesInCashAccount 或 
      第一条记录.numberOfShares 或 
      第一条记录.lockedUpShares 或 
      第一条记录.shareholdingRatio 或 
      第一条记录.remarks 或 
      第一条记录.clientCategory:
      //其他0101名册特有字段...
    返回 "01"
  
  // 检查t2名册特有字段（需根据实际特点补充）
  // ...
  
  // 检查t3名册特有字段（需根据实际特点补充）
  // ...
  
  // 4. 如果无法识别，返回未知类型
  返回 "未知"

/**
 * 通过文件名检测名册类型
 * @param 文件名 文件名
 * @return 名册类型: "01" | "05" | "t1" | "t2" | "t3" | "未知"
 */
export 函数 通过文件名检测名册类型(文件名):
  // 1. 统一转换为小写进行判断，提高匹配准确性
  小写文件名 = 文件名.转小写()
  
  // 2. 根据文件名包含的关键字判断名册类型
  如果 小写文件名.包含("dqmc01"):
    返回 "01"
  否则如果 小写文件名.包含("dqmc05"):
    返回 "05"
  否则如果 小写文件名.包含("t1"):
    返回 "t1"
  否则如果 小写文件名.包含("t2"):
    返回 "t2"
  否则如果 小写文件名.包含("t3"):
    返回 "t3"
  否则:
    返回 "未知"

/**
 * 其他可能的工具函数...
 */
```

### 4.3 名册处理器

每个名册类型都有对应的处理器，负责处理特定类型的名册数据。以沪市t1名册处理器为例：

```
/**
 * 沪市t1名册处理器伪代码：
 * 
 * 函数 uploadT1Handler(请求上下文, 请求数据, 用户ID):
 *   返回 数据库事务(tx => {
 *     // 1. 并行查询组织信息和同日期名册记录
 *     [组织信息, 已存在名册] = 并行执行:
 *       - 查询组织信息(data.organizationId)
 *       - 查询同日期名册(组织ID, 公司代码, 登记日期)
 *     
 *     // 2. 验证组织存在性和名册类型合法性
 *     如果(组织不存在):
 *       抛出错误(404, "组织不存在")
 *     
 *     如果(已存在名册):
 *       如果(已存在名册包含t1类型):
 *         抛出错误(400, "已存在t1类型名册，不能重复上传")
 *       
 *       如果(已同时存在t1、t2和t3名册):
 *         抛出错误(400, "已存在完整名册，不能继续上传")
 *     
 *     // 3. 处理组织与公司的绑定关系
 *     元数据 = 解析组织元数据或创建空对象
 *     
 *     如果(已绑定其他公司代码):
 *       抛出错误(400, "该组织已绑定其他公司，不能上传")
 *     
 *     如果(首次绑定):
 *       更新组织元数据(绑定公司代码、名称和时间)
 *     
 *     // 4. 处理名册记录
 *     如果(已存在名册):
 *       更新名册(合并文件名)
 *     否则:
 *       创建新名册和公司信息记录
 *     
 *     // 5. 批量处理股东数据
 *     收集所有股东ID和一码通账号
 *     查询现有股东记录
 *     构建高效查询Map(一码通账号_股东ID作为键)
 *     
 *     对于每个股东数据:
 *       如果(已存在记录):
 *         处理更新逻辑(优先保留已有数据，仅填充空字段)
 *       否则:
 *         准备创建记录(包含t1特有字段：流通类型、权益类别)
 *     
 *     // 6. 执行批量创建和更新操作
 *     固定批处理大小为200
 *     分批执行创建操作，每批次处理200条记录
 *     
 *     动态控制并发度(10/15/20)
 *     分批并行处理更新操作
 *     
 *     // 7. 名册更新后处理
 *     如果(更新已有名册):
 *       重新计算实际股东数量
 *       更新名册记录数
 *     
 *     // 8. 返回处理结果
 *     返回名册记录
 *   })
 */
```

### 4.4 合并处理器

对于沪市t1/t2/t3名册的合并处理，需要单独实现合并逻辑：

```
/**
 * 沪市t1/t2/t3名册合并处理器伪代码：
 * 
 * 函数 mergeT1T2T3Handler(事务上下文, 请求数据, 用户ID):
 *   // 1. 并行查询组织信息和同日期名册记录
 *   [组织信息, 已存在名册] = 并行执行:
 *     - 查询组织信息(data.organizationId)
 *     - 查询同日期名册(组织ID, 公司代码, 登记日期)
 *   
 *   // 2. 验证组织存在性
 *   如果(!组织信息):
 *     抛出错误(404, "组织不存在")
 *   
 *   // 3. 检查现有名册类型
 *   如果(已存在名册):
 *     // 判断已有名册包含的类型
 *     hasT1 = 已存在名册.fileName.包含("t1")
 *     hasT2 = 已存在名册.fileName.包含("t2")
 *     hasT3 = 已存在名册.fileName.包含("t3")
 *     
 *     // 判断当前上传的名册类型
 *     currentType = 通过文件名或数据特征检测名册类型(请求数据)
 *     isT1 = currentType === "t1"
 *     isT2 = currentType === "t2"
 *     isT3 = currentType === "t3"
 *     
 *     // 验证名册类型合法性
 *     如果(isT1 && hasT1):
 *       抛出错误(400, "DUPLICATE_REGISTRY_TYPE:已存在t1类型名册，不能重复上传")
 *     
 *     如果(isT2 && hasT2):
 *       抛出错误(400, "DUPLICATE_REGISTRY_TYPE:已存在t2类型名册，不能重复上传")
 *     
 *     如果(isT3 && hasT3):
 *       抛出错误(400, "DUPLICATE_REGISTRY_TYPE:已存在t3类型名册，不能重复上传")
 *     
 *     // 检查是否已存在完整名册（t1+t2+t3）
 *     如果(hasT1 && hasT2 && hasT3):
 *       抛出错误(400, "COMPLETE_REGISTRY:已存在t1、t2和t3类型名册的合并数据，不能继续上传")
 *     
 *     // 4. 合并名册记录
 *     新文件名 = 已存在名册.fileName + "\n" + 请求数据.fileName
 *     更新后的名册 = 更新名册记录(合并文件名)
 *     
 *     // 5. 根据名册类型更新公司信息
 *     如果(isT1):
 *       更新公司信息记录(股东总数、总股数等)
 *   否则:
 *     // 6. 如果不存在同日期名册，创建新名册
 *     新名册 = 创建名册记录(请求数据, 用户ID)
 *     创建公司信息记录(请求数据)
 *   
 *   // 7. 批量处理股东数据
 *   根据名册类型收集所有需要的字段
 *   批量创建或更新股东记录
 *   
 *   // 8. 更新实际股东数量
 *   如果(已存在名册):
 *     查询实际股东数量
 *     更新名册记录数为实际数量
 *   
 *   // 9. 返回处理结果
 *   返回名册记录
 */
```

为了支持处理沪市t1/t2/t3名册的特有字段，需要扩展现有的字段更新处理函数：

```
/**
 * 股东字段更新处理函数伪代码：
 * 
 * 函数 processShareholderUpdateFields(更新数据, 现有记录, 新数据, 名册类型):
 *   字段更新标志 = false
 *   
 *   // 根据不同名册类型处理特有字段
 *   如果(名册类型 == "t1"):
 *     // 处理t1特有字段
 *     如果(现有记录.流通类型为空 且 新数据.流通类型有值):
 *       更新数据.流通类型 = 新数据.流通类型
 *       字段更新标志 = true
 *     
 *     如果(现有记录.权益类别为空 且 新数据.权益类别有值):
 *       更新数据.权益类别 = 新数据.权益类别
 *       字段更新标志 = true
 *   
 *   如果(名册类型 == "01"):
 *     // 处理01特有字段更新
 *     ...省略已有逻辑...
 *   
 *   如果(名册类型 == "05"):
 *     // 处理05特有字段更新
 *     ...省略已有逻辑...
 *   
 *   如果(名册类型 == "t2"):
 *     // 处理t2特有字段更新
 *     ...t2特有字段处理...
 *   
 *   如果(名册类型 == "t3"):
 *     // 处理t3特有字段更新
 *     ...t3特有字段处理...
 *   
 *   // 处理所有类型共有字段
 *   对于每个共有字段:
 *     如果(现有值为空 且 新值不为空):
 *       设置更新字段
 *       字段更新标志 = true
 *   
 *   返回 字段更新标志
 */
```

## 5. 前端适配

前端需要适配新的字段：

1. 更新名册解析器，支持沪市t1/t2/t3名册的解析

## 6. 迁移计划

### 6.1 数据库迁移

1. 创建新的迁移文件，添加`Shareholder`表的新字段
2. 推送变更到数据库：`pnpm --filter database push`