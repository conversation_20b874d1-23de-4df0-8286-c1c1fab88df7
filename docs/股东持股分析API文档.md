# 股东持股分析API文档

> 文档更新时间：2025-06-17 17:04:20

## 更新记录

| 版本 | 更新时间 | 更新内容 | 作者 |
| ---- | -------- | -------- | ---- |
| v1.2 | 2025-06-17 17:04:20 | 修复sortType参数，支持具体期数日期(YYYY-MM-DD)作为排序字段 | hayden |
| v1.1 | 2025-06-17 16:40:27 | 新增sortType参数，支持按排名或期数日期排序 | hayden |
| v1.0 | 2025-06-10 17:30:00 | 初始版本，包含基础股东持股变化分析功能 | hayden |

## 目录

- [简介](#简介)
- [接口安全说明](#接口安全说明)
- [接口列表](#接口列表)
  - [1. 获取股东类型列表](#1-获取股东类型列表)
  - [2. 获取股东持股变化分析](#2-获取股东持股变化分析)

## 简介

本文档详细介绍了股东持股分析相关的API接口，包括股东类型查询和股东持股变化分析功能。这些接口主要用于前端展示股东结构和持股变动情况，支持多种筛选条件和分页功能。

## 接口安全说明

所有接口均采用以下安全措施：

1. **认证机制**：所有请求需要通过`authMiddleware`进行身份验证
2. **数据加密**：请求和响应数据通过`shareholderCryptoMiddleware`进行加密处理
3. **参数验证**：使用Zod进行严格的参数验证，防止非法请求

## 接口列表

### 1. 获取股东类型列表

#### 接口描述

获取指定组织下的所有股东类型（去重后的列表）。

#### 请求URL

```
POST /shareholder-types
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| organizationId | string | 是 | 组织ID |

#### 请求示例

```json
{
  "organizationId": "org_123456789"
}
```

#### 响应参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | number | 状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.shareholderTypes | array | 股东类型列表 |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取股东类型列表成功",
  "data": {
    "shareholderTypes": [
      "个人",
      "机构",
      "证券公司",
      "基金",
      "QFII"
    ]
  }
}
```

#### 错误码

| 错误码 | 描述 |
| ------ | ---- |
| 400 | 请求参数无效 |
| 500 | 服务器内部错误 |

### 2. 获取股东持股变化分析

#### 接口描述

分析指定时间范围内股东持股变化情况，支持按股东类型筛选、搜索和排序。支持按排名排序、期数日期排序或指定期数排序三种排序方式。

#### 请求URL

```
POST /shareholding-changes
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| organizationId | string | 是 | 组织ID |
| startDate | string | 是 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 是 | 结束日期，格式：YYYY-MM-DD |
| shareholderType | string | 否 | 股东类型，用于筛选特定类型的股东 |
| searchTerm | string | 否 | 搜索关键词，可搜索股东名称、ID或统一账号 |
| sortType | string | 否 | 排序类型，可选值：rank（按排名排序）、date（按期数日期排序）、具体日期（YYYY-MM-DD格式，按指定期数排序），默认：rank |
| sortOrder | string | 否 | 排序方式，可选值：asc（升序）、desc（降序），默认：desc |
| page | number | 否 | 页码，默认：1 |
| limit | number | 否 | 每页记录数，默认：30，最大：100 |

#### 请求示例

```json
{
  "organizationId": "org_123456789",
  "startDate": "2025-01-01",
  "endDate": "2025-06-01",
  "shareholderType": "机构",
  "searchTerm": "证券",
  "sortType": "2025-03-01",
  "sortOrder": "desc",
  "page": 1,
  "limit": 30
}
```

#### 响应参数

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | number | 状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.analysisRange | object | 分析范围信息 |
| data.analysisRange.startDate | string | 实际分析的开始日期 |
| data.analysisRange.endDate | string | 实际分析的结束日期 |
| data.analysisRange.totalPeriods | number | 分析的总期数 |
| data.availableDates | array | 可用的报告日期列表 |
| data.shareholderChanges | array | 股东持股变化数据列表 |
| data.pagination | object | 分页信息 |

#### 股东持股变化数据结构

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| rank | number | 排名 |
| shareholderId | string | 股东ID |
| shareholderName | string | 股东名称 |
| shareholderType | string | 股东类型 |
| unifiedAccountNumber | string | 统一账户编号 |
| netChange | number | 净变化股数（期末持股 - 期初持股） |
| startHolding | number | 期初持股数量 |
| endHolding | number | 期末持股数量 |
| periodChanges | array | 各期变动情况 |
| periodChanges[].date | string | 报告日期 |
| periodChanges[].change | number | 相对上期变化股数 |
| periodChanges[].holding | number | 当期持股数量 |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取股东持股变化分析数据成功",
  "data": {
    "analysisRange": {
      "startDate": "2025-01-01",
      "endDate": "2025-06-01",
      "totalPeriods": 6
    },
    "availableDates": [
      "2025-01-01",
      "2025-02-01",
      "2025-03-01",
      "2025-04-01",
      "2025-05-01",
      "2025-06-01"
    ],
    "shareholderChanges": [
      {
        "rank": 1,
        "shareholderId": "SH001",
        "shareholderName": "某证券投资有限公司",
        "shareholderType": "机构",
        "unifiedAccountNumber": "A123456789",
        "netChange": 50000,
        "startHolding": 100000,
        "endHolding": 150000,
        "periodChanges": [
          {
            "date": "2025-02-01",
            "change": 10000,
            "holding": 110000
          },
          {
            "date": "2025-03-01",
            "change": 15000,
            "holding": 125000
          },
          {
            "date": "2025-04-01",
            "change": 0,
            "holding": 125000
          },
          {
            "date": "2025-05-01",
            "change": 15000,
            "holding": 140000
          },
          {
            "date": "2025-06-01",
            "change": 10000,
            "holding": 150000
          }
        ]
      },
      // 更多股东数据...
    ],
    "pagination": {
      "page": 1,
      "limit": 30,
      "total": 150,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### 错误码

| 错误码 | 描述 |
| ------ | ---- |
| 400 | 请求参数无效，可能是日期格式错误或开始日期晚于结束日期 |
| 500 | 服务器内部错误 |

## 前端实现建议

### 股东类型筛选组件

可以使用`/shareholder-types`接口获取所有股东类型，用于构建筛选下拉框：

```typescript
// 获取股东类型列表
const fetchShareholderTypes = async (organizationId: string) => {
  const response = await api.post('/shareholder-types', { organizationId });
  return response.data.shareholderTypes;
};
```

### 股东持股变化分析图表

可以使用`/shareholding-changes`接口的数据构建以下可视化图表：

1. **持股变化排行榜**：按净变化量排序展示股东变化情况
2. **持股趋势图**：使用`periodChanges`数据绘制股东持股趋势线图
3. **股东结构分析**：按股东类型统计持股情况的饼图或环形图

```typescript
// 获取股东持股变化数据 - 按排名排序（默认）
const fetchShareholdingChangesByRank = async (params) => {
  const response = await api.post('/shareholding-changes', {
    ...params,
    sortType: 'rank',
    sortOrder: 'desc'  // 增持最多的股东排在前面
  });
  return response.data;
};

// 获取股东持股变化数据 - 按期数日期排序
const fetchShareholdingChangesByDate = async (params) => {
  const response = await api.post('/shareholding-changes', {
    ...params,
    sortType: 'date',
    sortOrder: 'desc'  // 最近有变动的股东排在前面
  });
  return response.data;
};

// 获取股东持股变化数据 - 按指定期数排序
const fetchShareholdingChangesBySpecificDate = async (params, specificDate) => {
  const response = await api.post('/shareholding-changes', {
    ...params,
    sortType: specificDate,  // 例如: '2024-03-29'
    sortOrder: 'desc'  // 该期数变化最大的股东排在前面
  });
  return response.data;
};
```

### 分页处理

前端可以利用响应中的`pagination`对象实现分页功能：

```typescript
// 分页控制示例
const handlePageChange = (newPage: number) => {
  setPage(newPage);
  fetchShareholdingChanges({
    ...currentParams,
    page: newPage
  });
};
```

## 排序说明

### sortType 参数详解

`sortType` 参数控制数据的排序方式，支持以下三种类型：

#### 1. rank（排名排序）- 默认值

- **排序依据**：按股东净变化量（期末持股 - 期初持股）进行排序
- **排序逻辑**：
  - `sortOrder: "desc"`：净变化量从大到小排序（增持最多的股东排在前面）
  - `sortOrder: "asc"`：净变化量从小到大排序（减持最多的股东排在前面）
- **适用场景**：分析股东增减持情况，快速识别主要增持或减持股东

#### 2. date（期数日期排序）

- **排序依据**：按股东最新期数日期进行排序
- **排序逻辑**：
  - `sortOrder: "desc"`：最新期数日期从新到旧排序（最近有变动的股东排在前面）
  - `sortOrder: "asc"`：最新期数日期从旧到新排序（较早有变动的股东排在前面）
- **适用场景**：按时间顺序分析股东变动，了解股东变动的时间分布

#### 3. 具体期数日期排序（YYYY-MM-DD格式）

- **排序依据**：按指定期数的持股变化量进行排序
- **参数格式**：具体日期字符串，如 `"2024-03-29"`、`"2025-01-15"` 等
- **排序逻辑**：
  - `sortOrder: "desc"`：指定期数变化量从大到小排序（该期增持最多的股东排在前面）
  - `sortOrder: "asc"`：指定期数变化量从小到大排序（该期减持最多的股东排在前面）
- **适用场景**：分析特定报告期的股东变动情况，识别某个时间点的主要变动股东
- **注意事项**：
  - 如果股东在指定期数没有数据，变化量按0处理
  - 指定的日期必须在查询的时间范围内
  - 日期格式必须严格符合 `YYYY-MM-DD` 格式

### 排名计算说明

**重要**：无论选择哪种 `sortType`，返回数据中的 `rank` 字段始终按照净变化量从大到小计算排名，不受 `sortType` 参数影响。

- `rank` 字段反映股东在净变化量方面的排名位置
- `sortType` 只影响数据的展示顺序，不影响排名计算逻辑
- 这样设计确保排名的一致性和可比性

## 注意事项

1. 所有日期参数必须符合`YYYY-MM-DD`格式
2. 开始日期不能晚于结束日期
3. 搜索时会同时匹配股东ID、股东名称和统一账号
4. 排序默认按净变化量降序排列（`sortType: "rank"`, `sortOrder: "desc"`）
5. 接口返回的实际分析日期范围可能与请求的日期范围不同，以实际有数据的日期为准
6. `sortType` 参数支持三种格式：`"rank"`、`"date"` 或具体日期（`YYYY-MM-DD`）
7. 当使用具体日期作为 `sortType` 时，该日期必须在查询的时间范围内
8. 具体期数排序时，如果股东在该期数没有数据，变化量按0处理

## 前端测试建议

### 测试用例

#### 1. 基础功能测试

```typescript
// 测试默认排序（按排名降序）
const testDefaultSort = async () => {
  const result = await fetchShareholdingChanges({
    organizationId: "your_org_id",
    startDate: "2025-01-01",
    endDate: "2025-06-01"
  });

  console.log("默认排序结果:", result);
  // 验证：数据应按净变化量从大到小排序
  // 验证：rank字段应从1开始递增
};
```

#### 2. 排名排序测试

```typescript
// 测试按排名升序排序
const testRankAscSort = async () => {
  const result = await fetchShareholdingChanges({
    organizationId: "your_org_id",
    startDate: "2025-01-01",
    endDate: "2025-06-01",
    sortType: "rank",
    sortOrder: "asc"
  });

  console.log("排名升序结果:", result);
  // 验证：数据应按净变化量从小到大排序
  // 验证：rank字段仍按净变化量从大到小计算
};
```

#### 3. 期数日期排序测试

```typescript
// 测试按期数日期降序排序
const testDateDescSort = async () => {
  const result = await fetchShareholdingChanges({
    organizationId: "your_org_id",
    startDate: "2025-01-01",
    endDate: "2025-06-01",
    sortType: "date",
    sortOrder: "desc"
  });

  console.log("日期降序结果:", result);
  // 验证：数据应按最新期数日期从新到旧排序
  // 验证：rank字段仍按净变化量计算，不受日期排序影响
};
```

#### 4. 指定期数排序测试

```typescript
// 测试按指定期数排序
const testSpecificDateSort = async () => {
  const result = await fetchShareholdingChanges({
    organizationId: "your_org_id",
    startDate: "2025-01-01",
    endDate: "2025-06-01",
    sortType: "2025-03-01",  // 指定具体期数
    sortOrder: "desc"
  });

  console.log("指定期数排序结果:", result);
  // 验证：数据应按2025-03-01期数的变化量从大到小排序
  // 验证：rank字段仍按净变化量计算，不受指定期数排序影响
  // 验证：没有该期数数据的股东变化量应为0
};
```

### 验证要点

1. **排序一致性**：确认不同sortType下数据排序符合预期
2. **排名独立性**：验证rank字段始终按净变化量计算，不受sortType影响
3. **参数兼容性**：测试新参数与现有参数的兼容性
4. **默认值处理**：验证sortType默认值为"rank"时的行为
5. **边界情况**：测试无数据、单条数据等边界情况下的排序行为
6. **日期格式验证**：测试具体日期格式的sortType参数验证
7. **期数数据处理**：验证指定期数不存在时的数据处理逻辑
8. **日期范围检查**：确认指定的期数日期在查询范围内

### 调试建议

在开发过程中，建议在控制台打印以下信息进行调试：

```typescript
console.log("请求参数:", {
  sortType,
  sortOrder,
  // 其他参数...
});

console.log("返回数据前3条:", result.shareholderChanges.slice(0, 3));
console.log("排名验证:", result.shareholderChanges.map(item => ({
  name: item.shareholderName,
  rank: item.rank,
  netChange: item.netChange
})));
```