# 股东名册01和05合并逻辑实施方案

## 1. 概述

本文档描述了股东名册系统中01和05名册合并逻辑的实施方案。系统需要支持两种不同类型的股东名册文件（01名册和05名册）的上传和合并处理，以确保数据的完整性和一致性。

### 1.1 背景说明

- **01名册**：文件名格式为`DQMC01_公司代码_日期.DBF`，包含普通股东信息
- **05名册**：文件名格式为`DQMC05_公司代码_日期.DBF`，包含信用账户股东信息
- 两种名册中有部分字段重叠，也有各自独有的字段
- 需要在数据库模型中新增字段以支持05名册特有的数据

### 1.2 目标

- 扩展现有的`Shareholder`模型，增加对05名册特有字段的支持
- 实现01和05名册数据的合并逻辑，确保同一股东的完整信息被正确存储
- 支持两种上传场景：先上传01名册后上传05名册，或先上传05名册后上传01名册

## 2. 数据库模型扩展

### 2.1 新增字段

根据需求，需要在`Shareholder`模型中新增以下字段：

| 序号 | 描述 | 字段名 | 数据来源 | 类型 | 说明 |
|------|------|--------|----------|------|------|
| 1 | 汇总账户号码 | marginCollateralAccountNumber | 名册05: 汇总证券账户 (HZZQZH) | string（长度=20） | 05名册特有 |
| 2 | 汇总账户名称 | marginCollateralAccountName | 名册05: 汇总证券账户名称 (HZZHMC) | string（长度=120） | 05名册特有 |
| 3 | 股份性质 | natureOfShares | 名册05: 股份性质 (GFXZ) | string（长度=2） | 05名册特有，"00"表示无限售条件流通股 |

### 2.2 数据库模型修改

需要修改`packages/database/prisma/schema.prisma`文件中的`Shareholder`模型：

```prisma
// 股东信息表
model Shareholder {
  // 现有字段保持不变
  ...
  
  // 新增字段
  marginCollateralAccountNumber  String?             // 汇总账户号码（05名册）
  marginCollateralAccountName    String?             // 汇总账户名称（05名册）
  natureOfShares                 String?             // 股份性质（05名册）
  
  // 现有索引和配置保持不变
  ...
}
```

## 3. 合并逻辑实现

### 3.1 核心合并流程

合并逻辑遵循以下优化的流程：

1. **解析名册文件信息和类型**：
   - 从文件名中提取名册类型（01或05）
   - 文件名格式：`DQMC01_公司代码_日期.DBF` 或 `DQMC05_公司代码_日期.DBF`

2. **组织和CompanyInfo公司绑定验证**：
   - 检查组织是否存在
   - 验证组织与公司代码的绑定关系
   - 如果存在
      - 进行股东记录的合并处理
   - 如果不存在：创建新名册，同时创建关联的公司信息
      - 如果组织尚未绑定公司，进行首次绑定并记录绑定时间
      - 05没有总股数、机构总股数、总户数，总机构户数默认值0

3. **ShareholderRegistry名册记录处理**：
   - 检查是否存在同日期名册记录
     - 如果存在：更新现有名册，合并文件名（使用换行符分隔）
     - 如果不存在：创建新名册，同时创建关联的公司信息

4. **Shareholder股东数据批量处理**：
   - 一次性查询所有可能存在的股东记录
   - 使用高效的Map数据结构快速查找匹配记录
   - 根据股东类别使用不同的查找键：
     - 基金/理财产品：使用"一码通账号_股东ID"作为复合键
     - 其他类型：使用股东ID作为键
   - 分别收集需要创建和更新的记录

5. **ShareholderRegistry名册更新后处理**：
   - 重新计算实际股东数量
   - 根据名册类型选择性更新公司信息
     - 01名册：更新所有公司统计数据
     - 05名册：保持原有公司统计数据不变
   - 更新名册记录数为实际合并后的数量

### 3.2 字段合并处理逻辑

当发现同一期数下已存在相同股东记录时（通过证件号码或复合键匹配），按照以下规则进行字段合并：

1. 使用辅助函数`processShareholderUpdateFields`处理字段更新逻辑
2. 根据名册类型（01或05）执行不同的合并策略
3. 对于每个待合并的字段：
   - 如果现有记录中该字段为空且当前名册有该字段值：更新该字段为当前名册的值
   - 否则：保留现有值不变，遵循"先到先得"原则
4. 仅当有字段需要更新时才执行数据库更新操作，避免不必要的数据库交互

### 3.3 合并场景处理

系统支持两种合并场景，使用相同的核心逻辑但针对不同名册类型有特殊处理：

#### 3.3.1 先上传01名册，后上传05名册

1. 01名册上传时，正常创建股东记录，包含01名册的所有字段
2. 05名册上传时，系统通过证件号码或复合键查找已存在的股东记录
3. 对于05名册特有的字段（如`marginCollateralAccountNumber`、`marginCollateralAccountName`、`natureOfShares`），直接填入
4. 对于共有字段，保留01名册的数据，遵循"先到先得"原则
5. 名册文件名以换行符分隔方式合并，例如：`DQMC01_公司代码_日期.DBF\nDQMC05_公司代码_日期.DBF`

#### 3.3.2 先上传05名册，后上传01名册

1. 05名册上传时，正常创建股东记录，包含05名册的所有字段
2. 01名册上传时，系统通过证件号码或复合键查找已存在的股东记录
3. 对于01名册特有的字段（如`relatedPartyIndicator`、`clientCategory`等），直接填入
4. 对于共有字段，保留05名册的数据，遵循"先到先得"原则
5. 同时更新公司信息表中的统计数据，使用01名册中的完整公司信息
6. 名册文件名以换行符分隔方式合并，例如：`DQMC05_公司代码_日期.DBF\nDQMC01_公司代码_日期.DBF`

## 4. 上传API改造

### 4.1 上传接口修改

需要修改`packages/api/src/routes/shareholder-registry/upload.ts`文件中的上传处理逻辑，实现01和05名册的合并功能：

```
函数 上传股东名册(请求数据):
  // 1. 数据验证和准备
  验证请求数据格式和权限
  解析文件名确定名册类型(01或05)
  提取公司代码和报告日期
  
  // 2. 开启数据库事务
  开启事务 {
    // 3. 并行查询组织信息和现有名册
    并行执行:
      查询组织信息(组织ID)
      查询是否存在同日期名册(组织ID, 公司代码, 报告日期)
    
    // 4. 检查组织和公司绑定关系
    如果组织不存在:
      抛出异常("组织不存在")
    
    如果组织已绑定其他公司:
      抛出异常("该组织已绑定其他公司")
    
    // 5. 首次绑定公司代码到组织
    如果组织未绑定公司:
      更新组织元数据，记录绑定公司和时间
    
    // 6. 处理名册记录
    如果存在同日期名册:
      更新现有名册，合并文件名(使用换行符分隔)
    否则:
      创建新名册和公司信息记录
    
    // 7. 批量处理股东数据
    提取所有股东ID和一码通账号
    批量查询现有股东记录
    
    // 8. 构建高效查找映射
    创建股东查找映射:
      对于基金/理财产品: 使用"一码通账号_股东ID"作为键
      对于其他类型: 使用"股东ID"作为键
    
    // 9. 处理每个股东记录
    准备创建列表和更新列表
    
    对于每个股东:
      确定查找键(基于股东类别)
      在映射中查找是否存在
      
      如果存在:
        使用字段更新函数处理合并逻辑
        如果有字段需要更新:
          添加到更新列表
      否则:
        添加到创建列表(包含所有字段)
    
    // 10. 批量创建新股东记录
    如果创建列表不为空:
      根据数据量确定批处理大小
      分批执行创建操作
    
    // 11. 批量更新现有股东记录
    如果更新列表不为空:
      根据数据量确定并发限制
      分批并行执行更新操作
    
    // 12. 名册更新后处理
    如果是更新现有名册:
      查询实际股东数量
      如果是01名册，更新公司统计信息
      更新名册记录数为实际合并后的数量
    
    返回名册记录
  }
  
  // 13. 构建响应数据
  格式化日期和响应数据
  返回成功响应
  
异常处理:
  捕获异常，记录日志，返回错误响应
```

### 4.2 字段更新处理函数

需要在`packages/api/src/routes/shareholder-registry/lib/utils.ts`文件中实现字段更新处理函数：

```
函数 处理股东字段更新(更新数据, 现有股东, 新股东数据, 名册类型):
  变更标记 = false
  
  // 处理所有可能的字段
  字段列表 = [
    // 共有字段
    "证券账户名称", "股东类别", "持股数量", "限售股数", "持股比例", 
    "冻结股数", "现金账户", "现金账户持股", "融资账户", "融资账户持股",
    "联系地址", "联系电话", "邮编", "备注",
    
    // 01名册特有字段
    "关联方标识", "客户类别",
    
    // 05名册特有字段
    "汇总账户号码", "汇总账户名称", "股份性质"
  ]
  
  // 遍历所有需要处理的字段
  对于每个字段:
    // 如果新数据中没有该字段，跳过
    如果 新股东数据[字段] 不存在:
      继续下一个字段
    
    // 如果现有记录中该字段为空且新数据有值，则更新
    如果 (现有股东[字段]为空 或 未定义 或 为空字符串) 且 新股东数据[字段]有值:
      更新数据[字段] = 新股东数据[字段]
      变更标记 = true
  
  返回 变更标记
```

### 4.3 验证器修改

需要修改`packages/api/src/routes/shareholder-registry/lib/validators.ts`文件中的`UploadRegistrySchema`，增加对05名册特有字段的验证：

```
股东数组: 数组(
  对象({
    // 新增字段（可选）
    汇总账户号码: 字符串(可选),
    汇总账户名称: 字符串(可选),
    股份性质: 字符串(可选),
  })
)
```

## 5. 实施步骤

### 5.1 数据库模型更新

1. 修改`packages/database/prisma/schema.prisma`文件，增加新字段
2. 执行数据库迁移命令，更新数据库结构
3. 更新生成的Prisma客户端

### 5.2 接口逻辑改造

1. 修改验证器和类型定义，支持新字段
2. 改造上传接口，实现合并逻辑
3. 修改相关查询接口，确保能正确返回新增字段

### 5.3 测试验证

1. 测试单独上传01名册或05名册的场景（浏览器测试）
2. 测试先上传01名册后上传05名册的合并场景 （浏览器测试）
3. 测试先上传05名册后上传01名册的合并场景 （浏览器测试）
4. 验证字段合并逻辑是否正确 （浏览器测试）
5. 验证查询接口是否能正确返回合并后的数据 （浏览器测试）


## 6. 合并流程图

```
graph TD
    A([开始: 接收待合并的名册文件数据]) --> B[解析名册文件的信息和字段];

    B --> C{数据库中是否存在<br/>与名册对应的“公司记录”?<br/>通过公司代码};
    C -- 否 (公司不存在) --> D([录入新公司到数据库 参考01/05名册字段数据库对应]);
 
    C -- 是 (公司已存在) --> H{该公司记录下是否存在<br/>与名册相同的“期数记录”?};

    H -- 否 (该期数不存在) --> I([录入新期数到数据库 参考01/05名册字段数据库对应]);
    H -- 是 (该期数已存在) --> J{开始遍历名册文件中的每个股东的证件号码};

    J --> K[获取当前股东的shareholderId,shareholderCategory + unifiedAccountNumber];
    K --> L{当前股东shareholderCategory是否属于个人 （是否存在“自然人”字符）?};
    L -- 是 --> Personal{在该公司当前期数的数据库中<br/>通过shareholderId查找是否存在该股东?}
    Personal -- 否 --> M([在该期数下创建新“股东记录”]);
  
    Personal -- 是 (股东已存在于该期数) --> O[数据库中逐字段筛查该股东的当期字段];

    O -->  Q{数据库中该股东的当前字段<br/>是否在当前待合并的名册中有对应字段？};
    Q -- 否 --> R([跳过，循环到下一个数据库字段])
    Q -- 是 --> S{数据库字段中是否有内容？}
    S -- 否 --> T([填入名册文件对应字段的内容])
    S -- 是 --> U([跳过，循环到下一个数据库字段])


    L -- 否,为机构 --> Institution{在该公司当前期数的数据库中<br/>通过shareholderId 和 unifiedAccountNumber的组合查找是否存在该股东?}
    Institution -- 否 --> iM([在该期数下创建新“股东记录”]);
  
    Institution --是 (股东已存在于该期数) --> iO[数据库中逐字段筛查该股东的当期字段];

    iO -->  iQ{数据库中该股东的当前字段<br/>是否在当前待合并的名册中有对应字段？};
    iQ -- 否 --> iR([跳过，循环到下一个数据库字段])
    iQ -- 是 --> iS{数据库字段中是否有内容？}
    iS -- 否 --> iT([填入名册文件对应字段的内容])
    iS -- 是 --> iU([跳过，循环到下一个数据库字段])
```