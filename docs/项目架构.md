# 🌟 Supastarter 超详细目录架构

## 1. 顶层目录结构

```
/
├── .turbo/                # Turborepo 缓存和配置
├── .git/                  # Git 版本控制
├── config/                # 项目配置
├── packages/              # 共享包和服务
├── docs/                  # 项目文档
├── node_modules/          # Node.js 依赖
├── tooling/               # 工具和配置
├── apps/                  # 应用程序
├── .vscode/               # VS Code 配置
├── .github/               # GitHub 相关配置
├── package.json           # 项目依赖和脚本
├── turbo.json             # Turborepo 配置
├── pnpm-workspace.yaml    # PNPM 工作区配置
├── tsconfig.json          # TypeScript 配置
├── biome.json             # Biome 代码格式化配置
├── .windsurfrules         # WindSurf 规则配置
├── .cursorrules           # Cursor 编辑器规则
├── .editorconfig          # 编辑器配置
├── .npmrc                 # NPM 配置
└── .gitignore             # Git 忽略文件列表
```

## 2. Web 应用详细结构 (apps/web/)

```
apps/web/
├── .next/                     # Next.js 构建输出
├── node_modules/              # 应用级依赖
├── .content-collections/      # 内容集合缓存
├── app/                       # Next.js App Router
│   ├── api/                   # API 路由
│   ├── image-proxy/           # 图片代理服务
│   ├── (marketing)/           # 营销分组
│   │   └── [locale]/          # 多语言路由
│   │       ├── layout.tsx     # 营销布局组件
│   │       ├── not-found.tsx  # 404页面
│   │       ├── (home)/        # 首页分组
│   │       ├── [...rest]/     # 通配符路由
│   │       ├── blog/          # 博客页面
│   │       ├── changelog/     # 更新日志
│   │       ├── contact/       # 联系页面
│   │       ├── docs/          # 文档页面
│   │       └── legal/         # 法律页面
│   ├── (saas)/                # SaaS应用分组
│   │   ├── layout.tsx         # SaaS主布局
│   │   ├── auth/              # 认证页面
│   │   └── app/               # 应用内页面
│   │       ├── layout.tsx     # 应用布局
│   │       ├── not-found.tsx  # 应用内404页面
│   │       ├── choose-plan/   # 选择套餐页面
│   │       ├── onboarding/    # 用户引导流程
│   │       ├── (organizations)/ # 组织相关
│   │       ├── [...rest]/     # 应用内通配符路由
│   │       └── (account)/     # 账户相关页面
│   ├── favicon.ico            # 网站图标
│   ├── icon.png               # 应用图标
│   ├── layout.tsx             # 根布局组件
│   ├── not-found.tsx          # 全局404页面
│   ├── robots.ts              # 搜索引擎爬虫配置
│   ├── sitemap.ts             # 网站地图
│   ├── docs-source.ts         # 文档源配置
│   └── globals.css            # 全局样式
├── public/                    # 静态资源
├── content/                   # MDX内容文件
├── tests/                     # 测试文件
├── middleware.ts              # Next.js中间件
├── next.config.ts             # Next.js配置
├── next-env.d.ts              # Next.js类型声明
├── package.json               # 应用依赖
├── tsconfig.json              # TypeScript配置
├── playwright.config.ts       # Playwright测试配置
├── postcss.config.cjs         # PostCSS配置
├── global.d.ts                # 全局类型声明
├── biome.json                 # Biome配置
├── components.json            # 组件配置
├── content-collections.ts     # 内容集合配置
└── .gitignore                 # Git忽略文件
```

## 3. 模块详细结构 (apps/web/modules/)

### 3.1 认证模块 (apps/web/modules/saas/auth/)

```
auth/
├── components/                # 认证组件
│   ├── LoginForm.tsx          # 登录表单 (348行)
│   ├── SignupForm.tsx         # 注册表单 (277行)
│   ├── ForgotPasswordForm.tsx # 忘记密码表单 (135行)
│   ├── ResetPasswordForm.tsx  # 重置密码表单 (136行)
│   ├── SessionProvider.tsx    # 会话提供者 (52行)
│   ├── SocialSigninButton.tsx # 社交登录按钮 (48行)
│   └── LoginModeSwitch.tsx    # 登录模式切换 (29行)
├── actions/                   # 认证服务器操作
│   ├── login.ts               # 登录操作
│   ├── signup.ts              # 注册操作
│   ├── forgot-password.ts     # 忘记密码
│   ├── reset-password.ts      # 重置密码
│   └── social-auth.ts         # 社交认证
└── forms/                     # 认证表单逻辑和验证
    ├── login-schema.ts        # 登录表单验证
    ├── signup-schema.ts       # 注册表单验证
    └── password-schema.ts     # 密码表单验证
```

### 3.2 组织管理模块 (apps/web/modules/saas/organizations/)

```
organizations/
├── components/                       # 组织相关组件
│   ├── ActiveOrganizationProvider.tsx # 活动组织提供者 (213行)
│   ├── OrganizationSelect.tsx        # 组织选择器 (208行)
│   ├── OrganizationMembersList.tsx   # 组织成员列表 (274行)
│   ├── OrganizationInvitationsList.tsx # 组织邀请列表 (233行)
│   ├── InviteMemberForm.tsx          # 邀请成员表单 (145行)
│   ├── OrganizationLogoForm.tsx      # 组织logo表单 (119行)
│   ├── CreateOrganizationForm.tsx    # 创建组织表单 (111行)
│   ├── OrganizationInvitationModal.tsx # 组织邀请模态框 (106行)
│   ├── ChangeOrganizationNameForm.tsx # 修改组织名表单 (94行)
│   ├── OrganizationStart.tsx         # 组织首页 (40行)
│   ├── DeleteOrganizationForm.tsx    # 删除组织表单 (76行)
│   ├── OrganizationRoleSelect.tsx    # 组织角色选择 (59行)
│   ├── OrganizationLogo.tsx          # 组织Logo (62行)
│   ├── OrganizationsGrid.tsx         # 组织网格 (60行)
│   ├── OrganizationMembersBlock.tsx  # 组织成员块 (43行)
│   ├── OrganizationInvitationAlert.tsx # 组织邀请提醒 (19行)
│   └── ... 其他组织相关组件
├── actions/                         # 组织相关服务器操作
│   ├── create-organization.ts       # 创建组织
│   ├── delete-organization.ts       # 删除组织
│   ├── update-organization.ts       # 更新组织
│   ├── invite-member.ts             # 邀请成员
│   ├── remove-member.ts             # 移除成员
│   ├── accept-invitation.ts         # 接受邀请
│   └── reject-invitation.ts         # 拒绝邀请
└── hooks/                           # 组织相关钩子
    ├── useOrganization.ts           # 使用组织钩子
    ├── useOrganizationMembers.ts    # 使用组织成员钩子
    └── useOrganizationInvitations.ts # 使用组织邀请钩子
```

### 3.3 设置模块 (apps/web/modules/saas/settings/)

```
settings/
├── components/                  # 设置页面组件
│   ├── ChangePasswordForm.tsx   # 修改密码表单 (135行)
│   ├── PasskeysBlock.tsx        # 密钥管理块 (122行)
│   ├── DeleteAccountForm.tsx    # 删除账户表单 (104行)
│   ├── ActiveSessionsBlock.tsx  # 活跃会话块 (104行)
│   ├── ConnectedAccountsBlock.tsx # 关联账户块 (96行)
│   ├── ChangeEmailForm.tsx      # 修改邮箱表单 (89行)
│   ├── ChangeNameForm.tsx       # 修改姓名表单 (84行)
│   ├── UserLanguageForm.tsx     # 用户语言表单 (82行)
│   ├── CropImageDialog.tsx      # 裁剪图片对话框 (80行)
│   ├── SetPassword.tsx          # 设置密码 (68行)
│   ├── SettingsMenu.tsx         # 设置菜单 (62行)
│   ├── UserAvatarUpload.tsx     # 用户头像上传 (108行)
│   ├── CustomerPortalButton.tsx # 客户门户按钮 (43行)
│   ├── SubscriptionStatusBadge.tsx # 订阅状态徽章 (39行)
│   └── UserAvatarForm.tsx       # 用户头像表单 (30行)
├── actions/                     # 设置相关操作
│   ├── update-profile.ts        # 更新配置文件
│   ├── change-password.ts       # 修改密码
│   ├── delete-account.ts        # 删除账户
│   ├── passkeys.ts              # 密钥管理
│   └── sessions.ts              # 会话管理
└── forms/                       # 设置表单
    ├── profile-schema.ts        # 个人资料验证
    ├── password-schema.ts       # 密码验证
    └── account-schema.ts        # 账户验证
```

## 4. 共享功能包详细结构 (packages/)

### 4.1 认证包 (packages/auth/)

```
auth/
├── auth.ts                      # 主认证逻辑 (461行)
├── index.ts                     # 导出文件
├── client.ts                    # 客户端工具 (24行)
├── lib/                         # 认证库
│   ├── helper.ts                # 通用辅助函数 (19行) 
│   ├── organization.ts          # 组织相关功能 (60行)
│   └── user.ts                  # 用户相关功能 (12行)
├── plugins/                     # 认证插件
│   ├── google.ts                # Google认证
│   ├── github.ts                # GitHub认证
│   ├── email.ts                 # 邮箱认证
│   └── credentials.ts           # 凭证认证
├── package.json                 # 包配置
├── tsconfig.json                # TypeScript配置
└── biome.json                   # Biome配置
```

### 4.2 数据库包 (packages/database/)

```
database/
├── prisma/                       # Prisma配置
│   ├── schema.prisma             # 数据库模式定义 (198行)
│   └── migrations/               # 数据库迁移文件
├── src/                          # 源代码
│   ├── client.ts                 # Prisma客户端导出 (19行)
│   └── zod/                      # Zod验证模式
│       ├── user.ts               # 用户验证模式
│       ├── organization.ts       # 组织验证模式
│       └── subscription.ts       # 订阅验证模式
├── index.ts                      # 主导出文件 (13行)
├── package.json                  # 包配置
├── tsconfig.json                 # TypeScript配置
├── biome.json                    # Biome配置
└── .eslintignore                 # ESLint忽略配置
```

### 4.3 邮件包 (packages/mail/)

```
mail/
├── src/                          # 源代码
│   ├── provider/                 # 邮件提供商
│   │   ├── index.ts              # 提供商导出 (3行)
│   │   ├── resend.ts             # Resend实现 (27行)
│   │   ├── postmark.ts           # Postmark实现 (30行)
│   │   ├── plunk.ts              # Plunk实现 (25行)
│   │   ├── nodemailer.ts         # Nodemailer实现 (25行)
│   │   ├── mailgun.ts            # Mailgun实现 (43行)
│   │   ├── tencent-ses.ts        # 腾讯SES实现 (45行)
│   │   ├── console.ts            # 控制台输出实现 (11行)
│   │   └── custom.ts             # 自定义实现 (10行)
│   ├── components/               # 邮件组件
│   │   ├── Button.tsx            # 按钮组件
│   │   ├── Container.tsx         # 容器组件
│   │   ├── Footer.tsx            # 页脚组件
│   │   ├── Header.tsx            # 页眉组件
│   │   └── Text.tsx              # 文本组件
│   └── util/                     # 工具函数
│       ├── render.ts             # 渲染邮件
│       └── send.ts               # 发送邮件
├── emails/                       # 邮件模板
│   ├── index.ts                  # 导出所有模板 (16行)
│   ├── MagicLink.tsx             # 魔法链接邮件 (47行)
│   ├── EmailVerification.tsx     # 邮箱验证邮件 (49行)
│   ├── ForgotPassword.tsx        # 忘记密码邮件 (47行)
│   ├── NewUser.tsx               # 新用户邮件 (59行)
│   ├── OrganizationInvitation.tsx # 组织邀请邮件 (56行)
│   └── NewsletterSignup.tsx      # 订阅确认邮件 (31行)
├── index.ts                      # 主导出文件
├── types.ts                      # 类型定义 (20行)
├── global.d.ts                   # 全局声明
├── package.json                  # 包配置
├── tsconfig.json                 # TypeScript配置
├── biome.json                    # Biome配置
└── .gitignore                    # Git忽略文件
```

### 4.4 API包 (packages/api/)

```
api/
├── src/                          # 源代码
│   ├── app.ts                    # 应用程序配置 (82行)
│   ├── routes/                   # API路由
│   │   ├── ai.ts                 # AI相关接口 (291行)
│   │   ├── auth.ts               # 认证接口 (11行)
│   │   ├── uploads.ts            # 上传接口 (51行)
│   │   ├── webhooks.ts           # Webhook接口 (15行)
│   │   ├── health.ts             # 健康检查 (14行)
│   │   ├── newsletter.ts         # 新闻通讯 (55行)
│   │   ├── admin/                # 管理员接口
│   │   │   ├── router.ts         # 管理员路由
│   │   │   └── auth.ts           # 管理员认证
│   │   ├── organizations/        # 组织接口
│   │   │   ├── router.ts         # 组织路由 (78行)
│   │   │   └── lib/              # 组织库
│   │   │       ├── create.ts     # 创建组织
│   │   │       ├── delete.ts     # 删除组织
│   │   │       ├── members.ts    # 成员管理
│   │   │       └── invitations.ts # 邀请管理
│   │   ├── payments/             # 支付接口
│   │   │   ├── router.ts         # 支付路由
│   │   │   ├── subscriptions.ts  # 订阅管理
│   │   │   └── webhooks/         # 支付Webhook
│   │   │       ├── stripe.ts     # Stripe Webhook
│   │   │       ├── lemonsqueezy.ts # LemonSqueezy Webhook
│   │   │       └── chargebee.ts  # Chargebee Webhook
│   │   └── contact/              # 联系表单
│   │       ├── router.ts         # 联系路由
│   │       └── handler.ts        # 联系处理
│   ├── middleware/               # 中间件
│   │   ├── auth.ts               # 认证中间件
│   │   ├── cors.ts               # CORS中间件
│   │   └── rate-limit.ts         # 速率限制
│   └── lib/                      # 工具库
│       ├── error-handler.ts      # 错误处理
│       ├── response.ts           # 响应处理
│       └── validator.ts          # 请求验证
├── index.ts                      # 主导出文件
├── package.json                  # 包配置
├── tsconfig.json                 # TypeScript配置
└── biome.json                    # Biome配置
```

## 5. 数据库模式分析 (packages/database/prisma/schema.prisma)

Prisma 数据库模式定义了以下核心实体模型：

```prisma
// 用户模型
model User {
  id                 String              @id @default(cuid())
  name               String?
  email              String?             @unique
  emailVerified      DateTime?
  image              String?
  password           String?
  language           String              @default("en")
  role               Role                @default(USER)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  accounts           Account[]
  sessions           Session[]
  // 组织关系
  organizations      OrganizationMember[]
  invitations        OrganizationInvitation[]
  // 订阅关系
  subscriptions      Subscription[]
}

// 组织模型
model Organization {
  id                 String              @id @default(cuid())
  name               String
  slug               String              @unique
  image              String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  // 成员和邀请
  members            OrganizationMember[]
  invitations        OrganizationInvitation[]
  // 订阅
  subscription       Subscription?
}

// 组织成员模型
model OrganizationMember {
  userId             String
  organizationId     String
  role               OrganizationRole    @default(MEMBER)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization       Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@id([userId, organizationId])
}

// 订阅模型
model Subscription {
  id                 String              @id @default(cuid())
  userId             String?
  organizationId     String?             @unique
  status             SubscriptionStatus  @default(ACTIVE)
  planId             String
  priceId            String?
  interval           SubscriptionInterval?
  currentPeriodEnd   DateTime?
  cancelAtPeriodEnd  Boolean             @default(false)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  // 关系
  user               User?               @relation(fields: [userId], references: [id], onDelete: SetNull)
  organization       Organization?       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

// 其他模型: Account, Session, VerificationToken, OrganizationInvitation
```

## 6. 应用流程分析

### 6.1 用户认证流程

1. **注册流程**:
   - 通过 `SignupForm.tsx` (277行) 提供注册表单
   - 表单数据通过 `signup.ts` 操作提交
   - 创建用户记录并发送验证邮件 (`EmailVerification.tsx`)
   - 成功后重定向到登录或直接登录

2. **登录流程**:
   - 通过 `LoginForm.tsx` (348行) 提供登录表单
   - 支持多种登录方式 (邮箱/密码, 魔法链接, 社交登录)
   - 使用 `SocialSigninButton.tsx` 处理社交登录
   - 成功后使用 `SessionProvider.tsx` 管理会话

3. **密码重置**:
   - 通过 `ForgotPasswordForm.tsx` (135行) 提供忘记密码表单
   - 发送重置邮件 (`ForgotPassword.tsx`)
   - 通过 `ResetPasswordForm.tsx` (136行) 重置密码

### 6.2 组织管理流程

1. **组织创建**:
   - 通过 `CreateOrganizationForm.tsx` (111行) 创建组织
   - 自动将创建者设为管理员
   - 提供组织Logo上传功能 (`OrganizationLogoForm.tsx`)

2. **成员管理**:
   - 通过 `OrganizationMembersList.tsx` (274行) 显示成员
   - 使用 `InviteMemberForm.tsx` (145行) 邀请新成员
   - 邀请通过邮件 (`OrganizationInvitation.tsx`) 发送
   - 邀请列表通过 `OrganizationInvitationsList.tsx` (233行) 管理

3. **组织设置**:
   - 提供名称修改功能 (`ChangeOrganizationNameForm.tsx`)
   - 支持删除组织 (`DeleteOrganizationForm.tsx`)
   - 角色管理 (`OrganizationRoleSelect.tsx`)

### 6.3 用户设置流程

1. **个人资料**:
   - 修改姓名 (`ChangeNameForm.tsx`)
   - 修改邮箱 (`ChangeEmailForm.tsx`)
   - 上传头像 (`UserAvatarUpload.tsx`, `UserAvatarForm.tsx`)

2. **账户安全**:
   - 密码管理 (`ChangePassword.tsx`, `SetPassword.tsx`)
   - 密钥管理 (`PasskeysBlock.tsx`)
   - 会话管理 (`ActiveSessionsBlock.tsx`)
   - 社交登录连接 (`ConnectedAccountsBlock.tsx`)
   - 删除账户 (`DeleteAccountForm.tsx`)

3. **语言设置**:
   - 修改界面语言 (`UserLanguageForm.tsx`)

## 7. 技术亮点与设计原则

1. **模块化设计**:
   - 明确的功能分组与职责分离
   - 共享包结构便于代码复用和维护

2. **类型安全**:
   - 严格的 TypeScript 类型定义
   - Zod 验证模式确保数据一致性

3. **多提供商支持**:
   - 邮件: 支持9种邮件提供商
   - 认证: 支持多种认证方式
   - 支付: 集成多种支付系统
   - 存储: 支持多种存储服务

4. **安全最佳实践**:
   - 会话管理与监控
   - 密码哈希与安全存储
   - 多因素认证支持

5. **国际化支持**:
   - 内置多语言路由
   - 国际化的邮件模板
   - 用户语言偏好设置

6. **UI组件库**:
   - 基于 Shadcn UI 和 Radix UI
   - 高度可定制的组件
   - 响应式设计
