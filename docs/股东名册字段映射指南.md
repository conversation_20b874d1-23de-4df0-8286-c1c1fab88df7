# 股东名册字段映射指南

**文档日期:** 2025-06-03
**版本:** 1.0

## 1. 概述

本文档提供股东名册上传系统的字段映射指南，主要面向前端开发人员。系统已实现了一套完整的字段映射机制，用于处理不同类型名册（01、05、t1、t2、t3）的字段差异，确保数据正确映射到统一的数据库模型中。

## 2. 字段映射机制说明

后端实现了一套完整的字段映射机制，可以将不同类型名册的原始字段名自动映射到数据库字段。这意味着前端上传数据时应使用**原始字段名**（如YMTH、ZQZHMC等），而不是数据库字段名（如unifiedAccountNumber、securitiesAccountName等）。

系统会根据名册类型自动进行以下处理：
1. 验证上传的数据是否包含必要字段
2. 验证字段特征是否与声明的名册类型匹配
3. 将原始字段名映射为数据库字段名
4. 处理特殊字段的合并逻辑（如持股数量合并）

## 3. 前端上传数据要求

### 3.1 文件名要求

上传的文件名必须包含明确的名册类型标识：
- 深市01名册：文件名中必须包含 `DQMC01`
- 深市05名册：文件名中必须包含 `DQMC05`
- 沪市t1名册：文件名中必须包含 `t1`
- 沪市t2名册：文件名中必须包含 `t2`
- 沪市t3名册：文件名中必须包含 `t3`

### 3.2 必要字段要求

每种名册类型必须包含以下必要字段：

#### 深市01名册必要字段
```json
{
  "YMTH": "一码通账户号码",
  "ZQZHMC": "证券账户名称",
  "ZJDM": "证件号码",
  "CYRLBMS": "持有人类别",
  "CGSL": "总持股数量",
  "XSGSL": "限售股数量",
  "CGBL": "持股比例"
}
```

#### 深市05名册必要字段
```json
{
  "YMTH": "一码通账户号码",
  "XYZHMC": "证券账户名称",
  "XYZHZJDM": "证件号码",
  "CYRLBMS": "持有人类别",
  "CGSL": "信用账户持股数量",
  "HZZQZH": "汇总账户号码",
  "HZZHMC": "汇总账户名称"
}
```

#### 沪市t1名册必要字段
```json
{
  "YMTZHHM": "一码通账户号码",
  "ZQZHMC": "证券账户名称",
  "ZJHM": "证件号码",
  "GDLB": "持有人类别",
  "LTLX": "流通类型",
  "YQLB": "权益类别"
}
```

#### 沪市t2名册必要字段
```json
{
  "YMTZHHM": "一码通账户号码",
  "ZQZHMC": "证券账户名称",
  "ZJHM": "证件号码",
  "GDLB": "持有人类别",
  "TZRMC": "投资者名称",
  "GDMC": "股东名称"
}
```

#### 沪市t3名册必要字段
```json
{
  "YMTZHHM": "一码通账户号码",
  "ZQZHMC": "证券账户名称",
  "ZJHM": "证件号码",
  "GDLB": "持有人类别",
  "ZCYSL": "总持股数量",
  "PTZQZHCYSL": "普通账户持股数量",
  "XYCYSL": "信用账户持股数量"
}
```

## 4. 完整字段映射参考

以下是各类型名册的完整字段映射关系，供前端开发参考：

### 4.1 深市01名册字段映射

| 原始字段名 | 数据库字段名 | 字段说明 |
|------------|--------------|----------|
| YMTH | unifiedAccountNumber | 一码通账户号码 |
| ZQZHMC | securitiesAccountName | 证券账户名称 |
| ZJDM | shareholderId | 证件号码 |
| CYRLBMS | shareholderCategory | 持有人类别 |
| CGSL | numberOfShares | 总持股数量 |
| XSGSL | lockedUpShares | 限售股数量 |
| CGBL | shareholdingRatio | 持股比例 |
| DJGS | frozenShares | 冻结股数 |
| PTZQZH | cashAccount | 普通证券账户 |
| PTZHCGSL | sharesInCashAccount | 普通账户持股数量 |
| XYZQZH | marginAccount | 信用证券账户 |
| XYZHCGSL | sharesInMarginAccount | 信用账户持股数量 |
| TXDZ | contactAddress | 通讯地址 |
| DHHM | contactNumber | 联系电话 |
| YZBM | zipCode | 邮政编码 |
| GLGXBS | relatedPartyIndicator | 关联关系确认标识 |
| KHLB | clientCategory | 客户类别 |
| BZ | remarks | 备注 |

### 4.2 深市05名册字段映射

| 原始字段名 | 数据库字段名 | 字段说明 |
|------------|--------------|----------|
| YMTH | unifiedAccountNumber | 一码通账户号码 |
| XYZHMC | securitiesAccountName | 证券账户名称 |
| XYZHZJDM | shareholderId | 证件号码 |
| CYRLBMS | shareholderCategory | 持有人类别 |
| CGSL | sharesInMarginAccount | 信用账户持股数量 |
| DJGS | frozenShares | 冻结股数 |
| XYZQZH | marginAccount | 信用证券账户 |
| TXDZ | contactAddress | 通讯地址 |
| HZZQZH | marginCollateralAccountNumber | 汇总账户号码 |
| HZZHMC | marginCollateralAccountName | 汇总账户名称 |
| GFXZ | natureOfShares | 股份性质 |

### 4.3 沪市t1名册字段映射

| 原始字段名 | 数据库字段名 | 字段说明 |
|------------|--------------|----------|
| YMTZHHM | unifiedAccountNumber | 一码通账户号码 |
| ZQZHMC | securitiesAccountName | 证券账户名称 |
| ZJHM | shareholderId | 证件号码 |
| GDLB | shareholderCategory | 持有人类别 |
| CYSL | sharesInCashAccount | 普通账户持股数量 |
| TXDZ | contactAddress | 通讯地址 |
| LXDH | contactNumber | 联系电话 |
| YZBM | zipCode | 邮政编码 |
| BZ | remarks | 备注 |
| LTLX | shareTradingCategory | 流通类型 |
| YQLB | rightsCategory | 权益类别 |
| TZRMC | investorName | 投资者名称 |
| GDMC | shareholderName | 股东名称 |
| ZJLX | certificateType | 证件类型 |
| GDZL | shareholderNature | 股东种类 |
| ZHZT | accountStatus | 账户状态 |

### 4.4 沪市t2名册字段映射

| 原始字段名 | 数据库字段名 | 字段说明 |
|------------|--------------|----------|
| YMTZHHM | unifiedAccountNumber | 一码通账户号码 |
| ZQZHMC | securitiesAccountName | 证券账户名称 |
| ZJHM | shareholderId | 证件号码 |
| GDLB | shareholderCategory | 持有人类别 |
| CYSL | sharesInCashAccount | 普通账户持股数量 |
| TXDZ | contactAddress | 通讯地址 |
| LXDH | contactNumber | 联系电话 |
| YZBM | zipCode | 邮政编码 |
| BZ | remarks | 备注 |
| TZRMC | investorName | 投资者名称 |
| GDMC | shareholderName | 股东名称 |
| ZJLX | certificateType | 证件类型 |
| GDZL | shareholderNature | 股东种类 |
| ZHZT | accountStatus | 账户状态 |

### 4.5 沪市t3名册字段映射

| 原始字段名 | 数据库字段名 | 字段说明 |
|------------|--------------|----------|
| YMTZHHM | unifiedAccountNumber | 一码通账户号码 |
| ZQZHMC | securitiesAccountName | 证券账户名称 |
| ZJHM | shareholderId | 证件号码 |
| GDLB | shareholderCategory | 持有人类别 |
| ZCYSL | numberOfShares | 总持股数量 |
| PTZQZHCYSL | sharesInCashAccount | 普通账户持股数量 |
| XYCYSL | sharesInMarginAccount | 信用账户持股数量 |
| PTZQZH | cashAccount | 普通证券账户 |
| XYZQZH | marginAccount | 信用证券账户 |
| TXDZ | contactAddress | 通讯地址 |
| LXDH | contactNumber | 联系电话 |
| YZBM | zipCode | 邮政编码 |
| BZ | remarks | 备注 |
| TZRMC | investorName | 投资者名称 |
| GDMC | shareholderName | 股东名称 |
| ZJLX | certificateType | 证件类型 |
| GDZL | shareholderNature | 股东种类 |
| ZHZT | accountStatus | 账户状态 |

## 5. 前端数据结构示例

以下是一个符合要求的深市01名册上传数据结构示例：

```typescript
const uploadData = {
  organizationId: "org_123456",
  fileName: "公司A-DQMC01-********.dbf",
  recordCount: 2,
  registerDate: "2025-01-01",
  companyCode: "000001",
  companyInfo: {
    companyName: "公司A",
    totalShares: "*********",
    totalShareholders: 5000,
    totalInstitutions: 200,
    largeSharesCount: "********",
    institutionShares: "********",
    largeShareholdersCount: 50
  },
  shareholders: [
    {
      // 使用原始字段名，而不是数据库字段名
      "YMTH": "**********",
      "ZQZHMC": "张三",
      "ZJDM": "110101199001011234",
      "CYRLBMS": "境内自然人(03)",
      "CGSL": "10000",
      "XSGSL": "0",
      "CGBL": "0.01",
      "DJGS": "0",
      "PTZQZH": "A123456789",
      "PTZHCGSL": "10000",
      "XYZQZH": "",
      "XYZHCGSL": "0",
      "TXDZ": "北京市朝阳区",
      "DHHM": "13800138000",
      "YZBM": "100000",
      "GLGXBS": "0",
      "KHLB": "个人",
      "BZ": ""
    },
    {
      "YMTH": "0000067890",
      "ZQZHMC": "李四",
      "ZJDM": "310101199002022345",
      "CYRLBMS": "境内自然人(03)",
      "CGSL": "20000",
      "XSGSL": "0",
      "CGBL": "0.02",
      "DJGS": "0",
      "PTZQZH": "A987654321",
      "PTZHCGSL": "20000",
      "XYZQZH": "",
      "XYZHCGSL": "0",
      "TXDZ": "上海市浦东新区",
      "DHHM": "13900139000",
      "YZBM": "200000",
      "GLGXBS": "0",
      "KHLB": "个人",
      "BZ": ""
    }
  ]
};
```

## 6. 字段验证和错误处理

后端系统会对上传的数据进行严格验证，包括：

1. **名册类型验证**：通过文件名和字段特征双重验证名册类型
2. **必要字段验证**：检查是否包含该类型名册的所有必要字段
3. **字段特征验证**：检查字段特征是否与声明的名册类型匹配

如果验证失败，系统会返回详细的错误信息，如：
- "上传的股东数据缺少01类型名册必要字段: YMTH, ZQZHMC"
- "名册类型不匹配：文件名表明是01类型，但字段特征表明是05类型"

## 7. 合并处理说明

系统支持深市01/05名册和沪市t1/t2/t3名册的合并处理：

### 7.1 深市01/05名册合并

当上传深市01名册后，可以上传同一公司同一日期的05名册进行合并，反之亦然。合并时：
- 01名册特有字段（如限售股数量、持股比例）保留01名册的值
- 05名册特有字段（如汇总账户号码、股份性质）保留05名册的值
- 共有字段按"先到先得"原则保留首次出现的值

### 7.2 沪市t1/t2/t3名册合并

当上传t1、t2或t3名册中的一种后，可以上传同一公司同一日期的其他类型名册进行合并。合并时：
- 持股数量相关字段（普通账户持股数量、信用账户持股数量、总持股数量）进行叠加
- 其他字段按"先到先得"原则保留首次出现的值

## 8. 注意事项

1. **字段名大小写敏感**：请确保使用正确的大写原始字段名
2. **数值字段格式**：持股数量等数值字段可以是字符串形式，后端会自动转换
3. **空值处理**：非必要字段可以为空字符串或不提供
4. **日期格式**：registerDate必须使用"YYYY-MM-DD"格式
5. **文件名格式**：必须包含对应的名册类型标识

## 9. 常见问题

### 9.1 为什么要使用原始字段名而不是数据库字段名？

使用原始字段名可以保持与源数据格式的一致性，减少前端的转换工作，同时也便于后端进行字段特征检测和类型验证。

### 9.2 如何处理可选字段？

非必要字段可以不提供或提供空值。后端会为数值类型字段设置默认值（通常为"0"）。

### 9.3 如何确保上传的数据类型正确？

1. 确保文件名中包含正确的名册类型标识
2. 确保提供该类型名册的所有必要字段
3. 确保字段特征与名册类型匹配（例如，01名册应包含限售股数量和持股比例等特有字段）

### 9.4 合并名册时有什么限制？

1. 必须是同一公司同一日期的名册
2. 不能重复上传同类型的名册
3. 完成全部合并后（01+05或t1+t2+t3）不能继续上传 